<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export des données - <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        .success {
            background-color: #10b981;
        }
        .success:hover {
            background-color: #059669;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .info {
            background-color: #dbeafe;
            border: 1px solid #93c5fd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #fbbf24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦷 Export des données du Cabinet</h1>
        
        <div class="info">
            <strong>📋 Instructions :</strong><br>
            1. Cliquez sur "Exporter les données"<br>
            2. Copiez le contenu qui s'affiche<br>
            3. Sauvegardez-le dans un fichier texte<br>
            4. Utilisez ces données pour migrer vers la nouvelle version
        </div>

        <div class="section">
            <h3>📊 Données actuelles</h3>
            <p id="dataInfo">Chargement...</p>
            <button onclick="exportData()">📤 Exporter les données</button>
            <button onclick="clearData()" style="background-color: #dc2626;">🗑️ Vider les données (ATTENTION !)</button>
        </div>

        <div class="section" id="exportSection" style="display: none;">
            <h3>📋 Données exportées</h3>
            <div class="warning">
                <strong>⚠️ Important :</strong> Copiez ces données et sauvegardez-les avant de fermer cette page !
            </div>
            <textarea id="exportData" readonly></textarea>
            <button onclick="copyToClipboard()" class="success">📋 Copier dans le presse-papier</button>
            <button onclick="downloadData()" class="success">💾 Télécharger le fichier</button>
        </div>
    </div>

    <script>
        // Charger les informations au démarrage
        window.onload = function() {
            loadDataInfo();
        };

        function loadDataInfo() {
            const patients = JSON.parse(localStorage.getItem('cabinet_patients') || '[]');
            const consultations = JSON.parse(localStorage.getItem('cabinet_consultations') || '[]');
            const payments = JSON.parse(localStorage.getItem('cabinet_payments') || '[]');
            const appointments = JSON.parse(localStorage.getItem('cabinet_appointments') || '[]');

            document.getElementById('dataInfo').innerHTML = `
                <strong>Données trouvées :</strong><br>
                👥 Patients : ${patients.length}<br>
                📅 Rendez-vous : ${appointments.length}<br>
                🏥 Consultations : ${consultations.length}<br>
                💰 Paiements : ${payments.length}
            `;
        }

        function exportData() {
            const data = {
                patients: JSON.parse(localStorage.getItem('cabinet_patients') || '[]'),
                consultations: JSON.parse(localStorage.getItem('cabinet_consultations') || '[]'),
                payments: JSON.parse(localStorage.getItem('cabinet_payments') || '[]'),
                appointments: JSON.parse(localStorage.getItem('cabinet_appointments') || '[]'),
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const jsonString = JSON.stringify(data, null, 2);
            document.getElementById('exportData').value = jsonString;
            document.getElementById('exportSection').style.display = 'block';
            
            console.log('Données exportées :', data);
        }

        function copyToClipboard() {
            const textarea = document.getElementById('exportData');
            textarea.select();
            document.execCommand('copy');
            alert('✅ Données copiées dans le presse-papier !');
        }

        function downloadData() {
            const data = document.getElementById('exportData').value;
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `cabinet-dentaire-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('✅ Fichier téléchargé !');
        }

        function clearData() {
            if (confirm('⚠️ ATTENTION ! Ceci va supprimer TOUTES vos données. Êtes-vous sûr ?')) {
                if (confirm('🚨 DERNIÈRE CHANCE ! Avez-vous exporté vos données ? Cette action est IRRÉVERSIBLE !')) {
                    localStorage.removeItem('cabinet_patients');
                    localStorage.removeItem('cabinet_consultations');
                    localStorage.removeItem('cabinet_payments');
                    localStorage.removeItem('cabinet_appointments');
                    alert('🗑️ Toutes les données ont été supprimées.');
                    loadDataInfo();
                }
            }
        }
    </script>
</body>
</html>
