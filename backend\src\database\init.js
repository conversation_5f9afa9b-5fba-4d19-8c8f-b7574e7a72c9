const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const DB_PATH = process.env.DB_PATH || './database/dental_cabinet.db';
const DB_DIR = path.dirname(DB_PATH);

// Créer le dossier database s'il n'existe pas
if (!fs.existsSync(DB_DIR)) {
  fs.mkdirSync(DB_DIR, { recursive: true });
}

class Database {
  constructor() {
    this.db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Erreur lors de l\'ouverture de la base de données:', err);
      } else {
        console.log('✅ Base de données SQLite connectée');
        this.initTables();
      }
    });
  }

  initTables() {
    // Table des utilisateurs
    this.db.run(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des patients
    this.db.run(`
      CREATE TABLE IF NOT EXISTS patients (
        id TEXT PRIMARY KEY,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        dateOfBirth DATE,
        gender TEXT,
        emergencyContact TEXT,
        emergencyPhone TEXT,
        medicalHistory TEXT,
        allergies TEXT,
        currentMedications TEXT,
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des rendez-vous
    this.db.run(`
      CREATE TABLE IF NOT EXISTS appointments (
        id TEXT PRIMARY KEY,
        patientId TEXT NOT NULL,
        patientName TEXT,
        patientPhone TEXT,
        date DATE NOT NULL,
        time TEXT NOT NULL,
        type TEXT NOT NULL,
        duration INTEGER DEFAULT 30,
        status TEXT DEFAULT 'scheduled',
        notes TEXT,
        isUrgent BOOLEAN DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patientId) REFERENCES patients (id)
      )
    `);

    // Table des consultations
    this.db.run(`
      CREATE TABLE IF NOT EXISTS consultations (
        id TEXT PRIMARY KEY,
        patientId TEXT NOT NULL,
        appointmentId TEXT,
        date DATE NOT NULL,
        type TEXT NOT NULL,
        diagnosis TEXT,
        treatment TEXT,
        prescription TEXT,
        notes TEXT,
        cost DECIMAL(10,2),
        status TEXT DEFAULT 'completed',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patientId) REFERENCES patients (id),
        FOREIGN KEY (appointmentId) REFERENCES appointments (id)
      )
    `);

    // Table des paiements
    this.db.run(`
      CREATE TABLE IF NOT EXISTS payments (
        id TEXT PRIMARY KEY,
        patientId TEXT NOT NULL,
        consultationId TEXT,
        amount DECIMAL(10,2) NOT NULL,
        method TEXT NOT NULL,
        date DATE NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'completed',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patientId) REFERENCES patients (id),
        FOREIGN KEY (consultationId) REFERENCES consultations (id)
      )
    `);

    // Créer un utilisateur admin par défaut
    this.createDefaultAdmin();
  }

  createDefaultAdmin() {
    const bcrypt = require('bcryptjs');
    const { v4: uuidv4 } = require('uuid');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123';
    
    this.db.get('SELECT id FROM users WHERE email = ?', [adminEmail], (err, row) => {
      if (err) {
        console.error('Erreur lors de la vérification de l\'admin:', err);
        return;
      }
      
      if (!row) {
        const hashedPassword = bcrypt.hashSync(adminPassword, 10);
        const adminId = uuidv4();
        
        this.db.run(
          'INSERT INTO users (id, email, password, firstName, lastName, role) VALUES (?, ?, ?, ?, ?, ?)',
          [adminId, adminEmail, hashedPassword, 'Admin', 'Cabinet', 'admin'],
          (err) => {
            if (err) {
              console.error('Erreur lors de la création de l\'admin:', err);
            } else {
              console.log('👤 Utilisateur admin créé:');
              console.log('   Email: <EMAIL>');
              console.log('   Mot de passe: admin123');
            }
          }
        );
      }
    });
  }

  // Méthodes utilitaires
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Base de données fermée');
          resolve();
        }
      });
    });
  }
}

// Instance singleton
const database = new Database();

module.exports = database;
