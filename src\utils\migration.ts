// Script de migration des données localStorage vers l'API
import { patientService, appointmentService } from '../services/api';

export interface LocalStorageData {
  patients: any[];
  appointments: any[];
  consultations: any[];
  payments: any[];
}

export const migrationService = {
  // Exporter les données du localStorage
  exportLocalData(): LocalStorageData {
    const patients = JSON.parse(localStorage.getItem('cabinet_patients') || '[]');
    const appointments = JSON.parse(localStorage.getItem('cabinet_appointments') || '[]');
    const consultations = JSON.parse(localStorage.getItem('cabinet_consultations') || '[]');
    const payments = JSON.parse(localStorage.getItem('cabinet_payments') || '[]');

    console.log('📊 Données trouvées dans localStorage:');
    console.log('👥 Patients:', patients.length);
    console.log('📅 Rendez-vous:', appointments.length);
    console.log('🏥 Consultations:', consultations.length);
    console.log('💰 Paiements:', payments.length);

    return { patients, appointments, consultations, payments };
  },

  // Migrer les patients vers l'API
  async migratePatients(patients: any[]): Promise<any[]> {
    const migratedPatients = [];
    
    for (const patient of patients) {
      try {
        // Adapter le format pour l'API
        const patientData = {
          firstName: patient.firstName,
          lastName: patient.lastName,
          email: patient.email,
          phone: patient.phone,
          address: patient.address,
          dateOfBirth: patient.dateOfBirth ? new Date(patient.dateOfBirth).toISOString().split('T')[0] : undefined,
          gender: patient.gender,
          emergencyContact: patient.emergencyContact,
          emergencyPhone: patient.emergencyPhone,
          medicalHistory: patient.medicalHistory,
          allergies: patient.allergies,
          currentMedications: patient.medications,
          notes: patient.notes
        };

        const newPatient = await patientService.create(patientData);
        migratedPatients.push({
          oldId: patient.id,
          newId: newPatient.id,
          patient: newPatient
        });
        
        console.log(`✅ Patient migré: ${patient.firstName} ${patient.lastName}`);
      } catch (error) {
        console.error(`❌ Erreur migration patient ${patient.firstName} ${patient.lastName}:`, error);
      }
    }

    return migratedPatients;
  },

  // Migrer les rendez-vous vers l'API
  async migrateAppointments(appointments: any[], patientMapping: any[]): Promise<void> {
    for (const appointment of appointments) {
      try {
        // Trouver le nouveau ID du patient
        const patientMap = patientMapping.find(p => p.oldId === appointment.patientId);
        
        const appointmentData = {
          patientId: patientMap?.newId,
          patientName: appointment.patientName || `${appointment.patient?.firstName} ${appointment.patient?.lastName}`,
          patientPhone: appointment.patientPhone || appointment.patient?.phone,
          date: new Date(appointment.date).toISOString().split('T')[0],
          time: appointment.time,
          type: appointment.type,
          duration: appointment.duration || 30,
          status: appointment.status || 'scheduled',
          notes: appointment.notes,
          isUrgent: appointment.isUrgent || false
        };

        await appointmentService.create(appointmentData);
        console.log(`✅ Rendez-vous migré: ${appointmentData.date} ${appointmentData.time}`);
      } catch (error) {
        console.error(`❌ Erreur migration rendez-vous:`, error);
      }
    }
  },

  // Migration complète
  async migrateAll(): Promise<boolean> {
    try {
      console.log('🚀 Début de la migration...');
      
      // 1. Exporter les données locales
      const localData = this.exportLocalData();
      
      if (localData.patients.length === 0) {
        console.log('ℹ️ Aucune donnée à migrer');
        return true;
      }

      // 2. Migrer les patients
      console.log('👥 Migration des patients...');
      const patientMapping = await this.migratePatients(localData.patients);

      // 3. Migrer les rendez-vous
      if (localData.appointments.length > 0) {
        console.log('📅 Migration des rendez-vous...');
        await this.migrateAppointments(localData.appointments, patientMapping);
      }

      console.log('✅ Migration terminée avec succès !');
      return true;
    } catch (error) {
      console.error('❌ Erreur lors de la migration:', error);
      return false;
    }
  },

  // Vérifier si une migration est nécessaire
  needsMigration(): boolean {
    const patients = JSON.parse(localStorage.getItem('cabinet_patients') || '[]');
    return patients.length > 0;
  },

  // Nettoyer le localStorage après migration réussie
  clearLocalStorage(): void {
    if (confirm('🗑️ Voulez-vous supprimer les données locales maintenant qu\'elles sont migrées vers la base de données ?')) {
      localStorage.removeItem('cabinet_patients');
      localStorage.removeItem('cabinet_appointments');
      localStorage.removeItem('cabinet_consultations');
      localStorage.removeItem('cabinet_payments');
      console.log('🧹 localStorage nettoyé');
    }
  }
};

// Fonction utilitaire pour afficher le statut de migration
export const showMigrationStatus = () => {
  const localData = migrationService.exportLocalData();
  
  if (localData.patients.length > 0) {
    console.log('⚠️ Migration nécessaire !');
    console.log('Utilisez migrationService.migrateAll() pour migrer vos données');
  } else {
    console.log('✅ Aucune migration nécessaire');
  }
  
  return localData;
};
