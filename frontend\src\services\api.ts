import axios from 'axios';

// Configuration de l'API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Instance Axios avec configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
}

export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  gender?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  currentMedications?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Appointment {
  id: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  date: string;
  time: string;
  type: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Consultation {
  id: string;
  patientId: string;
  appointmentId?: string;
  date: string;
  type: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  notes?: string;
  cost?: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface Payment {
  id: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: string;
  date: string;
  description?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

// Services d'authentification
export const authService = {
  async login(email: string, password: string) {
    const response = await api.post('/auth/login', { email, password });
    const { token, user } = response.data;
    
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
    
    return { token, user };
  },

  async logout() {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  },

  async getProfile() {
    const response = await api.get('/auth/me');
    return response.data.user;
  },

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  }
};

// Services des patients
export const patientService = {
  async getAll(params?: { search?: string; limit?: number; offset?: number }) {
    const response = await api.get('/patients', { params });
    return response.data;
  },

  async getById(id: string) {
    const response = await api.get(`/patients/${id}`);
    return response.data.patient;
  },

  async create(patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>) {
    const response = await api.post('/patients', patient);
    return response.data.patient;
  },

  async update(id: string, patient: Partial<Patient>) {
    const response = await api.put(`/patients/${id}`, patient);
    return response.data.patient;
  },

  async delete(id: string) {
    await api.delete(`/patients/${id}`);
  },

  async getAppointments(id: string) {
    const response = await api.get(`/patients/${id}/appointments`);
    return response.data.appointments;
  },

  async getConsultations(id: string) {
    const response = await api.get(`/patients/${id}/consultations`);
    return response.data.consultations;
  },

  async getPayments(id: string) {
    const response = await api.get(`/patients/${id}/payments`);
    return response.data.payments;
  }
};

// Services des rendez-vous
export const appointmentService = {
  async getAll(params?: { 
    date?: string; 
    startDate?: string; 
    endDate?: string; 
    patientId?: string; 
    status?: string; 
  }) {
    const response = await api.get('/appointments', { params });
    return response.data.appointments;
  },

  async getById(id: string) {
    const response = await api.get(`/appointments/${id}`);
    return response.data.appointment;
  },

  async create(appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>) {
    const response = await api.post('/appointments', appointment);
    return response.data.appointment;
  },

  async update(id: string, appointment: Partial<Appointment>) {
    const response = await api.put(`/appointments/${id}`, appointment);
    return response.data.appointment;
  },

  async delete(id: string) {
    await api.delete(`/appointments/${id}`);
  },

  async updateStatus(id: string, status: string) {
    const response = await api.patch(`/appointments/${id}/status`, { status });
    return response.data.appointment;
  }
};

// Service de santé de l'API
export const healthService = {
  async check() {
    const response = await api.get('/health');
    return response.data;
  }
};

export default api;
