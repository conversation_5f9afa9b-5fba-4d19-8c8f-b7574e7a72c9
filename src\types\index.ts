export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  address?: string;
  medicalHistory?: string;
  allergies?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Appointment {
  id: string;
  patientId: string;
  date: Date;
  time: string;
  duration: number;
  type: string;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  patient?: Patient;
}

export interface Consultation {
  id: string;
  patientId: string;
  date: Date;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  notes?: string;
  cost?: number;
  paid: boolean;
  createdAt: Date;
  updatedAt: Date;
  patient?: Patient;
}

export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}
