// Base de données SQLite pour l'application cabinet dentaire
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

// Types
export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConsultationData {
  id?: string;
  patientId: string;
  appointmentId?: string;
  date: Date;
  type: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  notes?: string;
  cost?: number;
  status: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PaymentData {
  id?: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: string;
  date: Date;
  description?: string;
  createdAt?: Date;
}

class SQLiteDatabase {
  private db: Database.Database;

  constructor() {
    // Créer la base de données dans le dossier de l'application
    const dbPath = path.join(process.cwd(), 'cabinet.sqlite');
    this.db = new Database(dbPath);
    
    // Activer les clés étrangères
    this.db.pragma('foreign_keys = ON');
    
    this.initTables();
    console.log('✅ Base de données SQLite initialisée:', dbPath);
  }

  private initTables() {
    // Table des patients
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS patients (
        id TEXT PRIMARY KEY,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        dateOfBirth DATE,
        gender TEXT,
        address TEXT,
        city TEXT,
        postalCode TEXT,
        profession TEXT,
        emergencyContact TEXT,
        emergencyPhone TEXT,
        medicalHistory TEXT,
        allergies TEXT,
        medications TEXT,
        bloodType TEXT,
        insuranceNumber TEXT,
        insuranceProvider TEXT,
        notes TEXT,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des rendez-vous
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS appointments (
        id TEXT PRIMARY KEY,
        patientId TEXT,
        patientName TEXT,
        patientPhone TEXT,
        type TEXT NOT NULL,
        date DATE NOT NULL,
        time TEXT NOT NULL,
        duration INTEGER DEFAULT 30,
        status TEXT DEFAULT 'scheduled',
        notes TEXT,
        isUrgent BOOLEAN DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patientId) REFERENCES patients (id) ON DELETE SET NULL
      )
    `);

    // Table des consultations
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS consultations (
        id TEXT PRIMARY KEY,
        patientId TEXT NOT NULL,
        appointmentId TEXT,
        date DATE NOT NULL,
        type TEXT NOT NULL,
        diagnosis TEXT,
        treatment TEXT,
        prescription TEXT,
        notes TEXT,
        cost DECIMAL(10,2),
        status TEXT DEFAULT 'completed',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patientId) REFERENCES patients (id) ON DELETE CASCADE,
        FOREIGN KEY (appointmentId) REFERENCES appointments (id) ON DELETE SET NULL
      )
    `);

    // Table des paiements
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS payments (
        id TEXT PRIMARY KEY,
        patientId TEXT NOT NULL,
        consultationId TEXT,
        amount DECIMAL(10,2) NOT NULL,
        method TEXT NOT NULL,
        date DATE NOT NULL,
        description TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patientId) REFERENCES patients (id) ON DELETE CASCADE,
        FOREIGN KEY (consultationId) REFERENCES consultations (id) ON DELETE SET NULL
      )
    `);

    console.log('📋 Tables SQLite créées');
  }

  // === PATIENTS ===
  createPatient(patient: PatientData): PatientData {
    const id = uuidv4();
    const now = new Date();
    
    const stmt = this.db.prepare(`
      INSERT INTO patients (
        id, firstName, lastName, phone, email, dateOfBirth, gender,
        address, city, postalCode, profession, emergencyContact, emergencyPhone,
        medicalHistory, allergies, medications, bloodType, insuranceNumber,
        insuranceProvider, notes, isActive, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id, patient.firstName, patient.lastName, patient.phone, patient.email,
      patient.dateOfBirth?.toISOString(), patient.gender, patient.address,
      patient.city, patient.postalCode, patient.profession, patient.emergencyContact,
      patient.emergencyPhone, patient.medicalHistory, patient.allergies,
      patient.medications, patient.bloodType, patient.insuranceNumber,
      patient.insuranceProvider, patient.notes, patient.isActive ?? true,
      now.toISOString(), now.toISOString()
    );

    return { ...patient, id, createdAt: now, updatedAt: now };
  }

  getAllPatients(): PatientData[] {
    const stmt = this.db.prepare('SELECT * FROM patients WHERE isActive = 1 ORDER BY lastName, firstName');
    const rows = stmt.all();
    
    return rows.map(row => ({
      ...row,
      dateOfBirth: row.dateOfBirth ? new Date(row.dateOfBirth) : undefined,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      isActive: Boolean(row.isActive)
    }));
  }

  getPatientById(id: string): PatientData | null {
    const stmt = this.db.prepare('SELECT * FROM patients WHERE id = ?');
    const row = stmt.get(id);
    
    if (!row) return null;
    
    return {
      ...row,
      dateOfBirth: row.dateOfBirth ? new Date(row.dateOfBirth) : undefined,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      isActive: Boolean(row.isActive)
    };
  }

  updatePatient(id: string, patient: Partial<PatientData>): PatientData | null {
    const now = new Date();
    
    const stmt = this.db.prepare(`
      UPDATE patients SET
        firstName = COALESCE(?, firstName),
        lastName = COALESCE(?, lastName),
        phone = COALESCE(?, phone),
        email = COALESCE(?, email),
        dateOfBirth = COALESCE(?, dateOfBirth),
        gender = COALESCE(?, gender),
        address = COALESCE(?, address),
        city = COALESCE(?, city),
        postalCode = COALESCE(?, postalCode),
        profession = COALESCE(?, profession),
        emergencyContact = COALESCE(?, emergencyContact),
        emergencyPhone = COALESCE(?, emergencyPhone),
        medicalHistory = COALESCE(?, medicalHistory),
        allergies = COALESCE(?, allergies),
        medications = COALESCE(?, medications),
        bloodType = COALESCE(?, bloodType),
        insuranceNumber = COALESCE(?, insuranceNumber),
        insuranceProvider = COALESCE(?, insuranceProvider),
        notes = COALESCE(?, notes),
        updatedAt = ?
      WHERE id = ?
    `);

    const result = stmt.run(
      patient.firstName, patient.lastName, patient.phone, patient.email,
      patient.dateOfBirth?.toISOString(), patient.gender, patient.address,
      patient.city, patient.postalCode, patient.profession, patient.emergencyContact,
      patient.emergencyPhone, patient.medicalHistory, patient.allergies,
      patient.medications, patient.bloodType, patient.insuranceNumber,
      patient.insuranceProvider, patient.notes, now.toISOString(), id
    );

    return result.changes > 0 ? this.getPatientById(id) : null;
  }

  deletePatient(id: string): boolean {
    const stmt = this.db.prepare('UPDATE patients SET isActive = 0, updatedAt = ? WHERE id = ?');
    const result = stmt.run(new Date().toISOString(), id);
    return result.changes > 0;
  }

  searchPatients(query: string): PatientData[] {
    const stmt = this.db.prepare(`
      SELECT * FROM patients 
      WHERE isActive = 1 AND (
        firstName LIKE ? OR 
        lastName LIKE ? OR 
        phone LIKE ? OR 
        email LIKE ?
      )
      ORDER BY lastName, firstName
    `);
    
    const searchTerm = `%${query}%`;
    const rows = stmt.all(searchTerm, searchTerm, searchTerm, searchTerm);
    
    return rows.map(row => ({
      ...row,
      dateOfBirth: row.dateOfBirth ? new Date(row.dateOfBirth) : undefined,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      isActive: Boolean(row.isActive)
    }));
  }

  // === RENDEZ-VOUS ===
  createAppointment(appointment: AppointmentData): AppointmentData {
    const id = uuidv4();
    const now = new Date();
    
    const stmt = this.db.prepare(`
      INSERT INTO appointments (
        id, patientId, patientName, patientPhone, type, date, time,
        duration, status, notes, isUrgent, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id, appointment.patientId, appointment.patientName, appointment.patientPhone,
      appointment.type, appointment.date.toISOString().split('T')[0], appointment.time,
      appointment.duration, appointment.status, appointment.notes,
      appointment.isUrgent ? 1 : 0, now.toISOString(), now.toISOString()
    );

    return { ...appointment, id, createdAt: now, updatedAt: now };
  }

  getAllAppointments(): AppointmentData[] {
    const stmt = this.db.prepare('SELECT * FROM appointments ORDER BY date DESC, time DESC');
    const rows = stmt.all();
    
    return rows.map(row => ({
      ...row,
      date: new Date(row.date),
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      isUrgent: Boolean(row.isUrgent)
    }));
  }

  getAppointmentsByPatient(patientId: string): AppointmentData[] {
    const stmt = this.db.prepare('SELECT * FROM appointments WHERE patientId = ? ORDER BY date DESC, time DESC');
    const rows = stmt.all(patientId);
    
    return rows.map(row => ({
      ...row,
      date: new Date(row.date),
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      isUrgent: Boolean(row.isUrgent)
    }));
  }

  updateAppointment(id: string, appointment: Partial<AppointmentData>): AppointmentData | null {
    const now = new Date();
    
    const stmt = this.db.prepare(`
      UPDATE appointments SET
        patientId = COALESCE(?, patientId),
        patientName = COALESCE(?, patientName),
        patientPhone = COALESCE(?, patientPhone),
        type = COALESCE(?, type),
        date = COALESCE(?, date),
        time = COALESCE(?, time),
        duration = COALESCE(?, duration),
        status = COALESCE(?, status),
        notes = COALESCE(?, notes),
        isUrgent = COALESCE(?, isUrgent),
        updatedAt = ?
      WHERE id = ?
    `);

    const result = stmt.run(
      appointment.patientId, appointment.patientName, appointment.patientPhone,
      appointment.type, appointment.date?.toISOString().split('T')[0], appointment.time,
      appointment.duration, appointment.status, appointment.notes,
      appointment.isUrgent ? 1 : 0, now.toISOString(), id
    );

    if (result.changes > 0) {
      const getStmt = this.db.prepare('SELECT * FROM appointments WHERE id = ?');
      const row = getStmt.get(id);
      return {
        ...row,
        date: new Date(row.date),
        createdAt: new Date(row.createdAt),
        updatedAt: new Date(row.updatedAt),
        isUrgent: Boolean(row.isUrgent)
      };
    }
    
    return null;
  }

  deleteAppointment(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM appointments WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Fermer la base de données
  close(): void {
    this.db.close();
  }
}

// Instance singleton
export const database = new SQLiteDatabase();
export default database;
