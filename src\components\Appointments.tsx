import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { database } from '../lib/database';
import AppointmentModal from './AppointmentModal';

// Interface locale pour éviter les problèmes d'import
interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const Appointments: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState<AppointmentData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentData | null>(null);

  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = () => {
    try {
      const allAppointments = database.getAllAppointments();
      setAppointments(allAppointments);
    } catch (error) {
      console.error('Erreur lors du chargement des rendez-vous:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getAppointmentsForDate = (date: Date) => {
    return appointments.filter(apt => {
      const aptDate = new Date(apt.date);
      return aptDate.toDateString() === date.toDateString();
    }).filter(apt => {
      if (!searchTerm) return true;
      return apt.patientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
             apt.type.toLowerCase().includes(searchTerm.toLowerCase());
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setDate(newDate.getDate() + 7);
    }
    setCurrentDate(newDate);
  };

  const getWeekDays = () => {
    const startOfWeek = new Date(currentDate);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Lundi = début de semaine
    startOfWeek.setDate(diff);

    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const getTimeSlots = () => {
    const slots = [];
    for (let hour = 8; hour <= 18; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`);
      if (hour < 18) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`);
      }
    }
    return slots;
  };

  const formatWeekRange = () => {
    const weekDays = getWeekDays();
    const start = weekDays[0];
    const end = weekDays[6];
    return `${start.getDate()} ${start.toLocaleDateString('fr-FR', { month: 'short' })} - ${end.getDate()} ${end.toLocaleDateString('fr-FR', { month: 'short' })} ${end.getFullYear()}`;
  };

  const handleSaveAppointment = (appointmentData: any) => {
    console.log('🔄 Sauvegarde du rendez-vous:', appointmentData);
    try {
      if (selectedAppointment) {
        // Modifier un rendez-vous existant
        console.log('✏️ Modification du rendez-vous:', selectedAppointment.id);
        database.updateAppointment(selectedAppointment.id!, appointmentData);
      } else {
        // Créer un nouveau rendez-vous
        console.log('➕ Création d\'un nouveau rendez-vous');
        const newAppointment = database.createAppointment(appointmentData);
        console.log('✅ Rendez-vous créé:', newAppointment);
      }
      loadAppointments();
      setIsModalOpen(false);
      setSelectedAppointment(null);
      console.log('✅ Sauvegarde terminée avec succès');
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error);
    }
  };

  const handleEditAppointment = (appointment: AppointmentData) => {
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  };

  const handleDeleteAppointment = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous ?')) {
      try {
        database.deleteAppointment(id);
        loadAppointments();
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900">Agenda</h1>
          <p className="mt-2 text-sm text-gray-700">
            Calendrier des rendez-vous
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button 
            onClick={() => {
              setSelectedAppointment(null);
              setIsModalOpen(true);
            }}
            className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nouveau RDV
          </button>
        </div>
      </div>

      {/* Navigation du calendrier */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-4 md:p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => navigateDate('prev')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-semibold text-gray-900">
              Semaine du {formatWeekRange()}
            </h2>
            <button 
              onClick={() => navigateDate('next')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
            <button
              onClick={() => setCurrentDate(new Date())}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
            >
              Aujourd'hui
            </button>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
            />
          </div>
        </div>

        {/* Planning hebdomadaire */}
        <div className="bg-white rounded-lg overflow-hidden">
          {/* En-têtes des jours */}
          <div className="grid grid-cols-8 border-b border-gray-200">
            <div className="p-4 bg-gray-50 border-r border-gray-200">
              <span className="text-sm font-medium text-gray-600">Heures</span>
            </div>
            {getWeekDays().map((day, index) => {
              const isToday = new Date().toDateString() === day.toDateString();
              return (
                <div
                  key={index}
                  className={`p-4 text-center border-r border-gray-200 last:border-r-0 ${
                    isToday ? 'bg-blue-50' : 'bg-gray-50'
                  }`}
                >
                  <div className={`text-sm font-medium ${isToday ? 'text-blue-700' : 'text-gray-900'}`}>
                    {day.toLocaleDateString('fr-FR', { weekday: 'short' })}
                  </div>
                  <div className={`text-lg font-bold ${isToday ? 'text-blue-700' : 'text-gray-700'}`}>
                    {day.getDate()}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Grille horaire */}
          <div className="grid grid-cols-8">
            {/* Colonne des heures */}
            <div className="border-r border-gray-200">
              {getTimeSlots().map((time) => (
                <div key={time} className="h-16 p-2 border-b border-gray-100 bg-gray-50 flex items-center">
                  <span className="text-xs font-medium text-gray-600">{time}</span>
                </div>
              ))}
            </div>

            {/* Colonnes des jours */}
            {getWeekDays().map((day, dayIndex) => {
              const isToday = new Date().toDateString() === day.toDateString();
              return (
                <div key={dayIndex} className="border-r border-gray-200 last:border-r-0">
                  {getTimeSlots().map((time) => {
                    const dayAppointments = getAppointmentsForDate(day);
                    const timeAppointments = dayAppointments.filter(apt => apt.time === time);

                    return (
                      <div
                        key={time}
                        className={`h-16 border-b border-gray-100 p-1 relative hover:bg-gray-50 ${
                          isToday ? 'bg-blue-25' : ''
                        }`}
                      >
                        {timeAppointments.map((appointment, idx) => {
                          const colors = [
                            'bg-emerald-500 text-white',
                            'bg-purple-500 text-white',
                            'bg-orange-500 text-white',
                            'bg-pink-500 text-white',
                            'bg-indigo-500 text-white'
                          ];
                          const urgentColor = 'bg-red-500 text-white';
                          const appointmentColor = appointment.isUrgent ? urgentColor : colors[idx % colors.length];

                          return (
                            <div
                              key={appointment.id}
                              onClick={() => handleEditAppointment(appointment)}
                              className={`absolute inset-1 rounded-md p-1 cursor-pointer hover:shadow-md transition-all duration-200 ${appointmentColor}`}
                            >
                              <div className="text-xs font-semibold truncate">
                                {appointment.patientName}
                              </div>
                              <div className="text-xs opacity-90 truncate">
                                {appointment.type}
                              </div>
                            </div>
                          );
                        })}

                        {/* Bouton d'ajout rapide */}
                        {timeAppointments.length === 0 && (
                          <button
                            onClick={() => {
                              setSelectedAppointment(null);
                              setIsModalOpen(true);
                            }}
                            className="absolute inset-1 rounded-md border border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100"
                          >
                            <span className="text-xs text-gray-400 hover:text-blue-600">+</span>
                          </button>
                        )}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <AppointmentModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedAppointment(null);
          }}
          onSave={handleSaveAppointment}
          appointment={selectedAppointment}
          preselectedPatient={null}
        />
      )}
    </div>
  );
};

export default Appointments;
