import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Plus,
  Clock,
  User,
  Phone,
  ChevronLeft,
  ChevronRight,
  Search
} from 'lucide-react';
import { prisma } from '../lib/prisma';
import { format, addDays, startOfWeek, endOfWeek, isSameDay } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Appointment {
  id: string;
  patientId: string;
  date: Date;
  time: string;
  duration: number;
  type: string;
  status: string;
  notes?: string;
  patient?: {
    firstName: string;
    lastName: string;
    phone: string;
  };
}

const Appointments: React.FC = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'day' | 'week'>('week');
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = async () => {
    try {
      setIsLoading(true);
      const data = await prisma.appointment.findMany({});
      setAppointments(data);
    } catch (error) {
      console.error('Erreur lors du chargement des rendez-vous:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getWeekDays = () => {
    const start = startOfWeek(currentDate, { weekStartsOn: 1 });
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getAppointmentsForDate = (date: Date) => {
    return appointments.filter(apt => 
      isSameDay(new Date(apt.date), date) &&
      (searchTerm === '' || 
       `${apt.patient?.firstName} ${apt.patient?.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
       apt.patient?.phone.includes(searchTerm))
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SCHEDULED': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      case 'NO_SHOW': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'CONSULTATION': return 'bg-purple-100 text-purple-800';
      case 'CONTROLE': return 'bg-green-100 text-green-800';
      case 'URGENCE': return 'bg-red-100 text-red-800';
      case 'TRAITEMENT': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const timeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Rendez-vous</h1>
          <p className="mt-2 text-sm text-gray-700">
            Planifiez et gérez vos rendez-vous patients
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500">
            <Plus className="w-4 h-4 mr-2" />
            Nouveau RDV
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Navigation */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => setCurrentDate(addDays(currentDate, viewMode === 'day' ? -1 : -7))}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <h2 className="text-lg font-semibold text-gray-900 min-w-[200px] text-center">
                {viewMode === 'day' 
                  ? format(currentDate, 'EEEE dd MMMM yyyy', { locale: fr })
                  : `Semaine du ${format(startOfWeek(currentDate, { weekStartsOn: 1 }), 'dd MMM', { locale: fr })} au ${format(endOfWeek(currentDate, { weekStartsOn: 1 }), 'dd MMM yyyy', { locale: fr })}`
                }
              </h2>
              <button 
                onClick={() => setCurrentDate(addDays(currentDate, viewMode === 'day' ? 1 : 7))}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
            <button 
              onClick={() => setCurrentDate(new Date())}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Aujourd'hui
            </button>
          </div>

          {/* View Mode & Search */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher un patient..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('day')}
                className={`px-3 py-1 text-sm rounded-md ${viewMode === 'day' ? 'bg-white shadow-sm' : ''}`}
              >
                Jour
              </button>
              <button
                onClick={() => setViewMode('week')}
                className={`px-3 py-1 text-sm rounded-md ${viewMode === 'week' ? 'bg-white shadow-sm' : ''}`}
              >
                Semaine
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar View */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden">
        {viewMode === 'week' ? (
          <div className="grid grid-cols-8 divide-x divide-gray-200">
            {/* Time column */}
            <div className="bg-gray-50">
              <div className="h-16 border-b border-gray-200 flex items-center justify-center">
                <Clock className="w-5 h-5 text-gray-400" />
              </div>
              {timeSlots.map(time => (
                <div key={time} className="h-16 border-b border-gray-200 flex items-center justify-center text-sm text-gray-500">
                  {time}
                </div>
              ))}
            </div>

            {/* Days columns */}
            {getWeekDays().map(day => (
              <div key={day.toISOString()} className="min-w-0">
                <div className="h-16 border-b border-gray-200 p-4">
                  <div className="text-sm font-medium text-gray-900">
                    {format(day, 'EEE', { locale: fr })}
                  </div>
                  <div className={`text-lg font-semibold ${isSameDay(day, new Date()) ? 'text-blue-600' : 'text-gray-900'}`}>
                    {format(day, 'd')}
                  </div>
                </div>
                
                {/* Time slots for this day */}
                <div className="relative">
                  {timeSlots.map(time => (
                    <div key={time} className="h-16 border-b border-gray-200 p-1">
                      {getAppointmentsForDate(day)
                        .filter(apt => apt.time === time)
                        .map(apt => (
                          <div
                            key={apt.id}
                            className="bg-blue-100 border border-blue-200 rounded p-1 text-xs cursor-pointer hover:bg-blue-200 transition-colors"
                          >
                            <div className="font-medium text-blue-900 truncate">
                              {apt.patient?.firstName} {apt.patient?.lastName}
                            </div>
                            <div className="text-blue-700 truncate">
                              {apt.type} - {apt.duration}min
                            </div>
                          </div>
                        ))
                      }
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Day view - List format */
          <div className="p-6">
            <div className="space-y-4">
              {getAppointmentsForDate(currentDate).length === 0 ? (
                <div className="text-center py-12">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Aucun rendez-vous pour cette journée</p>
                </div>
              ) : (
                getAppointmentsForDate(currentDate)
                  .sort((a, b) => a.time.localeCompare(b.time))
                  .map(apt => (
                    <div key={apt.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="text-lg font-semibold text-gray-900 min-w-[60px]">
                          {apt.time}
                        </div>
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {apt.patient?.firstName} {apt.patient?.lastName}
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span className="flex items-center">
                              <Phone className="w-4 h-4 mr-1" />
                              {apt.patient?.phone}
                            </span>
                            <span>{apt.duration} minutes</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getTypeColor(apt.type)}`}>
                          {apt.type}
                        </span>
                        <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(apt.status)}`}>
                          {apt.status}
                        </span>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Appointments;
