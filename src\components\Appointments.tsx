import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Plus,
  Clock,
  User,
  Phone,
  ChevronLeft,
  ChevronRight,
  Search,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { database } from '../lib/database';
import AppointmentModal from './AppointmentModal';

// Interface locale pour éviter les problèmes d'import
interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const Appointments: React.FC = () => {
  const [appointments, setAppointments] = useState<AppointmentData[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'day' | 'week'>('day');
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentData | null>(null);

  useEffect(() => {
    loadAppointments();
  }, [currentDate, viewMode]);

  const loadAppointments = () => {
    setIsLoading(true);
    try {
      if (viewMode === 'day') {
        const dayAppointments = database.getAppointmentsByDate(currentDate);
        setAppointments(dayAppointments);
      } else {
        const weekStart = getWeekStart(currentDate);
        const weekAppointments = database.getAppointmentsByWeek(weekStart);
        setAppointments(weekAppointments);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des rendez-vous:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getWeekStart = (date: Date) => {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1); // Lundi comme premier jour
    start.setDate(diff);
    return start;
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (viewMode === 'day') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    setCurrentDate(newDate);
  };

  const handleSaveAppointment = (appointmentData: any) => {
    if (selectedAppointment) {
      database.updateAppointment(selectedAppointment.id!, appointmentData);
    } else {
      database.createAppointment(appointmentData);
    }
    loadAppointments();
    setIsModalOpen(false);
    setSelectedAppointment(null);
  };

  const handleEditAppointment = (appointment: AppointmentData) => {
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  };

  const handleDeleteAppointment = (appointmentId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous ?')) {
      database.deleteAppointment(appointmentId);
      loadAppointments();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SCHEDULED': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      case 'NO_SHOW': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'SCHEDULED': return 'Planifié';
      case 'COMPLETED': return 'Terminé';
      case 'CANCELLED': return 'Annulé';
      case 'NO_SHOW': return 'Absent';
      default: return status;
    }
  };

  const getWeekDays = () => {
    const start = getWeekStart(currentDate);
    return Array.from({ length: 7 }, (_, i) => {
      const day = new Date(start);
      day.setDate(start.getDate() + i);
      return day;
    });
  };

  const getAppointmentsForDate = (date: Date) => {
    return appointments.filter(apt => {
      const aptDate = new Date(apt.date);
      aptDate.setHours(0, 0, 0, 0);
      const targetDate = new Date(date);
      targetDate.setHours(0, 0, 0, 0);

      const matchesDate = aptDate.getTime() === targetDate.getTime();
      const matchesSearch = searchTerm === '' ||
        (apt.patientName && apt.patientName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (apt.patientPhone && apt.patientPhone.includes(searchTerm)) ||
        apt.type.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesDate && matchesSearch;
    });
  };

  const getTypeColor = (type: string) => {
    switch (type.toUpperCase()) {
      case 'CONSULTATION': return 'bg-purple-100 text-purple-800';
      case 'CONTROLE': return 'bg-green-100 text-green-800';
      case 'URGENCE': return 'bg-red-100 text-red-800';
      case 'TRAITEMENT': return 'bg-blue-100 text-blue-800';
      case 'DETARTRAGE': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateShort = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'short',
      day: 'numeric',
      month: 'short'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900">Agenda</h1>
          <p className="mt-2 text-sm text-gray-700">
            Gérez vos rendez-vous du jour et de la semaine
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => {
              setSelectedAppointment(null);
              setIsModalOpen(true);
            }}
            className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nouveau RDV
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-4 md:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Navigation */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateDate('prev')}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <h2 className="text-lg font-semibold text-gray-900 min-w-[200px] text-center">
                {viewMode === 'day'
                  ? formatDate(currentDate)
                  : `Semaine du ${formatDateShort(getWeekStart(currentDate))}`
                }
              </h2>
              <button 
                onClick={() => setCurrentDate(addDays(currentDate, viewMode === 'day' ? 1 : 7))}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
            <button 
              onClick={() => setCurrentDate(new Date())}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Aujourd'hui
            </button>
          </div>

          {/* View Mode & Search */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher un patient..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('day')}
                className={`px-3 py-1 text-sm rounded-md ${viewMode === 'day' ? 'bg-white shadow-sm' : ''}`}
              >
                Jour
              </button>
              <button
                onClick={() => setViewMode('week')}
                className={`px-3 py-1 text-sm rounded-md ${viewMode === 'week' ? 'bg-white shadow-sm' : ''}`}
              >
                Semaine
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Appointments View */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden">
        {viewMode === 'day' ? (
          /* Vue jour */
          <div className="p-4 md:p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Rendez-vous du {formatDate(currentDate)}
            </h3>

            {appointments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p>Aucun rendez-vous pour cette date</p>
              </div>
            ) : (
              <div className="space-y-3">
                {appointments.map((appointment) => (
                  <div key={appointment.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className={`w-3 h-3 rounded-full ${
                            appointment.isUrgent ? 'bg-red-500' : 'bg-blue-500'
                          }`}></div>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">
                              {appointment.time}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(appointment.type)}`}>
                              {appointment.type}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(appointment.status)}`}>
                              {getStatusText(appointment.status)}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            <div className="flex items-center space-x-4">
                              <span className="flex items-center">
                                <User className="w-4 h-4 mr-1" />
                                {appointment.patientName || 'Patient non spécifié'}
                              </span>
                              {appointment.patientPhone && (
                                <span className="flex items-center">
                                  <Phone className="w-4 h-4 mr-1" />
                                  {appointment.patientPhone}
                                </span>
                              )}
                            </div>
                          </div>
                          {appointment.notes && (
                            <div className="text-sm text-gray-500 mt-1">
                              {appointment.notes}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 mt-3 sm:mt-0">
                        <button
                          onClick={() => handleEditAppointment(appointment)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteAppointment(appointment.id!)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          /* Vue semaine */
          <div className="p-4 md:p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Semaine du {formatDateShort(getWeekStart(currentDate))}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
              {getWeekDays().map((day) => {
                const dayAppointments = getAppointmentsForDate(day);
                const isToday = new Date().toDateString() === day.toDateString();

                return (
                  <div key={day.toISOString()} className={`border rounded-lg p-3 ${isToday ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                    <div className="text-center mb-3">
                      <div className={`text-sm font-medium ${isToday ? 'text-blue-900' : 'text-gray-900'}`}>
                        {formatDateShort(day)}
                      </div>
                      <div className={`text-xs ${isToday ? 'text-blue-600' : 'text-gray-500'}`}>
                        {dayAppointments.length} RDV
                      </div>
                    </div>

                    <div className="space-y-2">
                      {dayAppointments.slice(0, 3).map((appointment) => (
                        <div key={appointment.id} className="bg-white rounded p-2 text-xs border">
                          <div className="font-medium text-gray-900 truncate">
                            {appointment.time} - {appointment.patientName}
                          </div>
                          <div className="text-gray-500 truncate">
                            {appointment.type}
                          </div>
                        </div>
                      ))}
                      {dayAppointments.length > 3 && (
                        <div className="text-xs text-gray-500 text-center">
                          +{dayAppointments.length - 3} autres
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      <AppointmentModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedAppointment(null);
        }}
        onSave={handleSaveAppointment}
        appointment={selectedAppointment}
      />
    </div>
  );
};

export default Appointments;
