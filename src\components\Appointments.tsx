import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Plus,
  Search,
  ChevronLeft,
  ChevronRight,
  User,
  Phone,
  Clock,
  Edit,
  Trash2
} from 'lucide-react';
import { database } from '../lib/database';
import AppointmentModal from './AppointmentModal';

// Interface locale pour éviter les problèmes d'import
interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const Appointments: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState<AppointmentData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentData | null>(null);

  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = () => {
    try {
      const allAppointments = database.getAllAppointments();
      setAppointments(allAppointments);
    } catch (error) {
      console.error('Erreur lors du chargement des rendez-vous:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getAppointmentsForDate = (date: Date) => {
    return appointments.filter(apt => {
      const aptDate = new Date(apt.date);
      return aptDate.toDateString() === date.toDateString();
    }).filter(apt => {
      if (!searchTerm) return true;
      return apt.patientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
             apt.type.toLowerCase().includes(searchTerm.toLowerCase());
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const getCalendarDays = () => {
    const start = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const end = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    
    // Trouver le lundi de la première semaine
    const startDate = new Date(start);
    const startDay = start.getDay();
    const daysToSubtract = startDay === 0 ? 6 : startDay - 1;
    startDate.setDate(start.getDate() - daysToSubtract);
    
    // Générer 42 jours (6 semaines)
    const days = [];
    for (let i = 0; i < 42; i++) {
      const day = new Date(startDate);
      day.setDate(startDate.getDate() + i);
      days.push(day);
    }
    
    return days;
  };

  const handleSaveAppointment = (appointmentData: any) => {
    console.log('🔄 Sauvegarde du rendez-vous:', appointmentData);
    try {
      if (selectedAppointment) {
        // Modifier un rendez-vous existant
        console.log('✏️ Modification du rendez-vous:', selectedAppointment.id);
        database.updateAppointment(selectedAppointment.id!, appointmentData);
      } else {
        // Créer un nouveau rendez-vous
        console.log('➕ Création d\'un nouveau rendez-vous');
        const newAppointment = database.createAppointment(appointmentData);
        console.log('✅ Rendez-vous créé:', newAppointment);
      }
      loadAppointments();
      setIsModalOpen(false);
      setSelectedAppointment(null);
      console.log('✅ Sauvegarde terminée avec succès');
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error);
    }
  };

  const handleEditAppointment = (appointment: AppointmentData) => {
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  };

  const handleDeleteAppointment = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous ?')) {
      try {
        database.deleteAppointment(id);
        loadAppointments();
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900">Agenda</h1>
          <p className="mt-2 text-sm text-gray-700">
            Calendrier des rendez-vous
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button 
            onClick={() => {
              setSelectedAppointment(null);
              setIsModalOpen(true);
            }}
            className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nouveau RDV
          </button>
        </div>
      </div>

      {/* Navigation du calendrier */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-4 md:p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => navigateDate('prev')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-semibold text-gray-900">
              {formatDate(currentDate)}
            </h2>
            <button 
              onClick={() => navigateDate('next')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
            <button
              onClick={() => setCurrentDate(new Date())}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
            >
              Aujourd'hui
            </button>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
            />
          </div>
        </div>

        {/* Calendrier moderne */}
        <div className="grid grid-cols-7 gap-1 bg-gray-100 rounded-xl p-1">
          {/* En-têtes des jours */}
          {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map((day) => (
            <div key={day} className="bg-white rounded-lg p-4 text-center text-sm font-semibold text-gray-800 shadow-sm">
              {day}
            </div>
          ))}

          {/* Jours du calendrier */}
          {getCalendarDays().map((day, index) => {
            const dayAppointments = getAppointmentsForDate(day);
            const isToday = new Date().toDateString() === day.toDateString();
            const isCurrentMonth = day.getMonth() === currentDate.getMonth();

            return (
              <div
                key={index}
                className={`bg-white rounded-lg p-3 min-h-[140px] shadow-sm hover:shadow-md transition-all duration-200 ${
                  !isCurrentMonth ? 'opacity-40' : ''
                } ${isToday ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}
              >
                <div className={`text-sm font-semibold mb-3 ${
                  isToday ? 'text-blue-700' : isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                }`}>
                  {day.getDate()}
                </div>

                <div className="space-y-1.5">
                  {dayAppointments.slice(0, 3).map((appointment, idx) => {
                    const colors = [
                      'bg-emerald-100 text-emerald-800 border-emerald-200',
                      'bg-purple-100 text-purple-800 border-purple-200',
                      'bg-orange-100 text-orange-800 border-orange-200',
                      'bg-pink-100 text-pink-800 border-pink-200',
                      'bg-indigo-100 text-indigo-800 border-indigo-200'
                    ];
                    const urgentColor = 'bg-red-100 text-red-800 border-red-300';
                    const appointmentColor = appointment.isUrgent ? urgentColor : colors[idx % colors.length];

                    return (
                      <div
                        key={appointment.id}
                        onClick={() => handleEditAppointment(appointment)}
                        className={`text-xs p-2 rounded-md cursor-pointer border transition-all duration-200 hover:scale-105 hover:shadow-sm ${appointmentColor}`}
                      >
                        <div className="font-semibold truncate">
                          {appointment.time}
                        </div>
                        <div className="truncate opacity-90 mt-0.5">
                          {appointment.patientName}
                        </div>
                        <div className="truncate opacity-75 text-[10px] mt-0.5">
                          {appointment.type}
                        </div>
                      </div>
                    );
                  })}
                  {dayAppointments.length > 3 && (
                    <div className="text-xs text-gray-500 text-center bg-gray-50 rounded-md py-1 border border-gray-200">
                      +{dayAppointments.length - 3} autres
                    </div>
                  )}

                  {/* Bouton d'ajout rapide */}
                  {isCurrentMonth && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAppointment(null);
                        setIsModalOpen(true);
                      }}
                      className="w-full text-xs text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md py-1.5 border border-dashed border-gray-300 hover:border-blue-300 transition-all duration-200"
                    >
                      + Ajouter
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <AppointmentModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedAppointment(null);
          }}
          onSave={handleSaveAppointment}
          appointment={selectedAppointment}
        />
      )}
    </div>
  );
};

export default Appointments;
