import React, { useState, useEffect } from 'react';
import { X, CreditCard, Save } from 'lucide-react';
import { database } from '../lib/database';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  consultationId: string;
  consultationType: string;
  totalAmount: number;
  onPaymentAdded: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  consultationId,
  consultationType,
  totalAmount,
  onPaymentAdded
}) => {
  const [paymentAmount, setPaymentAmount] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [remainingAmount, setRemainingAmount] = useState(0);

  useEffect(() => {
    if (isOpen && consultationId) {
      const remaining = database.getRemainingAmountForConsultation(consultationId);
      setRemainingAmount(remaining);
    }
  }, [isOpen, consultationId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
      setMessage({ type: 'error', text: 'Veuillez saisir un montant valide' });
      return;
    }

    const amount = parseFloat(paymentAmount);
    if (amount > remainingAmount) {
      setMessage({ type: 'error', text: `Le montant ne peut pas dépasser ${remainingAmount.toLocaleString()} DA` });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const success = database.addPayment(consultationId, amount);
      
      if (success) {
        setMessage({ type: 'success', text: `Paiement de ${amount.toLocaleString()} DA enregistré avec succès` });
        setPaymentAmount('');
        onPaymentAdded();
        
        // Mettre à jour le montant restant
        const newRemaining = database.getRemainingAmountForConsultation(consultationId);
        setRemainingAmount(newRemaining);
        
        // Fermer la modal après 1 seconde
        setTimeout(() => {
          onClose();
          setMessage(null);
        }, 1000);
      } else {
        setMessage({ type: 'error', text: 'Erreur lors de l\'enregistrement du paiement' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors de l\'enregistrement du paiement' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <CreditCard className="w-5 h-5 text-green-600 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900">Enregistrer un paiement</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Info consultation */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600 mb-2">Consultation: <strong>{consultationType}</strong></div>
            <div className="text-sm text-gray-600 mb-2">Montant total: <strong>{totalAmount.toLocaleString()} DA</strong></div>
            <div className="text-sm text-red-600">Reste à payer: <strong>{remainingAmount.toLocaleString()} DA</strong></div>
          </div>

          {/* Message */}
          {message && (
            <div className={`mb-4 p-3 rounded-lg ${
              message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
            }`}>
              {message.text}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Montant à payer (DA)
              </label>
              <input
                type="number"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(e.target.value)}
                step="100"
                min="0"
                max={remainingAmount}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder={`Maximum: ${remainingAmount.toLocaleString()} DA`}
                disabled={isLoading}
              />
            </div>

            {/* Boutons rapides */}
            <div className="mb-6 flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => setPaymentAmount(remainingAmount.toString())}
                className="px-3 py-1 bg-green-100 text-green-700 text-sm rounded hover:bg-green-200 transition-colors"
                disabled={isLoading}
              >
                Solde complet
              </button>
              <button
                type="button"
                onClick={() => setPaymentAmount((remainingAmount / 2).toString())}
                className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded hover:bg-blue-200 transition-colors"
                disabled={isLoading}
              >
                50%
              </button>
              <button
                type="button"
                onClick={() => setPaymentAmount((remainingAmount / 3).toString())}
                className="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded hover:bg-purple-200 transition-colors"
                disabled={isLoading}
              >
                33%
              </button>
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors text-sm"
                disabled={isLoading}
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={isLoading || !paymentAmount || parseFloat(paymentAmount) <= 0}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors text-sm"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enregistrement...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Enregistrer paiement
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
