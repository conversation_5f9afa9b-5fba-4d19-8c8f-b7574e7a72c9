const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const os = require('os');

const app = express();
const PORT = 3001;

app.use(cors({ origin: true, credentials: true }));
app.use(express.json({ limit: '10mb' }));

const dbPath = path.join(__dirname, '..', 'cabinet.json');
let database = { patients: [], appointments: [] };

function loadDatabase() {
  try {
    if (fs.existsSync(dbPath)) {
      const data = fs.readFileSync(dbPath, 'utf8');
      database = JSON.parse(data);
      console.log('📁 Base chargée:', database.patients?.length || 0, 'patients');
    } else {
      console.log('📁 Nouvelle base créée');
    }
  } catch (error) {
    console.error('Erreur chargement:', error);
    database = { patients: [], appointments: [] };
  }
}

function saveDatabase() {
  try {
    fs.writeFileSync(dbPath, JSON.stringify(database, null, 2));
  } catch (error) {
    console.error('Erreur sauvegarde:', error);
  }
}

loadDatabase();

// GET /api/patients
app.get('/api/patients', (req, res) => {
  try {
    let patients = database.patients.filter(p => p.isActive !== false);
    res.json({ patients });
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// POST /api/patients
app.post('/api/patients', (req, res) => {
  try {
    const patient = req.body;
    const newPatient = {
      ...patient,
      id: uuidv4(),
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    database.patients.push(newPatient);
    saveDatabase();

    console.log('✅ Patient créé:', newPatient.firstName, newPatient.lastName);
    res.status(201).json({ patient: newPatient });
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Route de test
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur fonctionnel',
    patients: database.patients?.length || 0,
    appointments: database.appointments?.length || 0
  });
});

function getNetworkAddresses() {
  const interfaces = os.networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name,
          address: interface.address,
          url: `http://${interface.address}:${PORT}`
        });
      }
    }
  }

  return addresses;
}

app.listen(PORT, '0.0.0.0', () => {
  console.log('\n🚀 SERVEUR CABINET DENTAIRE');
  console.log(`📱 Local: http://localhost:${PORT}`);

  const networkAddresses = getNetworkAddresses();
  if (networkAddresses.length > 0) {
    console.log('🌐 Réseau:');
    networkAddresses.forEach(addr => {
      console.log(`   ${addr.name}: ${addr.url}`);
    });
  }

  console.log(`📁 Base: ${dbPath}`);
  console.log('✅ Prêt!\n');
});

process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt...');
  saveDatabase();
  process.exit(0);
});