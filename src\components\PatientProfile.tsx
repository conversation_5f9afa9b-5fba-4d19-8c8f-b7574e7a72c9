import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  ArrowLeft, Phone, Mail, User, Heart, AlertTriangle, Pill,
  Calendar, Plus, Stethoscope, FileText, Clock, Euro,
  Edit, Trash2, CheckCircle, XCircle
} from 'lucide-react';
import { prisma } from '../lib/prisma';
import AppointmentModal from './AppointmentModal';
import ConsultationModal from './ConsultationModal';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Consultation {
  id: string;
  date: Date;
  type: string;
  reason?: string;
  diagnosis?: string;
  treatment?: string;
  observations?: string;
  conclusion?: string;
  cost?: number;
  paid: boolean;
}

interface Treatment {
  id: string;
  name: string;
  type: string;
  tooth?: string;
  status: string;
  cost?: number;
  sessions: number;
  currentSession: number;
  startDate?: Date;
  endDate?: Date;
}

interface RadiotherapyInfo {
  id: string;
  fractionation: number;
  prescribedDose: number;
  rayType: string;
  sessions: number;
  completedSessions: number;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
}

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  notes?: string;
}

const PatientProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('consultations');
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false);
  const [isConsultationModalOpen, setIsConsultationModalOpen] = useState(false);
  const [appointments, setAppointments] = useState<any[]>([]);
  const [consultations, setConsultations] = useState<any[]>([]);

  useEffect(() => {
    if (id) {
      loadPatient(id);
    }
  }, [id]);

  const loadPatient = async (patientId: string) => {
    try {
      setIsLoading(true);
      const data = await prisma.patient.findUnique({
        where: { id: patientId }
      });
      setPatient(data as Patient);

      // Charger les données mock pour les rendez-vous et consultations
      setAppointments([
        {
          id: '1',
          date: new Date('2025-02-10'),
          time: '14:30',
          type: 'Consultation',
          status: 'SCHEDULED',
          notes: 'Contrôle de routine'
        },
        {
          id: '2',
          date: new Date('2025-01-15'),
          time: '10:00',
          type: 'Détartrage',
          status: 'COMPLETED',
          notes: 'Détartrage complet effectué'
        }
      ]);

      setConsultations([
        {
          id: '1',
          date: new Date('2025-01-15'),
          type: 'Détartrage',
          reason: 'Nettoyage de routine',
          diagnosis: 'Tartre modéré',
          treatment: 'Détartrage complet',
          cost: 80,
          paid: true,
          observations: 'Bonne hygiène dentaire générale'
        }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement du patient:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAppointment = async (appointmentData: any) => {
    const newAppointment = {
      id: Date.now().toString(),
      ...appointmentData,
      date: new Date(appointmentData.date),
      status: 'SCHEDULED'
    };
    setAppointments(prev => [newAppointment, ...prev]);
  };

  const handleSaveConsultation = async (consultationData: any) => {
    const newConsultation = {
      id: Date.now().toString(),
      ...consultationData,
      date: new Date()
    };
    setConsultations(prev => [newConsultation, ...prev]);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Patient non trouvé</h2>
          <button
            onClick={() => navigate('/patients')}
            className="text-blue-600 hover:text-blue-800"
          >
            Retour à la liste des patients
          </button>
        </div>
      </div>
    );
  }

  const age = patient.dateOfBirth ? new Date().getFullYear() - patient.dateOfBirth.getFullYear() : null;

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <button
            onClick={() => navigate('/patients')}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour aux patients
          </button>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-xl font-bold text-blue-600">
                    {patient.firstName[0]}{patient.lastName[0]}
                  </span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {patient.firstName} {patient.lastName}
                  </h1>
                  {age && <p className="text-gray-600">{age} ans</p>}
                  <div className="flex items-center mt-2 space-x-4">
                    <div className="flex items-center text-blue-600">
                      <Phone className="w-4 h-4 mr-1" />
                      <span className="text-sm">{patient.phone}</span>
                    </div>
                    {patient.email && (
                      <div className="flex items-center text-gray-600">
                        <Mail className="w-4 h-4 mr-1" />
                        <span className="text-sm">{patient.email}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informations personnelles */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations personnelles</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Date de naissance</span>
                <span className="text-sm font-medium">
                  {patient.dateOfBirth ? patient.dateOfBirth.toLocaleDateString('fr-FR') : '-'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sexe</span>
                <span className="text-sm font-medium">{patient.gender || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Profession</span>
                <span className="text-sm font-medium">{patient.profession || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Groupe sanguin</span>
                <span className="text-sm font-medium">{patient.bloodType || '-'}</span>
              </div>
              {patient.address && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Adresse</span>
                  <span className="text-sm font-medium">{patient.address}</span>
                </div>
              )}
            </div>
          </div>

          {/* Informations médicales */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations médicales</h3>

            {patient.allergies && (
              <div className="mb-4 p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                  <span className="text-sm font-medium text-red-800">Allergies</span>
                </div>
                <p className="text-sm text-red-700">{patient.allergies}</p>
              </div>
            )}

            {patient.medicalHistory && (
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Heart className="w-4 h-4 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-800">Antécédents médicaux</span>
                </div>
                <p className="text-sm text-gray-700">{patient.medicalHistory}</p>
              </div>
            )}

            {patient.medications && (
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Pill className="w-4 h-4 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-800">Médicaments actuels</span>
                </div>
                <p className="text-sm text-gray-700">{patient.medications}</p>
              </div>
            )}

            {patient.notes && (
              <div>
                <div className="flex items-center mb-2">
                  <User className="w-4 h-4 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-800">Notes</span>
                </div>
                <p className="text-sm text-gray-700">{patient.notes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Contact d'urgence */}
        {patient.emergencyContact && (
          <div className="mt-6 bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact d'urgence</h3>
            <div className="space-y-2">
              <p className="text-sm font-medium">{patient.emergencyContact}</p>
              {patient.emergencyPhone && (
                <div className="flex items-center text-gray-600">
                  <Phone className="w-4 h-4 mr-2" />
                  <span className="text-sm">{patient.emergencyPhone}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Actions rapides */}
        <div className="mt-6 bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <button
              onClick={() => setIsAppointmentModalOpen(true)}
              className="inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Calendar className="w-4 h-4 mr-2" />
              Nouveau RDV
            </button>
            <button
              onClick={() => setIsConsultationModalOpen(true)}
              className="inline-flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Stethoscope className="w-4 h-4 mr-2" />
              Nouvelle consultation
            </button>
          </div>
        </div>

        {/* Historique et données */}
        <div className="mt-6 bg-white rounded-xl shadow-sm">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('consultations')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'consultations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <Stethoscope className="w-4 h-4 mr-2" />
                  Consultations ({consultations.length})
                </div>
              </button>
              <button
                onClick={() => setActiveTab('appointments')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'appointments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  Rendez-vous ({appointments.length})
                </div>
              </button>
              <button
                onClick={() => setActiveTab('payments')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'payments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <Euro className="w-4 h-4 mr-2" />
                  Paiements
                </div>
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'consultations' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Historique des consultations</h3>
                  <button
                    onClick={() => setIsConsultationModalOpen(true)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Nouvelle consultation
                  </button>
                </div>

                {consultations.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Stethoscope className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <p>Aucune consultation enregistrée</p>
                  </div>
                ) : (
                  consultations.map((consultation) => (
                    <div key={consultation.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full mr-3 bg-green-500"></div>
                          <span className="font-medium text-gray-900">{consultation.type}</span>
                          <span className="ml-2 text-sm text-gray-500">
                            {consultation.date.toLocaleDateString('fr-FR')}
                          </span>
                        </div>
                        {consultation.cost && (
                          <div className="flex items-center">
                            <span className="text-sm font-medium">{consultation.cost}€</span>
                            {consultation.paid && (
                              <CheckCircle className="w-4 h-4 ml-2 text-green-600" />
                            )}
                          </div>
                        )}
                      </div>

                      {consultation.reason && (
                        <div className="mb-2">
                          <span className="text-sm font-medium text-gray-700">Motif: </span>
                          <span className="text-sm text-gray-600">{consultation.reason}</span>
                        </div>
                      )}

                      {consultation.diagnosis && (
                        <div className="mb-2">
                          <span className="text-sm font-medium text-gray-700">Diagnostic: </span>
                          <span className="text-sm text-gray-600">{consultation.diagnosis}</span>
                        </div>
                      )}

                      {consultation.treatment && (
                        <div className="mb-2">
                          <span className="text-sm font-medium text-gray-700">Traitement: </span>
                          <span className="text-sm text-gray-600">{consultation.treatment}</span>
                        </div>
                      )}

                      {consultation.observations && (
                        <div>
                          <span className="text-sm font-medium text-blue-600">Observations:</span>
                          <p className="text-sm text-gray-600 mt-1">{consultation.observations}</p>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'appointments' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Rendez-vous</h3>
                  <button
                    onClick={() => setIsAppointmentModalOpen(true)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Nouveau rendez-vous
                  </button>
                </div>

                {appointments.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <p>Aucun rendez-vous planifié</p>
                  </div>
                ) : (
                  appointments.map((appointment) => (
                    <div key={appointment.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-3 ${
                            appointment.status === 'SCHEDULED' ? 'bg-blue-500' :
                            appointment.status === 'COMPLETED' ? 'bg-green-500' : 'bg-gray-500'
                          }`}></div>
                          <span className="font-medium text-gray-900">{appointment.type}</span>
                          <span className="ml-2 text-sm text-gray-500">
                            {appointment.date.toLocaleDateString('fr-FR')} à {appointment.time}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            appointment.status === 'SCHEDULED' ? 'bg-blue-100 text-blue-800' :
                            appointment.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {appointment.status === 'SCHEDULED' ? 'Planifié' :
                             appointment.status === 'COMPLETED' ? 'Terminé' : 'Annulé'}
                          </span>
                        </div>
                      </div>

                      {appointment.notes && (
                        <div>
                          <span className="text-sm font-medium text-gray-700">Notes: </span>
                          <span className="text-sm text-gray-600">{appointment.notes}</span>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'payments' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Historique des paiements</h3>
                </div>

                <div className="text-center py-8 text-gray-500">
                  <Euro className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p>Aucun paiement enregistré</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      <AppointmentModal
        isOpen={isAppointmentModalOpen}
        onClose={() => setIsAppointmentModalOpen(false)}
        onSave={handleSaveAppointment}
        patientId={patient?.id}
        patientName={`${patient?.firstName} ${patient?.lastName}`}
      />

      <ConsultationModal
        isOpen={isConsultationModalOpen}
        onClose={() => setIsConsultationModalOpen(false)}
        onSave={handleSaveConsultation}
        patientId={patient?.id}
        patientName={`${patient?.firstName} ${patient?.lastName}`}
      />
    </div>
  );
};

export default PatientProfile;
