import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Phone,
  Mail,
  User,
  Heart,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Pill,
  Edit,
  Plus,
  Stethoscope,
  Tooth,
  CreditCard,
  Zap,
  Target,
  Activity
} from 'lucide-react';
import { format, differenceInYears } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Consultation {
  id: string;
  date: Date;
  type: string;
  reason?: string;
  diagnosis?: string;
  treatment?: string;
  observations?: string;
  conclusion?: string;
  cost?: number;
  paid: boolean;
}

interface Treatment {
  id: string;
  name: string;
  type: string;
  tooth?: string;
  status: string;
  cost?: number;
  sessions: number;
  currentSession: number;
  startDate?: Date;
  endDate?: Date;
}

interface RadiotherapyInfo {
  id: string;
  fractionation: number;
  prescribedDose: number;
  rayType: string;
  sessions: number;
  completedSessions: number;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
}

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  notes?: string;
}

interface Consultation {
  id: string;
  date: Date;
  type: string;
  reason?: string;
  diagnosis?: string;
  treatment?: string;
  observations?: string;
  conclusion?: string;
  cost?: number;
  paid: boolean;
}

const PatientProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('consultations');

  useEffect(() => {
    if (id) {
      loadPatientData(id);
    }
  }, [id]);

  const loadPatientData = async (patientId: string) => {
    try {
      setIsLoading(true);

      // Données mock pour le patient
      const mockPatient: Patient = {
        id: patientId,
        firstName: 'HOCINE',
        lastName: 'BOUKERCHE MANSOUR',
        phone: '**********',
        email: '<EMAIL>',
        dateOfBirth: new Date('1945-04-01'),
        gender: 'Homme',
        address: '123 Rue de la Paix',
        city: 'Alger',
        profession: 'Retraité',
        emergencyContact: 'Fatima Boukerche',
        emergencyPhone: '**********',
        medicalHistory: 'Hypertension artérielle, Diabète type 2',
        allergies: 'Pénicilline',
        medications: 'Metformine 500mg, Lisinopril 10mg',
        bloodType: 'O+',
        notes: 'Patient coopératif, sensible'
      };

      const mockConsultations: Consultation[] = [
        {
          id: '1',
          date: new Date('2025-02-05'),
          type: 'CONSULTATION',
          reason: 'Douleur dentaire',
          diagnosis: 'Carie profonde molaire 16',
          treatment: 'Obturation composite',
          observations: 'Patient en bon état général. Tolérance au traitement satisfaisante.',
          conclusion: 'Consultation positive, prochaine évaluation prévue.',
          cost: 120,
          paid: true
        }
      ];

      setPatient(mockPatient);
      setConsultations(mockConsultations);
    } catch (error) {
      console.error('Erreur lors du chargement des données patient:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Patient non trouvé</h2>
          <button
            onClick={() => navigate('/patients')}
            className="text-blue-600 hover:text-blue-800"
          >
            Retour à la liste des patients
          </button>
        </div>
      </div>
    );
  }

  const age = patient.dateOfBirth ? differenceInYears(new Date(), patient.dateOfBirth) : null;

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate('/patients')}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour aux patients
          </button>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-xl font-bold text-blue-600">
                    {patient.firstName[0]}{patient.lastName[0]}
                  </span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    MR. {patient.lastName} {patient.firstName}
                  </h1>
                  <p className="text-gray-600">{age} ANS</p>
                  <div className="flex items-center mt-2 space-x-4">
                    <div className="flex items-center text-blue-600">
                      <Phone className="w-4 h-4 mr-1" />
                      <span className="text-sm">{patient.phone}</span>
                    </div>
                    {patient.email && (
                      <div className="flex items-center text-gray-600">
                        <Mail className="w-4 h-4 mr-1" />
                        <span className="text-sm">{patient.email}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Edit className="w-4 h-4 mr-2" />
                Modifier
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informations importantes - Gauche */}
          <div className="lg:col-span-1 space-y-6">
            {/* Informations personnelles */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations personnelles</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Date de naissance</span>
                  <span className="text-sm font-medium">
                    {patient.dateOfBirth ? format(patient.dateOfBirth, 'dd/MM/yyyy', { locale: fr }) : '-'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Sexe</span>
                  <span className="text-sm font-medium">{patient.gender || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Profession</span>
                  <span className="text-sm font-medium">{patient.profession || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Groupe sanguin</span>
                  <span className="text-sm font-medium">{patient.bloodType || '-'}</span>
                </div>
              </div>
            </div>

            {/* Informations médicales */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations médicales</h3>

              {patient.allergies && (
                <div className="mb-4 p-3 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center mb-2">
                    <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                    <span className="text-sm font-medium text-red-800">Allergies</span>
                  </div>
                  <p className="text-sm text-red-700">{patient.allergies}</p>
                </div>
              )}

              {patient.medicalHistory && (
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Heart className="w-4 h-4 text-gray-600 mr-2" />
                    <span className="text-sm font-medium text-gray-800">Antécédents médicaux</span>
                  </div>
                  <p className="text-sm text-gray-700">{patient.medicalHistory}</p>
                </div>
              )}

              {patient.medications && (
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Pill className="w-4 h-4 text-gray-600 mr-2" />
                    <span className="text-sm font-medium text-gray-800">Médicaments actuels</span>
                  </div>
                  <p className="text-sm text-gray-700">{patient.medications}</p>
                </div>
              )}
            </div>

            {/* Informations radiothérapie */}
            <div className="bg-blue-50 rounded-xl shadow-sm p-6 border border-blue-200">
              <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                <Zap className="w-5 h-5 mr-2" />
                Informations radiothérapie
              </h3>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="bg-white rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Target className="w-4 h-4 text-blue-600" />
                  </div>
                  <p className="text-xs text-gray-600 mb-1">Fractionnement</p>
                  <p className="text-lg font-bold text-blue-600">2 Gy/séance</p>
                  <p className="text-xs text-gray-500">30 fractions</p>
                </div>

                <div className="bg-white rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Activity className="w-4 h-4 text-blue-600" />
                  </div>
                  <p className="text-xs text-gray-600 mb-1">Dose Prescrite</p>
                  <p className="text-lg font-bold text-blue-600">60 Gy</p>
                  <p className="text-xs text-gray-500">30 fractions</p>
                </div>

                <div className="bg-white rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Zap className="w-4 h-4 text-blue-600" />
                  </div>
                  <p className="text-xs text-gray-600 mb-1">Type de rayonnement</p>
                  <p className="text-sm font-bold text-blue-600">Photons (X)</p>
                  <p className="text-xs text-gray-500">4 fractions</p>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-800">Notes du médecin</span>
                  <span className="text-xs text-blue-600">05/02/2025</span>
                </div>

                <div className="mb-3">
                  <p className="text-sm text-blue-700 font-medium mb-1">observations du médecin:</p>
                  <p className="text-sm text-blue-600">
                    Patient en bon état général. Tolérance au traitement satisfaisante,
                    absence d'effets secondaires majeurs. Bonne évolution clinique,
                    poursuite du protocole sans modification.
                  </p>
                </div>

                <div>
                  <p className="text-sm text-blue-700 font-medium mb-1">Conclusion:</p>
                  <p className="text-sm text-blue-600">
                    Consultation positive, prochaine évaluation prévue selon le suivi habituel.
                  </p>
                </div>
              </div>
            </div>

            {/* Contact d'urgence */}
            {patient.emergencyContact && (
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact d'urgence</h3>
                <div className="space-y-2">
                  <p className="text-sm font-medium">{patient.emergencyContact}</p>
                  {patient.emergencyPhone && (
                    <div className="flex items-center text-gray-600">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="text-sm">{patient.emergencyPhone}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Historique - Droite */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm">
              {/* Tabs */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  <button
                    onClick={() => setActiveTab('consultations')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'consultations'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <Stethoscope className="w-4 h-4 mr-2" />
                      Consultations
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('treatments')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'treatments'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <Tooth className="w-4 h-4 mr-2" />
                      Traitements
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('payments')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'payments'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <CreditCard className="w-4 h-4 mr-2" />
                      Paiements
                    </div>
                  </button>
                </nav>
              </div>

              {/* Content */}
              <div className="p-6">
                {activeTab === 'consultations' && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Historique des consultations</h3>
                      <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <Plus className="w-4 h-4 mr-2" />
                        Nouvelle consultation
                      </button>
                    </div>

                    {consultations.map((consultation) => (
                      <div key={consultation.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full mr-3 bg-blue-500"></div>
                            <span className="font-medium text-gray-900">{consultation.type}</span>
                            <span className="ml-2 text-sm text-gray-500">
                              {format(consultation.date, 'dd/MM/yyyy', { locale: fr })}
                            </span>
                          </div>
                          {consultation.cost && (
                            <div className="flex items-center">
                              <span className="text-sm font-medium">{consultation.cost}€</span>
                              {consultation.paid && (
                                <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                  Payé
                                </span>
                              )}
                            </div>
                          )}
                        </div>

                        {consultation.reason && (
                          <div className="mb-2">
                            <span className="text-sm font-medium text-gray-700">Motif: </span>
                            <span className="text-sm text-gray-600">{consultation.reason}</span>
                          </div>
                        )}

                        {consultation.diagnosis && (
                          <div className="mb-2">
                            <span className="text-sm font-medium text-gray-700">Diagnostic: </span>
                            <span className="text-sm text-gray-600">{consultation.diagnosis}</span>
                          </div>
                        )}

                        {consultation.observations && (
                          <div className="mb-2">
                            <span className="text-sm font-medium text-blue-600">Observations du médecin:</span>
                            <p className="text-sm text-gray-600 mt-1">{consultation.observations}</p>
                          </div>
                        )}

                        {consultation.conclusion && (
                          <div>
                            <span className="text-sm font-medium text-blue-600">Conclusion:</span>
                            <p className="text-sm text-gray-600 mt-1">{consultation.conclusion}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {activeTab === 'treatments' && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Traitements</h3>
                      <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <Plus className="w-4 h-4 mr-2" />
                        Nouveau traitement
                      </button>
                    </div>

                    <div className="text-center py-8 text-gray-500">
                      <Tooth className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                      <p>Aucun traitement enregistré</p>
                    </div>
                  </div>
                )}

                {activeTab === 'payments' && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Historique des paiements</h3>
                      <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <Plus className="w-4 h-4 mr-2" />
                        Nouveau paiement
                      </button>
                    </div>

                    <div className="text-center py-8 text-gray-500">
                      <CreditCard className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                      <p>Aucun paiement enregistré</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientProfile;
