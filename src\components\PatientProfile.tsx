import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Phone, Mail, User, Heart, AlertTriangle, Pill } from 'lucide-react';
import { prisma } from '../lib/prisma';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Consultation {
  id: string;
  date: Date;
  type: string;
  reason?: string;
  diagnosis?: string;
  treatment?: string;
  observations?: string;
  conclusion?: string;
  cost?: number;
  paid: boolean;
}

interface Treatment {
  id: string;
  name: string;
  type: string;
  tooth?: string;
  status: string;
  cost?: number;
  sessions: number;
  currentSession: number;
  startDate?: Date;
  endDate?: Date;
}

interface RadiotherapyInfo {
  id: string;
  fractionation: number;
  prescribedDose: number;
  rayType: string;
  sessions: number;
  completedSessions: number;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
}

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  notes?: string;
}

const PatientProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadPatient(id);
    }
  }, [id]);

  const loadPatient = async (patientId: string) => {
    try {
      setIsLoading(true);
      const data = await prisma.patient.findUnique({
        where: { id: patientId }
      });
      setPatient(data);
    } catch (error) {
      console.error('Erreur lors du chargement du patient:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Patient non trouvé</h2>
          <button
            onClick={() => navigate('/patients')}
            className="text-blue-600 hover:text-blue-800"
          >
            Retour à la liste des patients
          </button>
        </div>
      </div>
    );
  }

  const age = patient.dateOfBirth ? new Date().getFullYear() - patient.dateOfBirth.getFullYear() : null;

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <button
            onClick={() => navigate('/patients')}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour aux patients
          </button>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-xl font-bold text-blue-600">
                    {patient.firstName[0]}{patient.lastName[0]}
                  </span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {patient.firstName} {patient.lastName}
                  </h1>
                  {age && <p className="text-gray-600">{age} ans</p>}
                  <div className="flex items-center mt-2 space-x-4">
                    <div className="flex items-center text-blue-600">
                      <Phone className="w-4 h-4 mr-1" />
                      <span className="text-sm">{patient.phone}</span>
                    </div>
                    {patient.email && (
                      <div className="flex items-center text-gray-600">
                        <Mail className="w-4 h-4 mr-1" />
                        <span className="text-sm">{patient.email}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informations personnelles */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations personnelles</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Date de naissance</span>
                <span className="text-sm font-medium">
                  {patient.dateOfBirth ? patient.dateOfBirth.toLocaleDateString('fr-FR') : '-'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sexe</span>
                <span className="text-sm font-medium">{patient.gender || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Profession</span>
                <span className="text-sm font-medium">{patient.profession || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Groupe sanguin</span>
                <span className="text-sm font-medium">{patient.bloodType || '-'}</span>
              </div>
              {patient.address && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Adresse</span>
                  <span className="text-sm font-medium">{patient.address}</span>
                </div>
              )}
            </div>
          </div>

          {/* Informations médicales */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations médicales</h3>

            {patient.allergies && (
              <div className="mb-4 p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                  <span className="text-sm font-medium text-red-800">Allergies</span>
                </div>
                <p className="text-sm text-red-700">{patient.allergies}</p>
              </div>
            )}

            {patient.medicalHistory && (
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Heart className="w-4 h-4 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-800">Antécédents médicaux</span>
                </div>
                <p className="text-sm text-gray-700">{patient.medicalHistory}</p>
              </div>
            )}

            {patient.medications && (
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Pill className="w-4 h-4 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-800">Médicaments actuels</span>
                </div>
                <p className="text-sm text-gray-700">{patient.medications}</p>
              </div>
            )}

            {patient.notes && (
              <div>
                <div className="flex items-center mb-2">
                  <User className="w-4 h-4 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-800">Notes</span>
                </div>
                <p className="text-sm text-gray-700">{patient.notes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Contact d'urgence */}
        {patient.emergencyContact && (
          <div className="mt-6 bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact d'urgence</h3>
            <div className="space-y-2">
              <p className="text-sm font-medium">{patient.emergencyContact}</p>
              {patient.emergencyPhone && (
                <div className="flex items-center text-gray-600">
                  <Phone className="w-4 h-4 mr-2" />
                  <span className="text-sm">{patient.emergencyPhone}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientProfile;
