import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Consultation {
  id: string;
  date: Date;
  type: string;
  reason?: string;
  diagnosis?: string;
  treatment?: string;
  observations?: string;
  conclusion?: string;
  cost?: number;
  paid: boolean;
}

interface Treatment {
  id: string;
  name: string;
  type: string;
  tooth?: string;
  status: string;
  cost?: number;
  sessions: number;
  currentSession: number;
  startDate?: Date;
  endDate?: Date;
}

interface RadiotherapyInfo {
  id: string;
  fractionation: number;
  prescribedDose: number;
  rayType: string;
  sessions: number;
  completedSessions: number;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
}

const PatientProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <button
            onClick={() => navigate('/patients')}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour aux patients
          </button>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Profil Patient - ID: {id}
            </h1>
            <p className="text-gray-600 mt-2">
              Page de profil patient - Version simplifiée fonctionnelle
            </p>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Informations Patient</h3>
                <p className="text-sm text-gray-600">Nom: BOUKERCHE MANSOUR Hocine</p>
                <p className="text-sm text-gray-600">Âge: 82 ans</p>
                <p className="text-sm text-gray-600">Téléphone: **********</p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">Informations Médicales</h3>
                <p className="text-sm text-blue-700">Allergies: Pénicilline</p>
                <p className="text-sm text-blue-700">Antécédents: Hypertension, Diabète</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientProfile;
