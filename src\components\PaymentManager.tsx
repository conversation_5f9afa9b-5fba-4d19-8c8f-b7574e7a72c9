import React, { useState, useEffect } from 'react';
import { X, Plus, CreditCard, Banknote, Smartphone, FileText, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { database, PaymentData, PaymentSummary } from '../lib/database';

interface PaymentManagerProps {
  isOpen: boolean;
  onClose: () => void;
  consultationId: string;
  patientName: string;
  onPaymentAdded: () => void;
}

const PaymentManager: React.FC<PaymentManagerProps> = ({
  isOpen,
  onClose,
  consultationId,
  patientName,
  onPaymentAdded
}) => {
  const [payments, setPayments] = useState<PaymentData[]>([]);
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  const [newPaymentAmount, setNewPaymentAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'CASH' | 'CARD' | 'TRANSFER' | 'CHECK'>('CASH');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (isOpen && consultationId) {
      loadPaymentData();
    }
  }, [isOpen, consultationId]);

  const loadPaymentData = () => {
    const paymentList = database.getPaymentsByConsultation(consultationId);
    const paymentSummary = database.getPaymentSummary(consultationId);
    setPayments(paymentList);
    setSummary(paymentSummary);
  };

  const handleAddPayment = async () => {
    if (!newPaymentAmount || parseFloat(newPaymentAmount) <= 0) {
      setMessage({ type: 'error', text: 'Veuillez saisir un montant valide' });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const result = database.addPaymentToConsultation(
        consultationId,
        parseFloat(newPaymentAmount),
        paymentMethod
      );

      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        setNewPaymentAmount('');
        loadPaymentData();
        onPaymentAdded();
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors de l\'ajout du paiement' });
    } finally {
      setIsLoading(false);
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'CASH': return <Banknote className="w-4 h-4" />;
      case 'CARD': return <CreditCard className="w-4 h-4" />;
      case 'TRANSFER': return <Smartphone className="w-4 h-4" />;
      case 'CHECK': return <FileText className="w-4 h-4" />;
      default: return <Banknote className="w-4 h-4" />;
    }
  };

  const getMethodLabel = (method: string) => {
    switch (method) {
      case 'CASH': return 'Espèces';
      case 'CARD': return 'Carte';
      case 'TRANSFER': return 'Virement';
      case 'CHECK': return 'Chèque';
      default: return method;
    }
  };

  const getStatusIcon = () => {
    if (!summary) return <Clock className="w-5 h-5 text-gray-400" />;
    
    if (summary.isFullyPaid) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    } else if (summary.totalPaid > 0) {
      return <Clock className="w-5 h-5 text-orange-500" />;
    } else {
      return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = () => {
    if (!summary) return 'Chargement...';
    
    if (summary.isFullyPaid) {
      return 'Entièrement payé';
    } else if (summary.totalPaid > 0) {
      return 'Partiellement payé';
    } else {
      return 'Non payé';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <CreditCard className="w-5 h-5 text-gray-600 mr-3" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Gestion des paiements</h2>
              <p className="text-sm text-gray-500">{patientName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Résumé financier */}
        {summary && (
          <div className="p-6 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900">Résumé financier</h3>
              <div className="flex items-center space-x-2">
                {getStatusIcon()}
                <span className="text-sm font-medium">{getStatusText()}</span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {summary.totalAmount.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Total DA</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {summary.totalPaid.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Payé DA</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {summary.remainingAmount.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Reste DA</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {summary.paymentPercentage.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">Progression</div>
              </div>
            </div>

            {/* Barre de progression */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${summary.paymentPercentage}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Ajouter un paiement */}
        {summary && !summary.isFullyPaid && (
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-4">Ajouter un paiement</h3>
            
            {message && (
              <div className={`mb-4 p-3 rounded-lg ${
                message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
              }`}>
                {message.text}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Montant (DA)
                </label>
                <input
                  type="number"
                  value={newPaymentAmount}
                  onChange={(e) => setNewPaymentAmount(e.target.value)}
                  step="100"
                  min="0"
                  max={summary.remainingAmount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={`Max: ${summary.remainingAmount.toLocaleString()}`}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Méthode
                </label>
                <select
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="CASH">Espèces</option>
                  <option value="CARD">Carte bancaire</option>
                  <option value="TRANSFER">Virement</option>
                  <option value="CHECK">Chèque</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={handleAddPayment}
                  disabled={isLoading || !newPaymentAmount}
                  className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Plus className="w-4 h-4 mr-2" />
                  )}
                  Ajouter
                </button>
              </div>
            </div>

            {/* Boutons rapides */}
            <div className="flex space-x-2 mt-3">
              <button
                onClick={() => setNewPaymentAmount(summary.remainingAmount.toString())}
                className="px-3 py-1 bg-green-100 text-green-700 text-sm rounded hover:bg-green-200 transition-colors"
              >
                Solde complet
              </button>
              <button
                onClick={() => setNewPaymentAmount((summary.remainingAmount / 2).toString())}
                className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded hover:bg-blue-200 transition-colors"
              >
                50%
              </button>
              <button
                onClick={() => setNewPaymentAmount((summary.remainingAmount / 3).toString())}
                className="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded hover:bg-purple-200 transition-colors"
              >
                33%
              </button>
            </div>
          </div>
        )}

        {/* Historique des paiements */}
        <div className="p-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4">
            Historique des paiements ({payments.length})
          </h3>
          
          {payments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CreditCard className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p>Aucun paiement enregistré</p>
            </div>
          ) : (
            <div className="space-y-3">
              {payments.map((payment, index) => (
                <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      {getMethodIcon(payment.method)}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {payment.amount.toLocaleString()} DA
                      </div>
                      <div className="text-sm text-gray-500">
                        {getMethodLabel(payment.method)} • {payment.date.toLocaleDateString('fr-FR')}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      Paiement #{payments.length - index}
                    </div>
                    <div className="text-xs text-gray-500">
                      {payment.date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentManager;
