# 🦷 Installation - Cabinet Dentaire

## 📦 Package complet prêt à déployer

### Structure livrée au client :
```
dental-cabinet-app/
├── backend/                 # Serveur API
├── frontend/               # Interface utilisateur  
├── package.json           # Scripts de démarrage
├── README.md              # Documentation
└── start.bat              # Démarrage Windows (optionnel)
```

## 🚀 Installation en 3 étapes

### 1. Prérequis
Installer Node.js depuis https://nodejs.org (version 18 ou plus récente)

### 2. Installation
```bash
# Ouvrir un terminal dans le dossier de l'application
cd dental-cabinet-app

# Installer toutes les dépendances
npm run install:all
```

### 3. Démarrage
```bash
# Démarrer l'application
npm run dev
```

## 🌐 Accès à l'application

### Sur le PC principal
- Ouvrir http://localhost:3001 dans le navigateur

### Depuis d'autres appareils (téléphone, tablette, autre PC)
Au démarrage, l'application affiche les adresses réseau :
```
🌐 Accès réseau:
   WiFi: http://*************:3001
```

Utiliser cette adresse sur les autres appareils du même réseau WiFi.

## 👤 Première connexion
- **Email** : <EMAIL>  
- **Mot de passe** : admin123

## 📱 Utilisation

L'application fonctionne sur :
- ✅ PC Windows/Mac/Linux
- ✅ Téléphones (Android/iPhone)
- ✅ Tablettes
- ✅ Tous navigateurs modernes

## 💾 Données

Toutes les données sont stockées localement dans :
`backend/database/dental_cabinet.db`

**Important** : Sauvegarder ce fichier régulièrement !

## 🛑 Arrêt de l'application

Dans le terminal, appuyer sur `Ctrl+C` pour arrêter le serveur.

## 🔄 Redémarrage automatique

Pour redémarrer automatiquement au démarrage du PC, créer un raccourci du script de démarrage dans le dossier de démarrage Windows.

---

**✅ L'application est maintenant prête à l'utilisation !**
