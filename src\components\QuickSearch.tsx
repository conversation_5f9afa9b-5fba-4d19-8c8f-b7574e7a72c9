import React, { useState, useEffect } from 'react';
import {
  Search,
  Phone,
  Calendar,
  Mail,
  MapPin,
  FileText,
  AlertCircle,
  Plus,
  Edit
} from 'lucide-react';
import { prisma } from '../lib/prisma';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  address?: string;
  medicalHistory?: string;
  allergies?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const QuickSearch: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  useEffect(() => {
    // Charger les recherches récentes depuis localStorage
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    if (searchTerm.length >= 2) {
      performSearch();
    } else {
      setSearchResults([]);
      setSelectedPatient(null);
    }
  }, [searchTerm]);

  const performSearch = async () => {
    setIsLoading(true);
    try {
      // Simuler une recherche dans la base de données
      const allPatients = await prisma.patient.findMany();
      const results = allPatients.filter(patient =>
        patient.phone.includes(searchTerm) ||
        `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSearchResults(results);
      
      // Si un seul résultat et recherche par téléphone exact, sélectionner automatiquement
      if (results.length === 1 && results[0].phone === searchTerm) {
        setSelectedPatient(results[0]);
        addToRecentSearches(searchTerm);
      }
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addToRecentSearches = (term: string) => {
    const updated = [term, ...recentSearches.filter(s => s !== term)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  const selectPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    addToRecentSearches(patient.phone);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setSearchResults([]);
    setSelectedPatient(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">Recherche rapide</h1>
        <p className="mt-2 text-sm text-gray-700">
          Trouvez rapidement un patient par téléphone, nom ou email
        </p>
      </div>

      {/* Search Bar */}
      <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400" />
          <input
            type="text"
            placeholder="Tapez un numéro de téléphone, nom ou email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            autoFocus
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}
        </div>

        {/* Recent Searches */}
        {!searchTerm && recentSearches.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Recherches récentes :</p>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((term, index) => (
                <button
                  key={index}
                  onClick={() => setSearchTerm(term)}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors"
                >
                  {term}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Loading */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && !selectedPatient && (
        <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Résultats de recherche ({searchResults.length})
            </h3>
          </div>
          <div className="divide-y divide-gray-200">
            {searchResults.map((patient) => (
              <div
                key={patient.id}
                onClick={() => selectPatient(patient)}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-medium text-blue-600">
                        {patient.firstName[0]}{patient.lastName[0]}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">
                        {patient.firstName} {patient.lastName}
                      </h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          <Phone className="w-4 h-4 mr-1" />
                          {patient.phone}
                        </span>
                        {patient.email && (
                          <span className="flex items-center">
                            <Mail className="w-4 h-4 mr-1" />
                            {patient.email}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    Cliquez pour voir le dossier
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Selected Patient Details */}
      {selectedPatient && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Patient Info */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xl font-medium text-blue-600">
                      {selectedPatient.firstName[0]}{selectedPatient.lastName[0]}
                    </span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                      {selectedPatient.firstName} {selectedPatient.lastName}
                    </h2>
                    <p className="text-gray-500">
                      Patient depuis {format(new Date(selectedPatient.createdAt), 'MMMM yyyy', { locale: fr })}
                    </p>
                  </div>
                </div>
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                  <Edit className="w-4 h-4 mr-2" />
                  Modifier
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Informations de contact</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Phone className="w-5 h-5 text-gray-400 mr-3" />
                      <span className="text-gray-900">{selectedPatient.phone}</span>
                    </div>
                    {selectedPatient.email && (
                      <div className="flex items-center">
                        <Mail className="w-5 h-5 text-gray-400 mr-3" />
                        <span className="text-gray-900">{selectedPatient.email}</span>
                      </div>
                    )}
                    {selectedPatient.address && (
                      <div className="flex items-start">
                        <MapPin className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                        <span className="text-gray-900">{selectedPatient.address}</span>
                      </div>
                    )}
                    {selectedPatient.dateOfBirth && (
                      <div className="flex items-center">
                        <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                        <span className="text-gray-900">
                          {format(new Date(selectedPatient.dateOfBirth), 'dd MMMM yyyy', { locale: fr })} 
                          ({new Date().getFullYear() - new Date(selectedPatient.dateOfBirth).getFullYear()} ans)
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Informations médicales</h3>
                  <div className="space-y-3">
                    {selectedPatient.medicalHistory && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Antécédents médicaux</p>
                        <p className="text-gray-900">{selectedPatient.medicalHistory}</p>
                      </div>
                    )}
                    {selectedPatient.allergies && (
                      <div>
                        <p className="text-sm font-medium text-gray-700 flex items-center">
                          <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                          Allergies
                        </p>
                        <p className="text-red-700 font-medium">{selectedPatient.allergies}</p>
                      </div>
                    )}
                    {selectedPatient.notes && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Notes</p>
                        <p className="text-gray-900">{selectedPatient.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <Plus className="w-5 h-5 mr-2" />
                  Nouveau rendez-vous
                </button>
                <button className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  <FileText className="w-5 h-5 mr-2" />
                  Nouvelle consultation
                </button>
                <button className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  <Phone className="w-5 h-5 mr-2" />
                  Appeler le patient
                </button>
              </div>
            </div>

            <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Dernières consultations</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">Contrôle de routine</span>
                    <span className="text-xs text-gray-500">Il y a 2 mois</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">Détartrage effectué</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">Traitement carie</span>
                    <span className="text-xs text-gray-500">Il y a 6 mois</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">Obturation composite</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* No Results */}
      {searchTerm.length >= 2 && searchResults.length === 0 && !isLoading && (
        <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-12 text-center">
          <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun patient trouvé</h3>
          <p className="text-gray-500 mb-4">
            Aucun patient ne correspond à votre recherche "{searchTerm}"
          </p>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="w-4 h-4 mr-2" />
            Créer un nouveau patient
          </button>
        </div>
      )}
    </div>
  );
};

export default QuickSearch;
