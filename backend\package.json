{"name": "dental-cabinet-backend", "version": "1.0.0", "description": "Backend API pour cabinet dentaire", "main": "dist/app.js", "scripts": {"dev": "node src/app.js", "start": "node src/app.js", "build": "mkdir -p dist && cp -r src/* dist/ && cp -r ../frontend/dist dist/public", "build:win": "if not exist dist mkdir dist && xcopy /E /I src dist\\src && if exist ..\\frontend\\dist xcopy /E /I ..\\frontend\\dist dist\\public"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}