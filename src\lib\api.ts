// Pour l'instant, on utilise une approche mock côté client
// En production, ceci serait remplacé par des appels API REST

// Types locaux pour éviter les problèmes d'import côté client
export type Gender = 'HOMME' | 'FEMME' | 'AUTRE';

// Types pour les patients
export interface PatientData {
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
}

// Données mock pour les patients
let mockPatients: (PatientData & { id: string; isActive: boolean; createdAt: Date; updatedAt: Date })[] = [
  {
    id: '1',
    firstName: 'Jean',
    lastName: 'Dupont',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1980-05-15'),
    gender: 'HOMME',
    address: '123 Rue de la Paix',
    city: 'Paris',
    postalCode: '75001',
    profession: 'Ingénieur',
    emergencyContact: 'Sophie Dupont',
    emergencyPhone: '**********',
    medicalHistory: 'Aucun antécédent particulier',
    allergies: 'Aucune allergie connue',
    bloodType: 'A+',
    insuranceNumber: '1234567890123',
    insuranceProvider: 'Sécurité Sociale',
    notes: 'Patient régulier, très ponctuel',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    firstName: 'Marie',
    lastName: 'Martin',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1975-12-03'),
    gender: 'FEMME',
    address: '456 Avenue des Champs',
    city: 'Paris',
    postalCode: '75008',
    profession: 'Professeure',
    emergencyContact: 'Paul Martin',
    emergencyPhone: '**********',
    medicalHistory: 'Hypertension artérielle',
    allergies: 'Allergie à la pénicilline',
    medications: 'Lisinopril 10mg',
    bloodType: 'B+',
    insuranceNumber: '2345678901234',
    insuranceProvider: 'Mutuelle Générale',
    notes: 'Préfère les rendez-vous le matin',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '3',
    firstName: 'Hocine',
    lastName: 'Boukerche Mansour',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1945-04-01'),
    gender: 'HOMME',
    address: '123 Rue de la Paix',
    city: 'Alger',
    postalCode: '16000',
    profession: 'Retraité',
    emergencyContact: 'Fatima Boukerche',
    emergencyPhone: '**********',
    medicalHistory: 'Hypertension artérielle, Diabète type 2',
    allergies: 'Pénicilline',
    medications: 'Metformine 500mg, Lisinopril 10mg',
    bloodType: 'O+',
    insuranceNumber: '1234567890123',
    insuranceProvider: 'CNAS',
    notes: 'Patient coopératif, sensible',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// API pour les patients (version mock)
export const patientAPI = {
  // Récupérer tous les patients
  async getAll() {
    return new Promise(resolve => {
      setTimeout(() => resolve(mockPatients), 100);
    });
  },

  // Récupérer un patient par ID
  async getById(id: string) {
    return new Promise(resolve => {
      setTimeout(() => {
        const patient = mockPatients.find(p => p.id === id);
        resolve(patient || null);
      }, 100);
    });
  },

  // Créer un nouveau patient
  async create(data: PatientData) {
    return new Promise(resolve => {
      setTimeout(() => {
        const newPatient = {
          ...data,
          id: Date.now().toString(),
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        mockPatients.push(newPatient);
        resolve(newPatient);
      }, 100);
    });
  },

  // Mettre à jour un patient
  async update(id: string, data: Partial<PatientData>) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockPatients.findIndex(p => p.id === id);
        if (index !== -1) {
          mockPatients[index] = {
            ...mockPatients[index],
            ...data,
            updatedAt: new Date()
          };
          resolve(mockPatients[index]);
        } else {
          throw new Error('Patient non trouvé');
        }
      }, 100);
    });
  },

  // Supprimer un patient
  async delete(id: string) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockPatients.findIndex(p => p.id === id);
        if (index !== -1) {
          const deleted = mockPatients.splice(index, 1)[0];
          resolve(deleted);
        } else {
          throw new Error('Patient non trouvé');
        }
      }, 100);
    });
  },

  // Rechercher des patients
  async search(query: string) {
    return new Promise(resolve => {
      setTimeout(() => {
        const filtered = mockPatients.filter(patient =>
          `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(query.toLowerCase()) ||
          patient.phone.includes(query) ||
          patient.email?.toLowerCase().includes(query.toLowerCase())
        );
        resolve(filtered);
      }, 100);
    });
  }
};

// API pour les consultations (version mock)
export const consultationAPI = {
  async create(data: {
    patientId: string;
    type: string;
    reason?: string;
    symptoms?: string;
    diagnosis?: string;
    treatment?: string;
    prescription?: string;
    observations?: string;
    conclusion?: string;
    cost?: number;
    paid?: boolean;
  }) {
    return new Promise(resolve => {
      setTimeout(() => {
        const consultation = {
          id: Date.now().toString(),
          ...data,
          date: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        };
        resolve(consultation);
      }, 100);
    });
  }
};
