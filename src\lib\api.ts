import { PrismaClient, Gender } from '../generated/prisma';

const prisma = new PrismaClient();

// Types pour les patients
export interface PatientData {
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
}

// API pour les patients
export const patientAPI = {
  // Récupérer tous les patients
  async getAll() {
    try {
      return await prisma.patient.findMany({
        orderBy: { createdAt: 'desc' }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des patients:', error);
      throw error;
    }
  },

  // Récupérer un patient par ID
  async getById(id: string) {
    try {
      return await prisma.patient.findUnique({
        where: { id },
        include: {
          consultations: {
            orderBy: { date: 'desc' }
          },
          treatments: {
            orderBy: { createdAt: 'desc' }
          },
          payments: {
            orderBy: { date: 'desc' }
          }
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du patient:', error);
      throw error;
    }
  },

  // Créer un nouveau patient
  async create(data: PatientData) {
    try {
      return await prisma.patient.create({
        data: {
          ...data,
          isActive: true
        }
      });
    } catch (error) {
      console.error('Erreur lors de la création du patient:', error);
      throw error;
    }
  },

  // Mettre à jour un patient
  async update(id: string, data: Partial<PatientData>) {
    try {
      return await prisma.patient.update({
        where: { id },
        data
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du patient:', error);
      throw error;
    }
  },

  // Supprimer un patient
  async delete(id: string) {
    try {
      return await prisma.patient.delete({
        where: { id }
      });
    } catch (error) {
      console.error('Erreur lors de la suppression du patient:', error);
      throw error;
    }
  },

  // Rechercher des patients
  async search(query: string) {
    try {
      return await prisma.patient.findMany({
        where: {
          OR: [
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query } },
            { email: { contains: query, mode: 'insensitive' } }
          ]
        },
        orderBy: { createdAt: 'desc' }
      });
    } catch (error) {
      console.error('Erreur lors de la recherche de patients:', error);
      throw error;
    }
  }
};

// API pour les consultations
export const consultationAPI = {
  async create(data: {
    patientId: string;
    type: string;
    reason?: string;
    symptoms?: string;
    diagnosis?: string;
    treatment?: string;
    prescription?: string;
    observations?: string;
    conclusion?: string;
    cost?: number;
    paid?: boolean;
  }) {
    try {
      return await prisma.consultation.create({
        data
      });
    } catch (error) {
      console.error('Erreur lors de la création de la consultation:', error);
      throw error;
    }
  }
};

// Initialiser la base de données avec des données de test
export const initializeDatabase = async () => {
  try {
    // Vérifier s'il y a déjà des utilisateurs
    const userCount = await prisma.user.count();
    
    if (userCount === 0) {
      // Créer un utilisateur admin
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'admin123', // En production, il faut hasher le mot de passe
          name: 'Dr. Administrateur',
          role: 'ADMIN'
        }
      });

      // Créer quelques patients de test
      await prisma.patient.createMany({
        data: [
          {
            firstName: 'Jean',
            lastName: 'Dupont',
            phone: '**********',
            email: '<EMAIL>',
            dateOfBirth: new Date('1980-05-15'),
            gender: 'HOMME',
            address: '123 Rue de la Paix',
            city: 'Paris',
            postalCode: '75001',
            profession: 'Ingénieur',
            emergencyContact: 'Sophie Dupont',
            emergencyPhone: '**********',
            medicalHistory: 'Aucun antécédent particulier',
            allergies: 'Aucune allergie connue',
            bloodType: 'A+',
            insuranceNumber: '1234567890123',
            insuranceProvider: 'Sécurité Sociale',
            notes: 'Patient régulier, très ponctuel',
            isActive: true
          },
          {
            firstName: 'Marie',
            lastName: 'Martin',
            phone: '**********',
            email: '<EMAIL>',
            dateOfBirth: new Date('1975-12-03'),
            gender: 'FEMME',
            address: '456 Avenue des Champs',
            city: 'Paris',
            postalCode: '75008',
            profession: 'Professeure',
            emergencyContact: 'Paul Martin',
            emergencyPhone: '**********',
            medicalHistory: 'Hypertension artérielle',
            allergies: 'Allergie à la pénicilline',
            medications: 'Lisinopril 10mg',
            bloodType: 'B+',
            insuranceNumber: '2345678901234',
            insuranceProvider: 'Mutuelle Générale',
            notes: 'Préfère les rendez-vous le matin',
            isActive: true
          },
          {
            firstName: 'Hocine',
            lastName: 'Boukerche Mansour',
            phone: '**********',
            email: '<EMAIL>',
            dateOfBirth: new Date('1945-04-01'),
            gender: 'HOMME',
            address: '123 Rue de la Paix',
            city: 'Alger',
            postalCode: '16000',
            profession: 'Retraité',
            emergencyContact: 'Fatima Boukerche',
            emergencyPhone: '**********',
            medicalHistory: 'Hypertension artérielle, Diabète type 2',
            allergies: 'Pénicilline',
            medications: 'Metformine 500mg, Lisinopril 10mg',
            bloodType: 'O+',
            insuranceNumber: '1234567890123',
            insuranceProvider: 'CNAS',
            notes: 'Patient coopératif, sensible',
            isActive: true
          }
        ]
      });

      console.log('Base de données initialisée avec succès');
    }
  } catch (error) {
    console.error('Erreur lors de l\'initialisation de la base de données:', error);
  }
};

export default prisma;
