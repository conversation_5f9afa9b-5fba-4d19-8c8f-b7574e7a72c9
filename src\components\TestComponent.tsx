import React from 'react';

const TestComponent: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8">
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Test Tailwind CSS</h1>
        <p className="text-gray-600 mb-6">
          Si vous voyez ce texte avec des styles, Tailwind CSS fonctionne correctement !
        </p>
        <div className="space-y-4">
          <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            Bouton Test
          </button>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-red-100 p-4 rounded-lg text-center">
              <div className="w-8 h-8 bg-red-500 rounded-full mx-auto mb-2"></div>
              <p className="text-sm text-red-700">Rouge</p>
            </div>
            <div className="bg-green-100 p-4 rounded-lg text-center">
              <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2"></div>
              <p className="text-sm text-green-700">Vert</p>
            </div>
            <div className="bg-blue-100 p-4 rounded-lg text-center">
              <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto mb-2"></div>
              <p className="text-sm text-blue-700">Bleu</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestComponent;
