@echo off
echo.
echo ========================================
echo    CABINET DENTAIRE - DEMARRAGE
echo ========================================
echo.

echo Verification de Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Node.js n'est pas installe !
    echo Telecharger depuis: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js detecte !
echo.

echo Installation des dependances...
call npm run install:all

if errorlevel 1 (
    echo ERREUR lors de l'installation !
    pause
    exit /b 1
)

echo.
echo ========================================
echo    DEMARRAGE DE L'APPLICATION
echo ========================================
echo.
echo L'application va demarrer...
echo Acces local: http://localhost:3001
echo.
echo Pour arreter: Ctrl+C dans cette fenetre
echo.

call npm run dev

pause
