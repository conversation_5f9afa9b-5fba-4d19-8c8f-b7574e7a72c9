import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Initialisation de la base de données...');

  // Créer l'utilisateur administrateur par défaut
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: 'admin123', // En production, il faudrait hasher le mot de passe
      name: 'Dr. Administrateur',
      role: 'ADMIN',
    },
  });

  console.log('✅ Utilisateur administrateur créé:', admin.email);

  // Créer quelques patients d'exemple
  const patients = await Promise.all([
    prisma.patient.upsert({
      where: { phone: '**********' },
      update: {},
      create: {
        firstName: 'Jean',
        lastName: '<PERSON><PERSON>',
        phone: '**********',
        email: 'jean.du<PERSON>@email.com',
        dateOfBirth: new Date('1980-05-15'),
        address: '123 Rue de la Paix, 75001 Paris',
        medicalHistory: 'Aucun antécédent particulier',
        allergies: 'Aucune allergie connue',
        notes: 'Patient régulier, très ponctuel',
      },
    }),
    prisma.patient.upsert({
      where: { phone: '**********' },
      update: {},
      create: {
        firstName: 'Marie',
        lastName: 'Martin',
        phone: '**********',
        email: '<EMAIL>',
        dateOfBirth: new Date('1975-12-03'),
        address: '456 Avenue des Champs, 75008 Paris',
        medicalHistory: 'Hypertension artérielle',
        allergies: 'Allergie à la pénicilline',
        notes: 'Préfère les rendez-vous le matin',
      },
    }),
    prisma.patient.upsert({
      where: { phone: '**********' },
      update: {},
      create: {
        firstName: 'Pierre',
        lastName: 'Durand',
        phone: '**********',
        email: '<EMAIL>',
        dateOfBirth: new Date('1990-08-22'),
        address: '789 Boulevard Saint-Germain, 75006 Paris',
        medicalHistory: 'Diabète de type 2',
        allergies: 'Aucune allergie connue',
        notes: 'Nouveau patient, première visite',
      },
    }),
  ]);

  console.log('✅ Patients d\'exemple créés:', patients.length);

  // Créer quelques rendez-vous d'exemple
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const nextWeek = new Date();
  nextWeek.setDate(nextWeek.getDate() + 7);

  const appointments = await Promise.all([
    prisma.appointment.create({
      data: {
        patientId: patients[0].id,
        date: tomorrow,
        time: '09:00',
        duration: 30,
        type: 'CONSULTATION',
        status: 'SCHEDULED',
        notes: 'Contrôle de routine',
      },
    }),
    prisma.appointment.create({
      data: {
        patientId: patients[1].id,
        date: tomorrow,
        time: '14:30',
        duration: 45,
        type: 'TRAITEMENT',
        status: 'SCHEDULED',
        notes: 'Détartrage',
      },
    }),
    prisma.appointment.create({
      data: {
        patientId: patients[2].id,
        date: nextWeek,
        time: '10:15',
        duration: 60,
        type: 'CONSULTATION',
        status: 'SCHEDULED',
        notes: 'Première consultation',
      },
    }),
  ]);

  console.log('✅ Rendez-vous d\'exemple créés:', appointments.length);

  // Créer quelques consultations d'exemple
  const lastWeek = new Date();
  lastWeek.setDate(lastWeek.getDate() - 7);

  const consultations = await Promise.all([
    prisma.consultation.create({
      data: {
        patientId: patients[0].id,
        date: lastWeek,
        diagnosis: 'Carie dentaire sur molaire supérieure droite',
        treatment: 'Obturation composite',
        prescription: 'Bain de bouche antiseptique 2x/jour pendant 1 semaine',
        notes: 'Traitement réalisé sans complications',
        cost: 85.0,
        paid: true,
      },
    }),
    prisma.consultation.create({
      data: {
        patientId: patients[1].id,
        date: lastWeek,
        diagnosis: 'Gingivite légère',
        treatment: 'Détartrage et polissage',
        prescription: 'Dentifrice spécialisé pour gencives sensibles',
        notes: 'Amélioration de l\'hygiène bucco-dentaire recommandée',
        cost: 65.0,
        paid: true,
      },
    }),
  ]);

  console.log('✅ Consultations d\'exemple créées:', consultations.length);

  console.log('🎉 Initialisation terminée avec succès !');
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors de l\'initialisation:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
