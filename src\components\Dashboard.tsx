import React, { useState, useEffect } from 'react';
import { Users, Calendar, TrendingUp, Clock } from 'lucide-react';
import { prisma } from '../lib/prisma';
import { format, startOfMonth, endOfMonth } from 'date-fns';
import { fr } from 'date-fns/locale';

interface DashboardStats {
  totalPatients: number;
  todayAppointments: number;
  monthlyConsultations: number;
  upcomingAppointments: any[];
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    todayAppointments: 0,
    monthlyConsultations: 0,
    upcomingAppointments: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      const today = new Date();
      const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
      
      const startMonth = startOfMonth(today);
      const endMonth = endOfMonth(today);

      // Compter le nombre total de patients
      const totalPatients = await prisma.patient.count();

      // Compter les rendez-vous d'aujourd'hui
      const todayAppointments = await prisma.appointment.count({
        where: {
          date: {
            gte: startOfToday,
            lte: endOfToday,
          },
          status: 'SCHEDULED',
        },
      });

      // Compter les consultations du mois
      const monthlyConsultations = await prisma.consultation.count({
        where: {
          date: {
            gte: startMonth,
            lte: endMonth,
          },
        },
      });

      // Récupérer les prochains rendez-vous
      const upcomingAppointments = await prisma.appointment.findMany({
        where: {
          date: {
            gte: today,
          },
          status: 'SCHEDULED',
        },
        include: {
          patient: true,
        },
        orderBy: {
          date: 'asc',
        },
        take: 5,
      });

      setStats({
        totalPatients,
        todayAppointments,
        monthlyConsultations,
        upcomingAppointments,
      });
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`w-8 h-8 ${color} rounded-md flex items-center justify-center`}>
              {icon}
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="text-lg font-medium text-gray-900">{value}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Vue d'ensemble de votre cabinet dentaire
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <StatCard
          title="Total Patients"
          value={stats.totalPatients}
          icon={<Users className="w-5 h-5 text-white" />}
          color="bg-blue-500"
        />
        <StatCard
          title="RDV Aujourd'hui"
          value={stats.todayAppointments}
          icon={<Calendar className="w-5 h-5 text-white" />}
          color="bg-green-500"
        />
        <StatCard
          title="Consultations ce mois"
          value={stats.monthlyConsultations}
          icon={<TrendingUp className="w-5 h-5 text-white" />}
          color="bg-purple-500"
        />
        <StatCard
          title="En attente"
          value={stats.upcomingAppointments.length}
          icon={<Clock className="w-5 h-5 text-white" />}
          color="bg-orange-500"
        />
      </div>

      {/* Upcoming Appointments */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Prochains rendez-vous
          </h3>
          {stats.upcomingAppointments.length === 0 ? (
            <p className="text-gray-500">Aucun rendez-vous à venir</p>
          ) : (
            <div className="space-y-3">
              {stats.upcomingAppointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-blue-600" />
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {appointment.patient.firstName} {appointment.patient.lastName}
                      </p>
                      <p className="text-sm text-gray-500">
                        {format(new Date(appointment.date), 'dd MMMM yyyy', { locale: fr })} à {appointment.time}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{appointment.type}</p>
                    <p className="text-sm text-gray-500">{appointment.duration} min</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
