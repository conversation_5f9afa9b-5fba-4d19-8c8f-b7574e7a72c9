import React, { useState, useEffect } from 'react';
import {
  Users,
  Calendar,
  TrendingUp,
  DollarSign,
  Phone,
  ChevronRight,
  Plus
} from 'lucide-react';
import { prisma } from '../lib/prisma';
import { format, startOfMonth, endOfMonth } from 'date-fns';
import { fr } from 'date-fns/locale';

interface DashboardStats {
  totalPatients: number;
  todayAppointments: number;
  monthlyConsultations: number;
  upcomingAppointments: any[];
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    todayAppointments: 0,
    monthlyConsultations: 0,
    upcomingAppointments: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      const today = new Date();
      const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
      
      const startMonth = startOfMonth(today);
      const endMonth = endOfMonth(today);

      // Compter le nombre total de patients
      const totalPatients = await prisma.patient.count();

      // Compter les rendez-vous d'aujourd'hui
      const todayAppointments = await prisma.appointment.count({
        where: {
          date: {
            gte: startOfToday,
            lte: endOfToday,
          },
          status: 'SCHEDULED',
        },
      });

      // Compter les consultations du mois
      const monthlyConsultations = await prisma.consultation.count({
        where: {
          date: {
            gte: startMonth,
            lte: endMonth,
          },
        },
      });

      // Récupérer les prochains rendez-vous
      const upcomingAppointments = await prisma.appointment.findMany({
        where: {
          date: {
            gte: today,
          },
          status: 'SCHEDULED',
        },
        include: {
          patient: true,
        },
        orderBy: {
          date: 'asc',
        },
        take: 5,
      });

      setStats({
        totalPatients,
        todayAppointments,
        monthlyConsultations,
        upcomingAppointments,
      });
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    trend?: string;
    trendValue?: string;
  }> = ({ title, value, icon, color, trend, trendValue }) => (
    <div className="relative overflow-hidden rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-900/5">
      <div className="flex items-center">
        <div className={`rounded-lg p-3 ${color}`}>
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {trend && trendValue && (
            <div className="mt-1 flex items-center text-sm">
              <span className={`font-medium ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                {trendValue}
              </span>
              <span className="ml-1 text-gray-500">vs mois dernier</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 lg:p-8 text-white">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold">Bonjour Dr. Administrateur 👋</h1>
            <p className="mt-2 text-blue-100">
              {format(new Date(), 'EEEE dd MMMM yyyy', { locale: fr })} - Voici un aperçu de votre cabinet
            </p>
          </div>
          <div className="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <button className="inline-flex items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm px-4 py-2 text-sm font-semibold text-white hover:bg-white/30 transition-colors">
              <Calendar className="w-4 h-4 mr-2" />
              Planning du jour
            </button>
            <button className="inline-flex items-center justify-center rounded-lg bg-white px-4 py-2 text-sm font-semibold text-blue-600 hover:bg-blue-50 transition-colors">
              <Plus className="w-4 h-4 mr-2" />
              Nouveau RDV
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Patients"
          value={stats.totalPatients}
          icon={<Users className="w-6 h-6 text-white" />}
          color="bg-gradient-to-br from-blue-500 to-blue-600"
          trend="up"
          trendValue="+12%"
        />
        <StatCard
          title="RDV Aujourd'hui"
          value={stats.todayAppointments}
          icon={<Calendar className="w-6 h-6 text-white" />}
          color="bg-gradient-to-br from-emerald-500 to-emerald-600"
          trend="up"
          trendValue="+5%"
        />
        <StatCard
          title="Consultations ce mois"
          value={stats.monthlyConsultations}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-gradient-to-br from-purple-500 to-purple-600"
          trend="up"
          trendValue="+8%"
        />
        <StatCard
          title="Revenus du mois"
          value={2450}
          icon={<DollarSign className="w-6 h-6 text-white" />}
          color="bg-gradient-to-br from-amber-500 to-orange-500"
          trend="up"
          trendValue="+15%"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Upcoming Appointments */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Prochains rendez-vous
                </h3>
                <button className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  Voir tout
                  <ChevronRight className="w-4 h-4 inline ml-1" />
                </button>
              </div>
            </div>
            <div className="p-6">
              {stats.upcomingAppointments.length === 0 ? (
                <div className="text-center py-12">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Aucun rendez-vous à venir</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {stats.upcomingAppointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Users className="w-5 h-5 text-blue-600" />
                          </div>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {appointment.patient.firstName} {appointment.patient.lastName}
                          </p>
                          <p className="text-sm text-gray-500">
                            {format(new Date(appointment.date), 'dd MMMM yyyy', { locale: fr })} à {appointment.time}
                          </p>
                          <p className="text-xs text-gray-400">{appointment.notes}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                          {appointment.type}
                        </span>
                        <p className="text-sm text-gray-500 mt-1">{appointment.duration} min</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions & Recent Activity */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
            <div className="space-y-3">
              <button className="w-full flex items-center justify-between p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <Plus className="w-5 h-5 text-blue-600 mr-3" />
                  <span className="font-medium text-blue-900">Nouveau patient</span>
                </div>
                <ChevronRight className="w-4 h-4 text-blue-600" />
              </button>
              <button className="w-full flex items-center justify-between p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 text-green-600 mr-3" />
                  <span className="font-medium text-green-900">Planifier RDV</span>
                </div>
                <ChevronRight className="w-4 h-4 text-green-600" />
              </button>
              <button className="w-full flex items-center justify-between p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <Phone className="w-5 h-5 text-purple-600 mr-3" />
                  <span className="font-medium text-purple-900">Recherche rapide</span>
                </div>
                <ChevronRight className="w-4 h-4 text-purple-600" />
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Consultation terminée</p>
                  <p className="text-xs text-gray-500">Jean Dupont - il y a 2h</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Nouveau rendez-vous</p>
                  <p className="text-xs text-gray-500">Marie Martin - il y a 4h</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Paiement reçu</p>
                  <p className="text-xs text-gray-500">85€ - il y a 6h</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
