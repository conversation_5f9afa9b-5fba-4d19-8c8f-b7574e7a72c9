const express = require('express');
const { v4: uuidv4 } = require('uuid');
const database = require('../database/init');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /api/appointments - Récupérer tous les rendez-vous
router.get('/', async (req, res) => {
  try {
    const { date, startDate, endDate, patientId, status } = req.query;
    
    let sql = 'SELECT * FROM appointments WHERE 1=1';
    let params = [];
    
    if (date) {
      sql += ' AND date = ?';
      params.push(date);
    }
    
    if (startDate && endDate) {
      sql += ' AND date BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }
    
    if (patientId) {
      sql += ' AND patientId = ?';
      params.push(patientId);
    }
    
    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }
    
    sql += ' ORDER BY date ASC, time ASC';
    
    const appointments = await database.all(sql, params);
    
    res.json({ appointments });

  } catch (error) {
    console.error('Erreur lors de la récupération des rendez-vous:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// GET /api/appointments/:id - Récupérer un rendez-vous par ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const appointment = await database.get('SELECT * FROM appointments WHERE id = ?', [id]);
    
    if (!appointment) {
      return res.status(404).json({ error: 'Rendez-vous non trouvé' });
    }
    
    res.json({ appointment });

  } catch (error) {
    console.error('Erreur lors de la récupération du rendez-vous:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// POST /api/appointments - Créer un nouveau rendez-vous
router.post('/', async (req, res) => {
  try {
    const {
      patientId,
      patientName,
      patientPhone,
      date,
      time,
      type,
      duration = 30,
      status = 'scheduled',
      notes,
      isUrgent = false
    } = req.body;

    if (!date || !time || !type) {
      return res.status(400).json({ error: 'Date, heure et type sont requis' });
    }

    // Si patientId est fourni, vérifier que le patient existe
    if (patientId) {
      const patient = await database.get('SELECT id, firstName, lastName, phone FROM patients WHERE id = ?', [patientId]);
      if (!patient) {
        return res.status(404).json({ error: 'Patient non trouvé' });
      }
    }

    // Vérifier qu'il n'y a pas de conflit d'horaire
    const conflictingAppointment = await database.get(
      'SELECT id FROM appointments WHERE date = ? AND time = ? AND status != ?',
      [date, time, 'cancelled']
    );
    
    if (conflictingAppointment) {
      return res.status(409).json({ error: 'Un rendez-vous existe déjà à cette heure' });
    }

    const appointmentId = uuidv4();
    
    await database.run(`
      INSERT INTO appointments (
        id, patientId, patientName, patientPhone, date, time, type,
        duration, status, notes, isUrgent
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      appointmentId, patientId, patientName, patientPhone, date, time, type,
      duration, status, notes, isUrgent ? 1 : 0
    ]);

    const newAppointment = await database.get('SELECT * FROM appointments WHERE id = ?', [appointmentId]);
    
    res.status(201).json({
      message: 'Rendez-vous créé avec succès',
      appointment: newAppointment
    });

  } catch (error) {
    console.error('Erreur lors de la création du rendez-vous:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// PUT /api/appointments/:id - Mettre à jour un rendez-vous
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      patientId,
      patientName,
      patientPhone,
      date,
      time,
      type,
      duration,
      status,
      notes,
      isUrgent
    } = req.body;

    // Vérifier que le rendez-vous existe
    const existingAppointment = await database.get('SELECT id FROM appointments WHERE id = ?', [id]);
    
    if (!existingAppointment) {
      return res.status(404).json({ error: 'Rendez-vous non trouvé' });
    }

    // Vérifier qu'il n'y a pas de conflit d'horaire (sauf pour ce rendez-vous)
    if (date && time) {
      const conflictingAppointment = await database.get(
        'SELECT id FROM appointments WHERE date = ? AND time = ? AND id != ? AND status != ?',
        [date, time, id, 'cancelled']
      );
      
      if (conflictingAppointment) {
        return res.status(409).json({ error: 'Un autre rendez-vous existe déjà à cette heure' });
      }
    }

    await database.run(`
      UPDATE appointments SET
        patientId = ?, patientName = ?, patientPhone = ?, date = ?, time = ?,
        type = ?, duration = ?, status = ?, notes = ?, isUrgent = ?,
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      patientId, patientName, patientPhone, date, time, type,
      duration, status, notes, isUrgent ? 1 : 0, id
    ]);

    const updatedAppointment = await database.get('SELECT * FROM appointments WHERE id = ?', [id]);
    
    res.json({
      message: 'Rendez-vous mis à jour avec succès',
      appointment: updatedAppointment
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du rendez-vous:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// DELETE /api/appointments/:id - Supprimer un rendez-vous
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que le rendez-vous existe
    const existingAppointment = await database.get('SELECT id FROM appointments WHERE id = ?', [id]);
    
    if (!existingAppointment) {
      return res.status(404).json({ error: 'Rendez-vous non trouvé' });
    }

    await database.run('DELETE FROM appointments WHERE id = ?', [id]);
    
    res.json({ message: 'Rendez-vous supprimé avec succès' });

  } catch (error) {
    console.error('Erreur lors de la suppression du rendez-vous:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// PATCH /api/appointments/:id/status - Changer le statut d'un rendez-vous
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ error: 'Statut requis' });
    }

    const validStatuses = ['scheduled', 'confirmed', 'completed', 'cancelled', 'no-show'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Statut invalide' });
    }

    // Vérifier que le rendez-vous existe
    const existingAppointment = await database.get('SELECT id FROM appointments WHERE id = ?', [id]);
    
    if (!existingAppointment) {
      return res.status(404).json({ error: 'Rendez-vous non trouvé' });
    }

    await database.run(
      'UPDATE appointments SET status = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      [status, id]
    );

    const updatedAppointment = await database.get('SELECT * FROM appointments WHERE id = ?', [id]);
    
    res.json({
      message: 'Statut mis à jour avec succès',
      appointment: updatedAppointment
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

module.exports = router;
