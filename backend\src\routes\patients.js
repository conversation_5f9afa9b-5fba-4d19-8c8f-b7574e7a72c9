const express = require('express');
const { v4: uuidv4 } = require('uuid');
const database = require('../database/init');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /api/patients - Récupérer tous les patients
router.get('/', async (req, res) => {
  try {
    const { search, limit = 50, offset = 0 } = req.query;
    
    let sql = 'SELECT * FROM patients';
    let params = [];
    
    if (search) {
      sql += ' WHERE firstName LIKE ? OR lastName LIKE ? OR email LIKE ? OR phone LIKE ?';
      const searchTerm = `%${search}%`;
      params = [searchTerm, searchTerm, searchTerm, searchTerm];
    }
    
    sql += ' ORDER BY lastName, firstName LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));
    
    const patients = await database.all(sql, params);
    
    res.json({
      patients,
      total: patients.length,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des patients:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// GET /api/patients/:id - Récupérer un patient par ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const patient = await database.get('SELECT * FROM patients WHERE id = ?', [id]);
    
    if (!patient) {
      return res.status(404).json({ error: 'Patient non trouvé' });
    }
    
    res.json({ patient });

  } catch (error) {
    console.error('Erreur lors de la récupération du patient:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// POST /api/patients - Créer un nouveau patient
router.post('/', async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      dateOfBirth,
      gender,
      emergencyContact,
      emergencyPhone,
      medicalHistory,
      allergies,
      currentMedications,
      notes
    } = req.body;

    if (!firstName || !lastName) {
      return res.status(400).json({ error: 'Prénom et nom sont requis' });
    }

    const patientId = uuidv4();
    
    await database.run(`
      INSERT INTO patients (
        id, firstName, lastName, email, phone, address, dateOfBirth,
        gender, emergencyContact, emergencyPhone, medicalHistory,
        allergies, currentMedications, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      patientId, firstName, lastName, email, phone, address, dateOfBirth,
      gender, emergencyContact, emergencyPhone, medicalHistory,
      allergies, currentMedications, notes
    ]);

    const newPatient = await database.get('SELECT * FROM patients WHERE id = ?', [patientId]);
    
    res.status(201).json({
      message: 'Patient créé avec succès',
      patient: newPatient
    });

  } catch (error) {
    console.error('Erreur lors de la création du patient:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// PUT /api/patients/:id - Mettre à jour un patient
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      dateOfBirth,
      gender,
      emergencyContact,
      emergencyPhone,
      medicalHistory,
      allergies,
      currentMedications,
      notes
    } = req.body;

    // Vérifier que le patient existe
    const existingPatient = await database.get('SELECT id FROM patients WHERE id = ?', [id]);
    
    if (!existingPatient) {
      return res.status(404).json({ error: 'Patient non trouvé' });
    }

    await database.run(`
      UPDATE patients SET
        firstName = ?, lastName = ?, email = ?, phone = ?, address = ?,
        dateOfBirth = ?, gender = ?, emergencyContact = ?, emergencyPhone = ?,
        medicalHistory = ?, allergies = ?, currentMedications = ?, notes = ?,
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      firstName, lastName, email, phone, address, dateOfBirth,
      gender, emergencyContact, emergencyPhone, medicalHistory,
      allergies, currentMedications, notes, id
    ]);

    const updatedPatient = await database.get('SELECT * FROM patients WHERE id = ?', [id]);
    
    res.json({
      message: 'Patient mis à jour avec succès',
      patient: updatedPatient
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du patient:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// DELETE /api/patients/:id - Supprimer un patient
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que le patient existe
    const existingPatient = await database.get('SELECT id FROM patients WHERE id = ?', [id]);
    
    if (!existingPatient) {
      return res.status(404).json({ error: 'Patient non trouvé' });
    }

    // Supprimer le patient (les contraintes de clé étrangère géreront les relations)
    await database.run('DELETE FROM patients WHERE id = ?', [id]);
    
    res.json({ message: 'Patient supprimé avec succès' });

  } catch (error) {
    console.error('Erreur lors de la suppression du patient:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// GET /api/patients/:id/appointments - Récupérer les rendez-vous d'un patient
router.get('/:id/appointments', async (req, res) => {
  try {
    const { id } = req.params;
    
    const appointments = await database.all(
      'SELECT * FROM appointments WHERE patientId = ? ORDER BY date DESC, time DESC',
      [id]
    );
    
    res.json({ appointments });

  } catch (error) {
    console.error('Erreur lors de la récupération des rendez-vous:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// GET /api/patients/:id/consultations - Récupérer les consultations d'un patient
router.get('/:id/consultations', async (req, res) => {
  try {
    const { id } = req.params;
    
    const consultations = await database.all(
      'SELECT * FROM consultations WHERE patientId = ? ORDER BY date DESC',
      [id]
    );
    
    res.json({ consultations });

  } catch (error) {
    console.error('Erreur lors de la récupération des consultations:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

// GET /api/patients/:id/payments - Récupérer les paiements d'un patient
router.get('/:id/payments', async (req, res) => {
  try {
    const { id } = req.params;
    
    const payments = await database.all(
      'SELECT * FROM payments WHERE patientId = ? ORDER BY date DESC',
      [id]
    );
    
    res.json({ payments });

  } catch (error) {
    console.error('Erreur lors de la récupération des paiements:', error);
    res.status(500).json({ error: 'Erreur interne du serveur' });
  }
});

module.exports = router;
