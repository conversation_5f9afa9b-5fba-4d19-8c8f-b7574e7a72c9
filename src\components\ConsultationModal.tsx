import React, { useState } from 'react';
import { X, Save, Stethoscope, FileText } from 'lucide-react';

interface ConsultationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (consultation: any) => Promise<void>;
  patientId?: string;
  patientName?: string;
}

const ConsultationModal: React.FC<ConsultationModalProps> = ({
  isOpen,
  onClose,
  onSave,
  patientId,
  patientName
}) => {
  const [formData, setFormData] = useState({
    type: '',
    reason: '',
    symptoms: '',
    diagnosis: '',
    treatment: '',
    prescription: '',
    cost: '',
    nextAppointment: ''
  });

  const [showPayment, setShowPayment] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const consultationTypes = [
    'Consultation initiale',
    'Consultation de contrôle',
    'Urgence',
    'Détartrage',
    'Soin conservateur',
    'Extraction',
    'Prothèse',
    'Orthod<PERSON>ie',
    'Chirurgie',
    'Endodontie'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const consultationData = {
        ...formData,
        patientId,
        cost: formData.cost ? parseFloat(formData.cost) : undefined,
        nextAppointment: formData.nextAppointment ? new Date(formData.nextAppointment) : undefined
      };

      // Ajouter les informations de paiement si un paiement a été saisi
      if (paymentAmount && parseFloat(paymentAmount) > 0) {
        consultationData.payment = {
          amount: parseFloat(paymentAmount),
          date: new Date(),
          method: 'CASH', // Par défaut
          remaining: parseFloat(formData.cost) - parseFloat(paymentAmount)
        };
      }

      await onSave(consultationData);
      onClose();

      // Reset form
      setFormData({
        type: '',
        reason: '',
        symptoms: '',
        diagnosis: '',
        treatment: '',
        prescription: '',
        cost: '',
        nextAppointment: ''
      });
      setPaymentAmount('');
      setShowPayment(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header simple */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Stethoscope className="w-5 h-5 text-gray-600 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900">Nouvelle consultation</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form simplifié */}
        <form onSubmit={handleSubmit} className="p-6">
          {patientName && (
            <div className="mb-6 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-700">
                <strong>Patient:</strong> {patientName}
              </p>
            </div>
          )}

          <div className="space-y-6">
            {/* Informations de base */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Informations de consultation</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type de consultation *
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Sélectionner un type</option>
                    {consultationTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Coût total (DA)
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      name="cost"
                      value={formData.cost}
                      onChange={handleChange}
                      step="100"
                      min="0"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Ex: 50000"
                    />
                    {formData.cost && formData.cost.trim() !== '' && parseFloat(formData.cost) > 0 && (
                      <button
                        type="button"
                        onClick={() => setShowPayment(!showPayment)}
                        className="px-3 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors whitespace-nowrap"
                      >
                        💰 Payer
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Section de paiement */}
              {showPayment && formData.cost && formData.cost.trim() !== '' && parseFloat(formData.cost) > 0 && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-800 mb-3">💰 Enregistrer un paiement</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs text-green-700 mb-1">
                        Montant payé (DA)
                      </label>
                      <input
                        type="number"
                        value={paymentAmount}
                        onChange={(e) => setPaymentAmount(e.target.value)}
                        step="100"
                        min="0"
                        max={formData.cost}
                        className="w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                        placeholder={`Max: ${parseInt(formData.cost).toLocaleString()} DA`}
                      />
                    </div>
                    <div className="flex items-end space-x-2">
                      <button
                        type="button"
                        onClick={() => setPaymentAmount(formData.cost)}
                        className="px-3 py-2 bg-green-100 text-green-700 text-sm rounded-lg hover:bg-green-200 transition-colors"
                      >
                        Paiement complet
                      </button>
                      <button
                        type="button"
                        onClick={() => setPaymentAmount((parseFloat(formData.cost) / 2).toString())}
                        className="px-3 py-2 bg-blue-100 text-blue-700 text-sm rounded-lg hover:bg-blue-200 transition-colors"
                      >
                        50%
                      </button>
                    </div>
                  </div>
                  {paymentAmount && parseFloat(paymentAmount) > 0 && (
                    <div className="mt-3 p-2 bg-white rounded border">
                      <div className="text-xs text-green-600 space-y-1">
                        <div>💰 Montant payé: <strong>{parseInt(paymentAmount).toLocaleString()} DA</strong></div>
                        <div>📋 Reste à payer: <strong>{(parseFloat(formData.cost) - parseFloat(paymentAmount)).toLocaleString()} DA</strong></div>
                        <div className="text-gray-500">
                          Progression: {((parseFloat(paymentAmount) / parseFloat(formData.cost)) * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Motif de consultation
                </label>
                <textarea
                  name="reason"
                  value={formData.reason}
                  onChange={handleChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Pourquoi le patient consulte-t-il ?"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Symptômes observés
                </label>
                <textarea
                  name="symptoms"
                  value={formData.symptoms}
                  onChange={handleChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Douleur, gonflement, saignement..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Diagnostic
                </label>
                <textarea
                  name="diagnosis"
                  value={formData.diagnosis}
                  onChange={handleChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Diagnostic établi..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Traitement effectué
                </label>
                <textarea
                  name="treatment"
                  value={formData.treatment}
                  onChange={handleChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Traitement réalisé..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prescription
                </label>
                <textarea
                  name="prescription"
                  value={formData.prescription}
                  onChange={handleChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Médicaments prescrits, posologie..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prochain rendez-vous
                </label>
                <input
                  type="date"
                  name="nextAppointment"
                  value={formData.nextAppointment}
                  onChange={handleChange}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Buttons simples */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors text-sm"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors text-sm"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sauvegarde...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Enregistrer consultation
                  {paymentAmount && parseFloat(paymentAmount) > 0 && ' + paiement'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ConsultationModal;
