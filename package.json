{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "1.0.0", "description": "Application de gestion pour cabinet dentaire", "main": "electron/main.cjs", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build && vite build --config vite.electron.config.ts", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build:electron": "vite build --config vite.electron.config.ts", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "postinstall": "prisma generate", "seed": "tsx scripts/seed.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/react-router-dom": "^5.3.3", "concurrently": "^9.2.0", "cross-env": "^10.0.0", "date-fns": "^4.1.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "lucide-react": "^0.528.0", "prisma": "^6.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "wait-on": "^8.0.4"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/vite": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "build": {"appId": "com.beddiar.cabinet-dentaire", "productName": "Cabinet <PERSON>taire", "directories": {"output": "dist-app"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}