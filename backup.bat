@echo off
echo.
echo ========================================
echo    SAUVEGARDE CABINET DENTAIRE
echo ========================================
echo.

set SOURCE_FILE=cabinet.json
set BACKUP_DIR=backup
set DATE_TIME=%date:~6,4%-%date:~3,2%-%date:~0,2%_%time:~0,2%h%time:~3,2%m

:: Remplacer les espaces dans l'heure
set DATE_TIME=%DATE_TIME: =0%

echo Verification du fichier source...
if not exist "%SOURCE_FILE%" (
    echo ERREUR: Le fichier %SOURCE_FILE% n'existe pas !
    echo Assurez-vous d'etre dans le bon dossier.
    pause
    exit /b 1
)

echo Fichier trouve: %SOURCE_FILE%
echo.

:: <PERSON><PERSON> le dossier backup s'il n'existe pas
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%"
    echo Dossier backup cree.
)

:: Copier avec horodatage
set BACKUP_FILE=%BACKUP_DIR%\cabinet_backup_%DATE_TIME%.json
copy "%SOURCE_FILE%" "%BACKUP_FILE%"

if errorlevel 1 (
    echo ERREUR lors de la copie !
    pause
    exit /b 1
)

echo.
echo ✅ SAUVEGARDE REUSSIE !
echo.
echo Fichier sauve: %BACKUP_FILE%
echo Taille: 
dir "%BACKUP_FILE%" | find "%BACKUP_FILE%"
echo.

:: Afficher le contenu du dossier backup
echo Historique des sauvegardes:
dir "%BACKUP_DIR%\cabinet_backup_*.json" /b 2>nul
if errorlevel 1 (
    echo Aucune sauvegarde precedente trouvee.
)

echo.
echo ========================================
echo Pour copier sur USB:
echo 1. Inserez votre cle USB
echo 2. Copiez le fichier: %BACKUP_FILE%
echo 3. Ou copiez tout le dossier: %BACKUP_DIR%
echo ========================================
echo.

pause
