// Mock Prisma client pour le développement frontend
// En production, ceci sera remplacé par des appels API

interface MockUser {
  id: string;
  email: string;
  password: string;
  name: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MockPatient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface MockAppointment {
  id: string;
  patientId: string;
  date: Date;
  time: string;
  duration: number;
  type: string;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  patient?: MockPatient;
}

interface MockConsultation {
  id: string;
  patientId: string;
  date: Date;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  notes?: string;
  cost?: number;
  paid: boolean;
  createdAt: Date;
  updatedAt: Date;
  patient?: MockPatient;
}

// Données mock
const mockUsers: MockUser[] = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Dr. Administrateur',
    role: 'ADMIN',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const mockPatients: MockPatient[] = [
  {
    id: '1',
    firstName: 'Jean',
    lastName: 'Dupont',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1980-05-15'),
    gender: 'Homme',
    address: '123 Rue de la Paix',
    city: 'Paris',
    postalCode: '75001',
    profession: 'Ingénieur',
    emergencyContact: 'Sophie Dupont',
    emergencyPhone: '**********',
    medicalHistory: 'Aucun antécédent particulier',
    allergies: 'Aucune allergie connue',
    medications: undefined,
    bloodType: 'A+',
    insuranceNumber: '1234567890123',
    insuranceProvider: 'Sécurité Sociale',
    notes: 'Patient régulier, très ponctuel',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    firstName: 'Marie',
    lastName: 'Martin',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1975-12-03'),
    gender: 'Femme',
    address: '456 Avenue des Champs',
    city: 'Paris',
    postalCode: '75008',
    profession: 'Professeure',
    emergencyContact: 'Paul Martin',
    emergencyPhone: '**********',
    medicalHistory: 'Hypertension artérielle',
    allergies: 'Allergie à la pénicilline',
    medications: 'Lisinopril 10mg',
    bloodType: 'B+',
    insuranceNumber: '2345678901234',
    insuranceProvider: 'Mutuelle Générale',
    notes: 'Préfère les rendez-vous le matin',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    firstName: 'Hocine',
    lastName: 'Boukerche Mansour',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1945-04-01'),
    gender: 'Homme',
    address: '123 Rue de la Paix',
    city: 'Alger',
    postalCode: '16000',
    profession: 'Retraité',
    emergencyContact: 'Fatima Boukerche',
    emergencyPhone: '**********',
    medicalHistory: 'Hypertension artérielle, Diabète type 2',
    allergies: 'Pénicilline',
    medications: 'Metformine 500mg, Lisinopril 10mg',
    bloodType: 'O+',
    insuranceNumber: '1234567890123',
    insuranceProvider: 'CNAS',
    notes: 'Patient coopératif, sensible',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);

const mockAppointments: MockAppointment[] = [
  {
    id: '1',
    patientId: '1',
    date: tomorrow,
    time: '09:00',
    duration: 30,
    type: 'CONSULTATION',
    status: 'SCHEDULED',
    notes: 'Contrôle de routine',
    createdAt: new Date(),
    updatedAt: new Date(),
    patient: mockPatients[0],
  },
  {
    id: '2',
    patientId: '2',
    date: tomorrow,
    time: '14:30',
    duration: 45,
    type: 'TRAITEMENT',
    status: 'SCHEDULED',
    notes: 'Détartrage',
    createdAt: new Date(),
    updatedAt: new Date(),
    patient: mockPatients[1],
  }
];

const mockConsultations: MockConsultation[] = [
  {
    id: '1',
    patientId: '1',
    date: new Date(),
    diagnosis: 'Carie dentaire sur molaire supérieure droite',
    treatment: 'Obturation composite',
    prescription: 'Bain de bouche antiseptique 2x/jour pendant 1 semaine',
    notes: 'Traitement réalisé sans complications',
    cost: 85.0,
    paid: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    patient: mockPatients[0],
  }
];

import { patientAPI } from './api';

// Export des API pour compatibilité
export const prisma = {
  user: {
    findUnique: async ({ where }: { where: { email: string } }) => {
      // Pour l'instant, on garde l'utilisateur mock pour l'authentification
      return mockUsers.find(user => user.email === where.email) || null;
    },
    count: async () => mockUsers.length,
  },
  patient: {
    count: async () => {
      const patients = await patientAPI.getAll();
      return patients.length;
    },
    findMany: async () => {
      return await patientAPI.getAll();
    },
    findUnique: async ({ where }: { where: { id: string } }) => {
      return await patientAPI.getById(where.id);
    },
    create: async ({ data }: { data: any }) => {
      return await patientAPI.create(data);
    },
    update: async ({ where, data }: { where: { id: string }, data: any }) => {
      return await patientAPI.update(where.id, data);
    },
    delete: async ({ where }: { where: { id: string } }) => {
      return await patientAPI.delete(where.id);
    },
  },
  appointment: {
    count: async ({ where }: any) => {
      if (where?.date) {
        return mockAppointments.filter(apt =>
          apt.date >= where.date.gte && apt.date <= where.date.lte
        ).length;
      }
      return mockAppointments.length;
    },
    findMany: async ({ where, take }: any) => {
      let filtered = mockAppointments;
      if (where?.date?.gte) {
        filtered = filtered.filter(apt => apt.date >= where.date.gte);
      }
      if (where?.status) {
        filtered = filtered.filter(apt => apt.status === where.status);
      }
      return filtered.slice(0, take || filtered.length);
    },
  },
  consultation: {
    count: async ({ where }: any) => {
      if (where?.date) {
        return mockConsultations.filter(cons =>
          cons.date >= where.date.gte && cons.date <= where.date.lte
        ).length;
      }
      return mockConsultations.length;
    },
  },
};
