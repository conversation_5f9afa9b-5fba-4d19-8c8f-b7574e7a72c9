// Mock Prisma client pour le développement frontend
// En production, ceci sera remplacé par des appels API

interface MockUser {
  id: string;
  email: string;
  password: string;
  name: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MockPatient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  address?: string;
  medicalHistory?: string;
  allergies?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MockAppointment {
  id: string;
  patientId: string;
  date: Date;
  time: string;
  duration: number;
  type: string;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  patient?: MockPatient;
}

interface MockConsultation {
  id: string;
  patientId: string;
  date: Date;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  notes?: string;
  cost?: number;
  paid: boolean;
  createdAt: Date;
  updatedAt: Date;
  patient?: MockPatient;
}

// Données mock
const mockUsers: MockUser[] = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Dr. Administrateur',
    role: 'ADMIN',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const mockPatients: MockPatient[] = [
  {
    id: '1',
    firstName: 'Jean',
    lastName: 'Dupont',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1980-05-15'),
    address: '123 Rue de la Paix, 75001 Paris',
    medicalHistory: 'Aucun antécédent particulier',
    allergies: 'Aucune allergie connue',
    notes: 'Patient régulier, très ponctuel',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    firstName: 'Marie',
    lastName: 'Martin',
    phone: '**********',
    email: '<EMAIL>',
    dateOfBirth: new Date('1975-12-03'),
    address: '456 Avenue des Champs, 75008 Paris',
    medicalHistory: 'Hypertension artérielle',
    allergies: 'Allergie à la pénicilline',
    notes: 'Préfère les rendez-vous le matin',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);

const mockAppointments: MockAppointment[] = [
  {
    id: '1',
    patientId: '1',
    date: tomorrow,
    time: '09:00',
    duration: 30,
    type: 'CONSULTATION',
    status: 'SCHEDULED',
    notes: 'Contrôle de routine',
    createdAt: new Date(),
    updatedAt: new Date(),
    patient: mockPatients[0],
  },
  {
    id: '2',
    patientId: '2',
    date: tomorrow,
    time: '14:30',
    duration: 45,
    type: 'TRAITEMENT',
    status: 'SCHEDULED',
    notes: 'Détartrage',
    createdAt: new Date(),
    updatedAt: new Date(),
    patient: mockPatients[1],
  }
];

const mockConsultations: MockConsultation[] = [
  {
    id: '1',
    patientId: '1',
    date: new Date(),
    diagnosis: 'Carie dentaire sur molaire supérieure droite',
    treatment: 'Obturation composite',
    prescription: 'Bain de bouche antiseptique 2x/jour pendant 1 semaine',
    notes: 'Traitement réalisé sans complications',
    cost: 85.0,
    paid: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    patient: mockPatients[0],
  }
];

// Mock Prisma client
export const prisma = {
  user: {
    findUnique: async ({ where }: { where: { email: string } }) => {
      return mockUsers.find(user => user.email === where.email) || null;
    },
    count: async () => mockUsers.length,
  },
  patient: {
    count: async () => mockPatients.length,
    findMany: async () => mockPatients,
  },
  appointment: {
    count: async ({ where }: any) => {
      if (where?.date) {
        return mockAppointments.filter(apt =>
          apt.date >= where.date.gte && apt.date <= where.date.lte
        ).length;
      }
      return mockAppointments.length;
    },
    findMany: async ({ where, take }: any) => {
      let filtered = mockAppointments;
      if (where?.date?.gte) {
        filtered = filtered.filter(apt => apt.date >= where.date.gte);
      }
      if (where?.status) {
        filtered = filtered.filter(apt => apt.status === where.status);
      }
      return filtered.slice(0, take || filtered.length);
    },
  },
  consultation: {
    count: async ({ where }: any) => {
      if (where?.date) {
        return mockConsultations.filter(cons =>
          cons.date >= where.date.gte && cons.date <= where.date.lte
        ).length;
      }
      return mockConsultations.length;
    },
  },
};
