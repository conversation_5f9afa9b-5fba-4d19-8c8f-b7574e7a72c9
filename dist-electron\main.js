"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("electron"),u=require("path"),b=require("fs");function d(a){const s=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(a){for(const o in a)if(o!=="default"){const i=Object.getOwnPropertyDescriptor(a,o);Object.defineProperty(s,o,i.get?i:{enumerable:!0,get:()=>a[o]})}}return s.default=a,Object.freeze(s)}const t=d(u),g=d(b),l=l||t.resolve();process.env.APP_ROOT=t.join(l,"..");const r=process.env.VITE_DEV_SERVER_URL,f=t.join(process.env.APP_ROOT,"dist-electron"),c=t.join(process.env.APP_ROOT,"dist");process.env.VITE_PUBLIC=r?t.join(process.env.APP_ROOT,"public"):c;let n;function p(){n=new e.BrowserWindow({width:1200,height:800,minWidth:800,minHeight:600,icon:t.join(process.env.VITE_PUBLIC,"icon.png"),webPreferences:{preload:t.join(l,"preload.mjs"),nodeIntegration:!1,contextIsolation:!0}}),n.webContents.on("did-finish-load",()=>{n?.webContents.send("main-process-message",new Date().toLocaleString())}),r?n.loadURL(r):n.loadFile(t.join(c,"index.html"))}e.app.on("window-all-closed",()=>{process.platform!=="darwin"&&(e.app.quit(),n=null)});e.app.on("activate",()=>{e.BrowserWindow.getAllWindows().length===0&&p()});e.app.whenReady().then(()=>{p();const a=[{label:"Fichier",submenu:[{label:"Sauvegarder la base de données",click:async()=>{const o=await e.dialog.showSaveDialog(n,{title:"Sauvegarder la base de données",defaultPath:"cabinet-dentaire-backup.db",filters:[{name:"Base de données",extensions:["db","sqlite"]}]});if(!o.canceled&&o.filePath)try{const i=t.join(process.env.APP_ROOT,"prisma","cabinet.sqlite");g.copyFileSync(i,o.filePath),e.dialog.showMessageBox(n,{type:"info",title:"Sauvegarde réussie",message:"La base de données a été sauvegardée avec succès."})}catch{e.dialog.showErrorBox("Erreur de sauvegarde","Impossible de sauvegarder la base de données.")}}},{type:"separator"},{label:"Quitter",accelerator:"CmdOrCtrl+Q",click:()=>{e.app.quit()}}]},{label:"Aide",submenu:[{label:"À propos",click:()=>{e.dialog.showMessageBox(n,{type:"info",title:"À propos",message:"Cabinet Dentaire v1.0.0",detail:"Application de gestion pour cabinet dentaire"})}}]}],s=e.Menu.buildFromTemplate(a);e.Menu.setApplicationMenu(s)});exports.MAIN_DIST=f;exports.RENDERER_DIST=c;exports.VITE_DEV_SERVER_URL=r;
