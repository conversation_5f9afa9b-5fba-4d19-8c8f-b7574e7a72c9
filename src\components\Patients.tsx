import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  Plus,
  Search,
  Phone,
  Mail,
  Calendar,
  Edit,
  Trash2,
  Eye,
  Filter,
  AlertTriangle
} from 'lucide-react';
import { prisma } from '../lib/prisma';
import PatientModal from './PatientModal';

// Type local pour éviter les problèmes d'import
type Gender = 'HOMME' | 'FEMME' | 'AUTRE';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const Patients: React.FC = () => {
  const navigate = useNavigate();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);


  useEffect(() => {
    loadPatients();
  }, []);

  const loadPatients = async () => {
    try {
      setIsLoading(true);
      const data = await prisma.patient.findMany();
      setPatients(data);
    } catch (error) {
      console.error('Erreur lors du chargement des patients:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddPatient = () => {
    setSelectedPatient(null);
    setIsModalOpen(true);
  };

  const handleEditPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setIsModalOpen(true);
  };

  const handleSavePatient = async (patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>) => {
    try {
      if (selectedPatient) {
        // Mise à jour
        await prisma.patient.update({
          where: { id: selectedPatient.id },
          data: patientData
        });
      } else {
        // Création
        await prisma.patient.create({
          data: patientData
        });
      }
      await loadPatients();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du patient:', error);
      throw error;
    }
  };

  const handleDeletePatient = async (id: string) => {
    if (deleteConfirm === id) {
      try {
        await prisma.patient.delete({ where: { id } });
        await loadPatients();
        setDeleteConfirm(null);
      } catch (error) {
        console.error('Erreur lors de la suppression du patient:', error);
      }
    } else {
      setDeleteConfirm(id);
      // Auto-reset après 3 secondes
      setTimeout(() => setDeleteConfirm(null), 3000);
    }
  };

  const filteredPatients = patients.filter(patient =>
    `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone.includes(searchTerm) ||
    patient.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header moderne */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                Gestion des Patients
              </h1>
              <p className="text-gray-600 text-lg">
                Gérez vos patients et leurs informations médicales en toute simplicité
              </p>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <Users className="w-4 h-4 mr-1" />
                  {patients.length} patients
                </span>
              </div>
            </div>
            <button
              onClick={handleAddPatient}
              className="group relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300 overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-blue-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Plus className="w-5 h-5 mr-3 relative z-10" />
              <span className="relative z-10">Nouveau Patient</span>
            </button>
          </div>
        </div>

        {/* Barre de recherche moderne */}
        <div className="bg-white/70 backdrop-blur-sm border border-white/20 shadow-xl rounded-3xl p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative group">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
                <input
                  type="text"
                  placeholder="Rechercher un patient par nom, téléphone ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-gray-50/50 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 text-gray-700 placeholder-gray-400"
                />
              </div>
            </div>
            <button className="inline-flex items-center px-6 py-4 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-2xl transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-gray-300">
              <Filter className="w-5 h-5 mr-2" />
              Filtres
            </button>
          </div>

          {/* Statistiques rapides */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Total Patients</p>
                  <p className="text-2xl font-bold">{patients.length}</p>
                </div>
                <Users className="w-8 h-8 text-blue-200" />
              </div>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Actifs</p>
                  <p className="text-2xl font-bold">{patients.filter(p => p.isActive).length}</p>
                </div>
                <Users className="w-8 h-8 text-green-200" />
              </div>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">Résultats</p>
                  <p className="text-2xl font-bold">{filteredPatients.length}</p>
                </div>
                <Search className="w-8 h-8 text-purple-200" />
              </div>
            </div>
          </div>
        </div>

        {/* Liste des patients en cartes modernes */}
        <div className="space-y-4">
          {filteredPatients.length === 0 ? (
            <div className="text-center py-16">
              <div className="bg-white/50 backdrop-blur-sm rounded-3xl p-12 border border-white/20">
                <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  {searchTerm ? 'Aucun patient trouvé' : 'Aucun patient enregistré'}
                </h3>
                <p className="text-gray-500 mb-6">
                  {searchTerm
                    ? 'Essayez de modifier votre recherche'
                    : 'Commencez par ajouter votre premier patient'
                  }
                </p>
                {!searchTerm && (
                  <button
                    onClick={handleAddPatient}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Ajouter un patient
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredPatients.map((patient) => (
                <div
                  key={patient.id}
                  className="group bg-white/80 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                >
                  {/* Header de la carte */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                        {patient.firstName[0]}{patient.lastName[0]}
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg">
                          {patient.firstName} {patient.lastName}
                        </h3>
                        <p className="text-gray-500 text-sm">
                          {patient.dateOfBirth ?
                            new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear() + ' ans'
                            : 'Âge non renseigné'
                          }
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <button
                        onClick={() => navigate(`/patients/${patient.id}`)}
                        className="p-2 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-xl transition-colors"
                        title="Voir le profil"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditPatient(patient)}
                        className="p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-xl transition-colors"
                        title="Modifier"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeletePatient(patient.id)}
                        className={`p-2 rounded-xl transition-colors ${
                          deleteConfirm === patient.id
                            ? 'bg-red-200 text-red-800'
                            : 'bg-red-100 hover:bg-red-200 text-red-600'
                        }`}
                        title={deleteConfirm === patient.id ? 'Cliquer pour confirmer' : 'Supprimer'}
                      >
                        {deleteConfirm === patient.id ? (
                          <AlertTriangle className="w-4 h-4" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Informations de contact */}
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-gray-600">
                      <Phone className="w-4 h-4 mr-3 text-blue-500" />
                      <span className="text-sm font-medium">{patient.phone}</span>
                    </div>
                    {patient.email && (
                      <div className="flex items-center text-gray-600">
                        <Mail className="w-4 h-4 mr-3 text-green-500" />
                        <span className="text-sm">{patient.email}</span>
                      </div>
                    )}
                  </div>

                  {/* Badges et statuts */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        patient.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {patient.isActive ? 'Actif' : 'Inactif'}
                      </span>
                      {patient.allergies && (
                        <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                          Allergies
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-400">
                      {new Date(patient.updatedAt).toLocaleDateString('fr-FR')}
                    </span>
                  </div>

                  {/* Actions rapides */}
                  <div className="mt-4 pt-4 border-t border-gray-100 flex space-x-2">
                    <button
                      onClick={() => navigate(`/patients/${patient.id}`)}
                      className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-4 rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
                    >
                      Voir profil
                    </button>
                    <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-xl text-sm font-medium transition-colors">
                      Nouveau RDV
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>



      {/* Modal pour ajouter/modifier un patient */}
      <PatientModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSavePatient}
        patient={selectedPatient}
        title={selectedPatient ? 'Modifier le patient' : 'Nouveau patient'}
      />
    </div>
  );
};

export default Patients;
