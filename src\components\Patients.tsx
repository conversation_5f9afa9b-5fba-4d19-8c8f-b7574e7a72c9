import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  Plus,
  Search,
  Phone,
  Mail,
  Calendar,
  Edit,
  Trash2,
  Eye,
  Filter,
  AlertTriangle
} from 'lucide-react';
import { prisma } from '../lib/prisma';
import PatientModal from './PatientModal';

// Type local pour éviter les problèmes d'import
type Gender = 'HOMME' | 'FEMME' | 'AUTRE';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const Patients: React.FC = () => {
  const navigate = useNavigate();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);


  useEffect(() => {
    loadPatients();
  }, []);

  const loadPatients = async () => {
    try {
      setIsLoading(true);
      const data = await prisma.patient.findMany();
      setPatients(data);
    } catch (error) {
      console.error('Erreur lors du chargement des patients:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddPatient = () => {
    setSelectedPatient(null);
    setIsModalOpen(true);
  };

  const handleEditPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setIsModalOpen(true);
  };

  const handleSavePatient = async (patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>) => {
    try {
      if (selectedPatient) {
        // Mise à jour
        await prisma.patient.update({
          where: { id: selectedPatient.id },
          data: patientData
        });
      } else {
        // Création
        await prisma.patient.create({
          data: patientData
        });
      }
      await loadPatients();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du patient:', error);
      throw error;
    }
  };

  const handleDeletePatient = async (id: string) => {
    if (deleteConfirm === id) {
      try {
        await prisma.patient.delete({ where: { id } });
        await loadPatients();
        setDeleteConfirm(null);
      } catch (error) {
        console.error('Erreur lors de la suppression du patient:', error);
      }
    } else {
      setDeleteConfirm(id);
      // Auto-reset après 3 secondes
      setTimeout(() => setDeleteConfirm(null), 3000);
    }
  };

  const filteredPatients = patients.filter(patient =>
    `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone.includes(searchTerm) ||
    patient.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Header simple */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Patients</h1>
              <p className="text-sm text-gray-500 mt-1">{patients.length} patients au total</p>
            </div>
            <button
              onClick={handleAddPatient}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Nouveau patient
            </button>
          </div>
        </div>

        {/* Barre de recherche simple */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher un patient..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Liste des patients simple */}
        <div className="space-y-3">
          {filteredPatients.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? 'Aucun patient trouvé' : 'Aucun patient enregistré'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchTerm
                  ? 'Essayez de modifier votre recherche'
                  : 'Commencez par ajouter votre premier patient'
                }
              </p>
              {!searchTerm && (
                <button
                  onClick={handleAddPatient}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter un patient
                </button>
              )}
            </div>
          ) : (
            filteredPatients.map((patient) => (
              <div
                key={patient.id}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-700">
                        {patient.firstName[0]}{patient.lastName[0]}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {patient.firstName} {patient.lastName}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{patient.phone}</span>
                        {patient.email && <span>{patient.email}</span>}
                        {patient.dateOfBirth && (
                          <span>
                            {new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear()} ans
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => navigate(`/patients/${patient.id}`)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Voir le profil"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleEditPatient(patient)}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Modifier"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeletePatient(patient.id)}
                      className={`p-2 transition-colors ${
                        deleteConfirm === patient.id
                          ? 'text-red-600 bg-red-50 rounded'
                          : 'text-gray-400 hover:text-red-600'
                      }`}
                      title={deleteConfirm === patient.id ? 'Cliquer pour confirmer' : 'Supprimer'}
                    >
                      {deleteConfirm === patient.id ? (
                        <AlertTriangle className="w-4 h-4" />
                      ) : (
                        <Trash2 className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>



      {/* Modal pour ajouter/modifier un patient */}
      <PatientModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSavePatient}
        patient={selectedPatient}
        title={selectedPatient ? 'Modifier le patient' : 'Nouveau patient'}
      />
    </div>
  );
};

export default Patients;
