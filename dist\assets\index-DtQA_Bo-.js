(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const x of h.addedNodes)x.tagName==="LINK"&&x.rel==="modulepreload"&&c(x)}).observe(document,{childList:!0,subtree:!0});function s(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function c(d){if(d.ep)return;d.ep=!0;const h=s(d);fetch(d.href,h)}})();var Vr={exports:{}},kn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var um;function Ey(){if(um)return kn;um=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function s(c,d,h){var x=null;if(h!==void 0&&(x=""+h),d.key!==void 0&&(x=""+d.key),"key"in d){h={};for(var N in d)N!=="key"&&(h[N]=d[N])}else h=d;return d=h.ref,{$$typeof:i,type:c,key:x,ref:d!==void 0?d:null,props:h}}return kn.Fragment=r,kn.jsx=s,kn.jsxs=s,kn}var im;function wy(){return im||(im=1,Vr.exports=Ey()),Vr.exports}var o=wy(),Zr={exports:{}},te={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cm;function My(){if(cm)return te;cm=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),x=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),E=Symbol.for("react.lazy"),_=Symbol.iterator;function H(p){return p===null||typeof p!="object"?null:(p=_&&p[_]||p["@@iterator"],typeof p=="function"?p:null)}var Z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},q=Object.assign,G={};function U(p,C,X){this.props=p,this.context=C,this.refs=G,this.updater=X||Z}U.prototype.isReactComponent={},U.prototype.setState=function(p,C){if(typeof p!="object"&&typeof p!="function"&&p!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,p,C,"setState")},U.prototype.forceUpdate=function(p){this.updater.enqueueForceUpdate(this,p,"forceUpdate")};function Y(){}Y.prototype=U.prototype;function I(p,C,X){this.props=p,this.context=C,this.refs=G,this.updater=X||Z}var ee=I.prototype=new Y;ee.constructor=I,q(ee,U.prototype),ee.isPureReactComponent=!0;var je=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},Ue=Object.prototype.hasOwnProperty;function _e(p,C,X,B,k,ce){return X=ce.ref,{$$typeof:i,type:p,key:C,ref:X!==void 0?X:null,props:ce}}function He(p,C){return _e(p.type,C,void 0,void 0,void 0,p.props)}function we(p){return typeof p=="object"&&p!==null&&p.$$typeof===i}function at(p){var C={"=":"=0",":":"=2"};return"$"+p.replace(/[=:]/g,function(X){return C[X]})}var mt=/\/+/g;function Ve(p,C){return typeof p=="object"&&p!==null&&p.key!=null?at(""+p.key):C.toString(36)}function _a(){}function za(p){switch(p.status){case"fulfilled":return p.value;case"rejected":throw p.reason;default:switch(typeof p.status=="string"?p.then(_a,_a):(p.status="pending",p.then(function(C){p.status==="pending"&&(p.status="fulfilled",p.value=C)},function(C){p.status==="pending"&&(p.status="rejected",p.reason=C)})),p.status){case"fulfilled":return p.value;case"rejected":throw p.reason}}throw p}function Ze(p,C,X,B,k){var ce=typeof p;(ce==="undefined"||ce==="boolean")&&(p=null);var P=!1;if(p===null)P=!0;else switch(ce){case"bigint":case"string":case"number":P=!0;break;case"object":switch(p.$$typeof){case i:case r:P=!0;break;case E:return P=p._init,Ze(P(p._payload),C,X,B,k)}}if(P)return k=k(p),P=B===""?"."+Ve(p,0):B,je(k)?(X="",P!=null&&(X=P.replace(mt,"$&/")+"/"),Ze(k,C,X,"",function(na){return na})):k!=null&&(we(k)&&(k=He(k,X+(k.key==null||p&&p.key===k.key?"":(""+k.key).replace(mt,"$&/")+"/")+P)),C.push(k)),1;P=0;var lt=B===""?".":B+":";if(je(p))for(var be=0;be<p.length;be++)B=p[be],ce=lt+Ve(B,be),P+=Ze(B,C,X,ce,k);else if(be=H(p),typeof be=="function")for(p=be.call(p),be=0;!(B=p.next()).done;)B=B.value,ce=lt+Ve(B,be++),P+=Ze(B,C,X,ce,k);else if(ce==="object"){if(typeof p.then=="function")return Ze(za(p),C,X,B,k);throw C=String(p),Error("Objects are not valid as a React child (found: "+(C==="[object Object]"?"object with keys {"+Object.keys(p).join(", ")+"}":C)+"). If you meant to render a collection of children, use an array instead.")}return P}function O(p,C,X){if(p==null)return p;var B=[],k=0;return Ze(p,B,"","",function(ce){return C.call(X,ce,k++)}),B}function L(p){if(p._status===-1){var C=p._result;C=C(),C.then(function(X){(p._status===0||p._status===-1)&&(p._status=1,p._result=X)},function(X){(p._status===0||p._status===-1)&&(p._status=2,p._result=X)}),p._status===-1&&(p._status=0,p._result=C)}if(p._status===1)return p._result.default;throw p._result}var W=typeof reportError=="function"?reportError:function(p){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var C=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof p=="object"&&p!==null&&typeof p.message=="string"?String(p.message):String(p),error:p});if(!window.dispatchEvent(C))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",p);return}console.error(p)};function ge(){}return te.Children={map:O,forEach:function(p,C,X){O(p,function(){C.apply(this,arguments)},X)},count:function(p){var C=0;return O(p,function(){C++}),C},toArray:function(p){return O(p,function(C){return C})||[]},only:function(p){if(!we(p))throw Error("React.Children.only expected to receive a single React element child.");return p}},te.Component=U,te.Fragment=s,te.Profiler=d,te.PureComponent=I,te.StrictMode=c,te.Suspense=v,te.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,te.__COMPILER_RUNTIME={__proto__:null,c:function(p){return F.H.useMemoCache(p)}},te.cache=function(p){return function(){return p.apply(null,arguments)}},te.cloneElement=function(p,C,X){if(p==null)throw Error("The argument must be a React element, but you passed "+p+".");var B=q({},p.props),k=p.key,ce=void 0;if(C!=null)for(P in C.ref!==void 0&&(ce=void 0),C.key!==void 0&&(k=""+C.key),C)!Ue.call(C,P)||P==="key"||P==="__self"||P==="__source"||P==="ref"&&C.ref===void 0||(B[P]=C[P]);var P=arguments.length-2;if(P===1)B.children=X;else if(1<P){for(var lt=Array(P),be=0;be<P;be++)lt[be]=arguments[be+2];B.children=lt}return _e(p.type,k,void 0,void 0,ce,B)},te.createContext=function(p){return p={$$typeof:x,_currentValue:p,_currentValue2:p,_threadCount:0,Provider:null,Consumer:null},p.Provider=p,p.Consumer={$$typeof:h,_context:p},p},te.createElement=function(p,C,X){var B,k={},ce=null;if(C!=null)for(B in C.key!==void 0&&(ce=""+C.key),C)Ue.call(C,B)&&B!=="key"&&B!=="__self"&&B!=="__source"&&(k[B]=C[B]);var P=arguments.length-2;if(P===1)k.children=X;else if(1<P){for(var lt=Array(P),be=0;be<P;be++)lt[be]=arguments[be+2];k.children=lt}if(p&&p.defaultProps)for(B in P=p.defaultProps,P)k[B]===void 0&&(k[B]=P[B]);return _e(p,ce,void 0,void 0,null,k)},te.createRef=function(){return{current:null}},te.forwardRef=function(p){return{$$typeof:N,render:p}},te.isValidElement=we,te.lazy=function(p){return{$$typeof:E,_payload:{_status:-1,_result:p},_init:L}},te.memo=function(p,C){return{$$typeof:y,type:p,compare:C===void 0?null:C}},te.startTransition=function(p){var C=F.T,X={};F.T=X;try{var B=p(),k=F.S;k!==null&&k(X,B),typeof B=="object"&&B!==null&&typeof B.then=="function"&&B.then(ge,W)}catch(ce){W(ce)}finally{F.T=C}},te.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},te.use=function(p){return F.H.use(p)},te.useActionState=function(p,C,X){return F.H.useActionState(p,C,X)},te.useCallback=function(p,C){return F.H.useCallback(p,C)},te.useContext=function(p){return F.H.useContext(p)},te.useDebugValue=function(){},te.useDeferredValue=function(p,C){return F.H.useDeferredValue(p,C)},te.useEffect=function(p,C,X){var B=F.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return B.useEffect(p,C)},te.useId=function(){return F.H.useId()},te.useImperativeHandle=function(p,C,X){return F.H.useImperativeHandle(p,C,X)},te.useInsertionEffect=function(p,C){return F.H.useInsertionEffect(p,C)},te.useLayoutEffect=function(p,C){return F.H.useLayoutEffect(p,C)},te.useMemo=function(p,C){return F.H.useMemo(p,C)},te.useOptimistic=function(p,C){return F.H.useOptimistic(p,C)},te.useReducer=function(p,C,X){return F.H.useReducer(p,C,X)},te.useRef=function(p){return F.H.useRef(p)},te.useState=function(p){return F.H.useState(p)},te.useSyncExternalStore=function(p,C,X){return F.H.useSyncExternalStore(p,C,X)},te.useTransition=function(){return F.H.useTransition()},te.version="19.1.0",te}var rm;function ls(){return rm||(rm=1,Zr.exports=My()),Zr.exports}var A=ls(),kr={exports:{}},Kn={},Kr={exports:{}},Jr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sm;function Ty(){return sm||(sm=1,function(i){function r(O,L){var W=O.length;O.push(L);e:for(;0<W;){var ge=W-1>>>1,p=O[ge];if(0<d(p,L))O[ge]=L,O[W]=p,W=ge;else break e}}function s(O){return O.length===0?null:O[0]}function c(O){if(O.length===0)return null;var L=O[0],W=O.pop();if(W!==L){O[0]=W;e:for(var ge=0,p=O.length,C=p>>>1;ge<C;){var X=2*(ge+1)-1,B=O[X],k=X+1,ce=O[k];if(0>d(B,W))k<p&&0>d(ce,B)?(O[ge]=ce,O[k]=W,ge=k):(O[ge]=B,O[X]=W,ge=X);else if(k<p&&0>d(ce,W))O[ge]=ce,O[k]=W,ge=k;else break e}}return L}function d(O,L){var W=O.sortIndex-L.sortIndex;return W!==0?W:O.id-L.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var x=Date,N=x.now();i.unstable_now=function(){return x.now()-N}}var v=[],y=[],E=1,_=null,H=3,Z=!1,q=!1,G=!1,U=!1,Y=typeof setTimeout=="function"?setTimeout:null,I=typeof clearTimeout=="function"?clearTimeout:null,ee=typeof setImmediate<"u"?setImmediate:null;function je(O){for(var L=s(y);L!==null;){if(L.callback===null)c(y);else if(L.startTime<=O)c(y),L.sortIndex=L.expirationTime,r(v,L);else break;L=s(y)}}function F(O){if(G=!1,je(O),!q)if(s(v)!==null)q=!0,Ue||(Ue=!0,Ve());else{var L=s(y);L!==null&&Ze(F,L.startTime-O)}}var Ue=!1,_e=-1,He=5,we=-1;function at(){return U?!0:!(i.unstable_now()-we<He)}function mt(){if(U=!1,Ue){var O=i.unstable_now();we=O;var L=!0;try{e:{q=!1,G&&(G=!1,I(_e),_e=-1),Z=!0;var W=H;try{t:{for(je(O),_=s(v);_!==null&&!(_.expirationTime>O&&at());){var ge=_.callback;if(typeof ge=="function"){_.callback=null,H=_.priorityLevel;var p=ge(_.expirationTime<=O);if(O=i.unstable_now(),typeof p=="function"){_.callback=p,je(O),L=!0;break t}_===s(v)&&c(v),je(O)}else c(v);_=s(v)}if(_!==null)L=!0;else{var C=s(y);C!==null&&Ze(F,C.startTime-O),L=!1}}break e}finally{_=null,H=W,Z=!1}L=void 0}}finally{L?Ve():Ue=!1}}}var Ve;if(typeof ee=="function")Ve=function(){ee(mt)};else if(typeof MessageChannel<"u"){var _a=new MessageChannel,za=_a.port2;_a.port1.onmessage=mt,Ve=function(){za.postMessage(null)}}else Ve=function(){Y(mt,0)};function Ze(O,L){_e=Y(function(){O(i.unstable_now())},L)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(O){O.callback=null},i.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):He=0<O?Math.floor(1e3/O):5},i.unstable_getCurrentPriorityLevel=function(){return H},i.unstable_next=function(O){switch(H){case 1:case 2:case 3:var L=3;break;default:L=H}var W=H;H=L;try{return O()}finally{H=W}},i.unstable_requestPaint=function(){U=!0},i.unstable_runWithPriority=function(O,L){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var W=H;H=O;try{return L()}finally{H=W}},i.unstable_scheduleCallback=function(O,L,W){var ge=i.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?ge+W:ge):W=ge,O){case 1:var p=-1;break;case 2:p=250;break;case 5:p=1073741823;break;case 4:p=1e4;break;default:p=5e3}return p=W+p,O={id:E++,callback:L,priorityLevel:O,startTime:W,expirationTime:p,sortIndex:-1},W>ge?(O.sortIndex=W,r(y,O),s(v)===null&&O===s(y)&&(G?(I(_e),_e=-1):G=!0,Ze(F,W-ge))):(O.sortIndex=p,r(v,O),q||Z||(q=!0,Ue||(Ue=!0,Ve()))),O},i.unstable_shouldYield=at,i.unstable_wrapCallback=function(O){var L=H;return function(){var W=H;H=L;try{return O.apply(this,arguments)}finally{H=W}}}}(Jr)),Jr}var fm;function Ay(){return fm||(fm=1,Kr.exports=Ty()),Kr.exports}var Wr={exports:{}},Je={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var om;function Dy(){if(om)return Je;om=1;var i=ls();function r(v){var y="https://react.dev/errors/"+v;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var E=2;E<arguments.length;E++)y+="&args[]="+encodeURIComponent(arguments[E])}return"Minified React error #"+v+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(r(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},d=Symbol.for("react.portal");function h(v,y,E){var _=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:_==null?null:""+_,children:v,containerInfo:y,implementation:E}}var x=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function N(v,y){if(v==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Je.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,Je.createPortal=function(v,y){var E=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(r(299));return h(v,y,null,E)},Je.flushSync=function(v){var y=x.T,E=c.p;try{if(x.T=null,c.p=2,v)return v()}finally{x.T=y,c.p=E,c.d.f()}},Je.preconnect=function(v,y){typeof v=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,c.d.C(v,y))},Je.prefetchDNS=function(v){typeof v=="string"&&c.d.D(v)},Je.preinit=function(v,y){if(typeof v=="string"&&y&&typeof y.as=="string"){var E=y.as,_=N(E,y.crossOrigin),H=typeof y.integrity=="string"?y.integrity:void 0,Z=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;E==="style"?c.d.S(v,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:_,integrity:H,fetchPriority:Z}):E==="script"&&c.d.X(v,{crossOrigin:_,integrity:H,fetchPriority:Z,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Je.preinitModule=function(v,y){if(typeof v=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var E=N(y.as,y.crossOrigin);c.d.M(v,{crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&c.d.M(v)},Je.preload=function(v,y){if(typeof v=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var E=y.as,_=N(E,y.crossOrigin);c.d.L(v,E,{crossOrigin:_,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Je.preloadModule=function(v,y){if(typeof v=="string")if(y){var E=N(y.as,y.crossOrigin);c.d.m(v,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else c.d.m(v)},Je.requestFormReset=function(v){c.d.r(v)},Je.unstable_batchedUpdates=function(v,y){return v(y)},Je.useFormState=function(v,y,E){return x.H.useFormState(v,y,E)},Je.useFormStatus=function(){return x.H.useHostTransitionStatus()},Je.version="19.1.0",Je}var dm;function Oy(){if(dm)return Wr.exports;dm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),Wr.exports=Dy(),Wr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mm;function Ry(){if(mm)return Kn;mm=1;var i=Ay(),r=ls(),s=Oy();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function x(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function N(e){if(h(e)!==e)throw Error(c(188))}function v(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(c(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var u=n.alternate;if(u===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===a)return N(n),e;if(u===l)return N(n),t;u=u.sibling}throw Error(c(188))}if(a.return!==l.return)a=n,l=u;else{for(var f=!1,m=n.child;m;){if(m===a){f=!0,a=n,l=u;break}if(m===l){f=!0,l=n,a=u;break}m=m.sibling}if(!f){for(m=u.child;m;){if(m===a){f=!0,a=u,l=n;break}if(m===l){f=!0,l=u,a=n;break}m=m.sibling}if(!f)throw Error(c(189))}}if(a.alternate!==l)throw Error(c(190))}if(a.tag!==3)throw Error(c(188));return a.stateNode.current===a?e:t}function y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=y(e),t!==null)return t;e=e.sibling}return null}var E=Object.assign,_=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),q=Symbol.for("react.fragment"),G=Symbol.for("react.strict_mode"),U=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),I=Symbol.for("react.consumer"),ee=Symbol.for("react.context"),je=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),Ue=Symbol.for("react.suspense_list"),_e=Symbol.for("react.memo"),He=Symbol.for("react.lazy"),we=Symbol.for("react.activity"),at=Symbol.for("react.memo_cache_sentinel"),mt=Symbol.iterator;function Ve(e){return e===null||typeof e!="object"?null:(e=mt&&e[mt]||e["@@iterator"],typeof e=="function"?e:null)}var _a=Symbol.for("react.client.reference");function za(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===_a?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case q:return"Fragment";case U:return"Profiler";case G:return"StrictMode";case F:return"Suspense";case Ue:return"SuspenseList";case we:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Z:return"Portal";case ee:return(e.displayName||"Context")+".Provider";case I:return(e._context.displayName||"Context")+".Consumer";case je:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _e:return t=e.displayName||null,t!==null?t:za(e.type)||"Memo";case He:t=e._payload,e=e._init;try{return za(e(t))}catch{}}return null}var Ze=Array.isArray,O=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W={pending:!1,data:null,method:null,action:null},ge=[],p=-1;function C(e){return{current:e}}function X(e){0>p||(e.current=ge[p],ge[p]=null,p--)}function B(e,t){p++,ge[p]=e.current,e.current=t}var k=C(null),ce=C(null),P=C(null),lt=C(null);function be(e,t){switch(B(P,t),B(ce,e),B(k,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?zd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=zd(t),e=Cd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}X(k),B(k,e)}function na(){X(k),X(ce),X(P)}function Di(e){e.memoizedState!==null&&B(lt,e);var t=k.current,a=Cd(t,e.type);t!==a&&(B(ce,e),B(k,a))}function tu(e){ce.current===e&&(X(k),X(ce)),lt.current===e&&(X(lt),Xn._currentValue=W)}var Oi=Object.prototype.hasOwnProperty,Ri=i.unstable_scheduleCallback,_i=i.unstable_cancelCallback,ah=i.unstable_shouldYield,lh=i.unstable_requestPaint,At=i.unstable_now,nh=i.unstable_getCurrentPriorityLevel,os=i.unstable_ImmediatePriority,ds=i.unstable_UserBlockingPriority,au=i.unstable_NormalPriority,uh=i.unstable_LowPriority,ms=i.unstable_IdlePriority,ih=i.log,ch=i.unstable_setDisableYieldValue,Jl=null,nt=null;function ua(e){if(typeof ih=="function"&&ch(e),nt&&typeof nt.setStrictMode=="function")try{nt.setStrictMode(Jl,e)}catch{}}var ut=Math.clz32?Math.clz32:fh,rh=Math.log,sh=Math.LN2;function fh(e){return e>>>=0,e===0?32:31-(rh(e)/sh|0)|0}var lu=256,nu=4194304;function Ca(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function uu(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,u=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var m=l&134217727;return m!==0?(l=m&~u,l!==0?n=Ca(l):(f&=m,f!==0?n=Ca(f):a||(a=m&~e,a!==0&&(n=Ca(a))))):(m=l&~u,m!==0?n=Ca(m):f!==0?n=Ca(f):a||(a=l&~e,a!==0&&(n=Ca(a)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,a=t&-t,u>=a||u===32&&(a&4194048)!==0)?t:n}function Wl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function oh(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function hs(){var e=lu;return lu<<=1,(lu&4194048)===0&&(lu=256),e}function ys(){var e=nu;return nu<<=1,(nu&62914560)===0&&(nu=4194304),e}function zi(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function $l(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function dh(e,t,a,l,n,u){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var m=e.entanglements,g=e.expirationTimes,w=e.hiddenUpdates;for(a=f&~a;0<a;){var D=31-ut(a),z=1<<D;m[D]=0,g[D]=-1;var M=w[D];if(M!==null)for(w[D]=null,D=0;D<M.length;D++){var T=M[D];T!==null&&(T.lane&=-536870913)}a&=~z}l!==0&&gs(e,l,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(f&~t))}function gs(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-ut(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function vs(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-ut(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function Ci(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ui(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function xs(){var e=L.p;return e!==0?e:(e=window.event,e===void 0?32:Id(e.type))}function mh(e,t){var a=L.p;try{return L.p=e,t()}finally{L.p=a}}var ia=Math.random().toString(36).slice(2),ke="__reactFiber$"+ia,Fe="__reactProps$"+ia,ll="__reactContainer$"+ia,Hi="__reactEvents$"+ia,hh="__reactListeners$"+ia,yh="__reactHandles$"+ia,ps="__reactResources$"+ia,Fl="__reactMarker$"+ia;function Yi(e){delete e[ke],delete e[Fe],delete e[Hi],delete e[hh],delete e[yh]}function nl(e){var t=e[ke];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ll]||a[ke]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=qd(e);e!==null;){if(a=e[ke])return a;e=qd(e)}return t}e=a,a=e.parentNode}return null}function ul(e){if(e=e[ke]||e[ll]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Pl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function il(e){var t=e[ps];return t||(t=e[ps]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ye(e){e[Fl]=!0}var bs=new Set,Ss={};function Ua(e,t){cl(e,t),cl(e+"Capture",t)}function cl(e,t){for(Ss[e]=t,e=0;e<t.length;e++)bs.add(t[e])}var gh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ns={},js={};function vh(e){return Oi.call(js,e)?!0:Oi.call(Ns,e)?!1:gh.test(e)?js[e]=!0:(Ns[e]=!0,!1)}function iu(e,t,a){if(vh(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function cu(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function qt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var qi,Es;function rl(e){if(qi===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);qi=t&&t[1]||"",Es=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+qi+e+Es}var Bi=!1;function Li(e,t){if(!e||Bi)return"";Bi=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(T){var M=T}Reflect.construct(e,[],z)}else{try{z.call()}catch(T){M=T}e.call(z.prototype)}}else{try{throw Error()}catch(T){M=T}(z=e())&&typeof z.catch=="function"&&z.catch(function(){})}}catch(T){if(T&&M&&typeof T.stack=="string")return[T.stack,M.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),f=u[0],m=u[1];if(f&&m){var g=f.split(`
`),w=m.split(`
`);for(n=l=0;l<g.length&&!g[l].includes("DetermineComponentFrameRoot");)l++;for(;n<w.length&&!w[n].includes("DetermineComponentFrameRoot");)n++;if(l===g.length||n===w.length)for(l=g.length-1,n=w.length-1;1<=l&&0<=n&&g[l]!==w[n];)n--;for(;1<=l&&0<=n;l--,n--)if(g[l]!==w[n]){if(l!==1||n!==1)do if(l--,n--,0>n||g[l]!==w[n]){var D=`
`+g[l].replace(" at new "," at ");return e.displayName&&D.includes("<anonymous>")&&(D=D.replace("<anonymous>",e.displayName)),D}while(1<=l&&0<=n);break}}}finally{Bi=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?rl(a):""}function xh(e){switch(e.tag){case 26:case 27:case 5:return rl(e.type);case 16:return rl("Lazy");case 13:return rl("Suspense");case 19:return rl("SuspenseList");case 0:case 15:return Li(e.type,!1);case 11:return Li(e.type.render,!1);case 1:return Li(e.type,!0);case 31:return rl("Activity");default:return""}}function ws(e){try{var t="";do t+=xh(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function ht(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ms(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ph(e){var t=Ms(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,u=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,u.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ru(e){e._valueTracker||(e._valueTracker=ph(e))}function Ts(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Ms(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function su(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var bh=/[\n"\\]/g;function yt(e){return e.replace(bh,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Xi(e,t,a,l,n,u,f,m){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+ht(t)):e.value!==""+ht(t)&&(e.value=""+ht(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?Gi(e,f,ht(t)):a!=null?Gi(e,f,ht(a)):l!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+ht(m):e.removeAttribute("name")}function As(e,t,a,l,n,u,f,m){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||a!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;a=a!=null?""+ht(a):"",t=t!=null?""+ht(t):a,m||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=m?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function Gi(e,t,a){t==="number"&&su(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function sl(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+ht(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Ds(e,t,a){if(t!=null&&(t=""+ht(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+ht(a):""}function Os(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(c(92));if(Ze(l)){if(1<l.length)throw Error(c(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=ht(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function fl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Sh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Rs(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||Sh.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function _s(e,t,a){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&Rs(e,n,l)}else for(var u in t)t.hasOwnProperty(u)&&Rs(e,u,t[u])}function Qi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),jh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function fu(e){return jh.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Vi=null;function Zi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ol=null,dl=null;function zs(e){var t=ul(e);if(t&&(e=t.stateNode)){var a=e[Fe]||null;e:switch(e=t.stateNode,t.type){case"input":if(Xi(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+yt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[Fe]||null;if(!n)throw Error(c(90));Xi(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Ts(l)}break e;case"textarea":Ds(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&sl(e,!!a.multiple,t,!1)}}}var ki=!1;function Cs(e,t,a){if(ki)return e(t,a);ki=!0;try{var l=e(t);return l}finally{if(ki=!1,(ol!==null||dl!==null)&&(Wu(),ol&&(t=ol,e=dl,dl=ol=null,zs(t),e)))for(t=0;t<e.length;t++)zs(e[t])}}function Il(e,t){var a=e.stateNode;if(a===null)return null;var l=a[Fe]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(c(231,t,typeof a));return a}var Bt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ki=!1;if(Bt)try{var en={};Object.defineProperty(en,"passive",{get:function(){Ki=!0}}),window.addEventListener("test",en,en),window.removeEventListener("test",en,en)}catch{Ki=!1}var ca=null,Ji=null,ou=null;function Us(){if(ou)return ou;var e,t=Ji,a=t.length,l,n="value"in ca?ca.value:ca.textContent,u=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[u-l];l++);return ou=n.slice(e,1<l?1-l:void 0)}function du(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function mu(){return!0}function Hs(){return!1}function Pe(e){function t(a,l,n,u,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(a=e[m],this[m]=a?a(u):u[m]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?mu:Hs,this.isPropagationStopped=Hs,this}return E(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=mu)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=mu)},persist:function(){},isPersistent:mu}),t}var Ha={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hu=Pe(Ha),tn=E({},Ha,{view:0,detail:0}),Eh=Pe(tn),Wi,$i,an,yu=E({},tn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Pi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==an&&(an&&e.type==="mousemove"?(Wi=e.screenX-an.screenX,$i=e.screenY-an.screenY):$i=Wi=0,an=e),Wi)},movementY:function(e){return"movementY"in e?e.movementY:$i}}),Ys=Pe(yu),wh=E({},yu,{dataTransfer:0}),Mh=Pe(wh),Th=E({},tn,{relatedTarget:0}),Fi=Pe(Th),Ah=E({},Ha,{animationName:0,elapsedTime:0,pseudoElement:0}),Dh=Pe(Ah),Oh=E({},Ha,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Rh=Pe(Oh),_h=E({},Ha,{data:0}),qs=Pe(_h),zh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ch={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Uh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Hh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Uh[e])?!!t[e]:!1}function Pi(){return Hh}var Yh=E({},tn,{key:function(e){if(e.key){var t=zh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=du(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ch[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Pi,charCode:function(e){return e.type==="keypress"?du(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?du(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),qh=Pe(Yh),Bh=E({},yu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bs=Pe(Bh),Lh=E({},tn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Pi}),Xh=Pe(Lh),Gh=E({},Ha,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qh=Pe(Gh),Vh=E({},yu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Zh=Pe(Vh),kh=E({},Ha,{newState:0,oldState:0}),Kh=Pe(kh),Jh=[9,13,27,32],Ii=Bt&&"CompositionEvent"in window,ln=null;Bt&&"documentMode"in document&&(ln=document.documentMode);var Wh=Bt&&"TextEvent"in window&&!ln,Ls=Bt&&(!Ii||ln&&8<ln&&11>=ln),Xs=" ",Gs=!1;function Qs(e,t){switch(e){case"keyup":return Jh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Vs(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ml=!1;function $h(e,t){switch(e){case"compositionend":return Vs(t);case"keypress":return t.which!==32?null:(Gs=!0,Xs);case"textInput":return e=t.data,e===Xs&&Gs?null:e;default:return null}}function Fh(e,t){if(ml)return e==="compositionend"||!Ii&&Qs(e,t)?(e=Us(),ou=Ji=ca=null,ml=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ls&&t.locale!=="ko"?null:t.data;default:return null}}var Ph={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Zs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ph[e.type]:t==="textarea"}function ks(e,t,a,l){ol?dl?dl.push(l):dl=[l]:ol=l,t=ti(t,"onChange"),0<t.length&&(a=new hu("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var nn=null,un=null;function Ih(e){Ad(e,0)}function gu(e){var t=Pl(e);if(Ts(t))return e}function Ks(e,t){if(e==="change")return t}var Js=!1;if(Bt){var ec;if(Bt){var tc="oninput"in document;if(!tc){var Ws=document.createElement("div");Ws.setAttribute("oninput","return;"),tc=typeof Ws.oninput=="function"}ec=tc}else ec=!1;Js=ec&&(!document.documentMode||9<document.documentMode)}function $s(){nn&&(nn.detachEvent("onpropertychange",Fs),un=nn=null)}function Fs(e){if(e.propertyName==="value"&&gu(un)){var t=[];ks(t,un,e,Zi(e)),Cs(Ih,t)}}function e0(e,t,a){e==="focusin"?($s(),nn=t,un=a,nn.attachEvent("onpropertychange",Fs)):e==="focusout"&&$s()}function t0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return gu(un)}function a0(e,t){if(e==="click")return gu(t)}function l0(e,t){if(e==="input"||e==="change")return gu(t)}function n0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var it=typeof Object.is=="function"?Object.is:n0;function cn(e,t){if(it(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!Oi.call(t,n)||!it(e[n],t[n]))return!1}return!0}function Ps(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Is(e,t){var a=Ps(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Ps(a)}}function ef(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ef(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function tf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=su(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=su(e.document)}return t}function ac(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var u0=Bt&&"documentMode"in document&&11>=document.documentMode,hl=null,lc=null,rn=null,nc=!1;function af(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;nc||hl==null||hl!==su(l)||(l=hl,"selectionStart"in l&&ac(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),rn&&cn(rn,l)||(rn=l,l=ti(lc,"onSelect"),0<l.length&&(t=new hu("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=hl)))}function Ya(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var yl={animationend:Ya("Animation","AnimationEnd"),animationiteration:Ya("Animation","AnimationIteration"),animationstart:Ya("Animation","AnimationStart"),transitionrun:Ya("Transition","TransitionRun"),transitionstart:Ya("Transition","TransitionStart"),transitioncancel:Ya("Transition","TransitionCancel"),transitionend:Ya("Transition","TransitionEnd")},uc={},lf={};Bt&&(lf=document.createElement("div").style,"AnimationEvent"in window||(delete yl.animationend.animation,delete yl.animationiteration.animation,delete yl.animationstart.animation),"TransitionEvent"in window||delete yl.transitionend.transition);function qa(e){if(uc[e])return uc[e];if(!yl[e])return e;var t=yl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in lf)return uc[e]=t[a];return e}var nf=qa("animationend"),uf=qa("animationiteration"),cf=qa("animationstart"),i0=qa("transitionrun"),c0=qa("transitionstart"),r0=qa("transitioncancel"),rf=qa("transitionend"),sf=new Map,ic="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ic.push("scrollEnd");function jt(e,t){sf.set(e,t),Ua(t,[e])}var ff=new WeakMap;function gt(e,t){if(typeof e=="object"&&e!==null){var a=ff.get(e);return a!==void 0?a:(t={value:e,source:t,stack:ws(t)},ff.set(e,t),t)}return{value:e,source:t,stack:ws(t)}}var vt=[],gl=0,cc=0;function vu(){for(var e=gl,t=cc=gl=0;t<e;){var a=vt[t];vt[t++]=null;var l=vt[t];vt[t++]=null;var n=vt[t];vt[t++]=null;var u=vt[t];if(vt[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}u!==0&&of(a,n,u)}}function xu(e,t,a,l){vt[gl++]=e,vt[gl++]=t,vt[gl++]=a,vt[gl++]=l,cc|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function rc(e,t,a,l){return xu(e,t,a,l),pu(e)}function vl(e,t){return xu(e,null,null,t),pu(e)}function of(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,u=e.return;u!==null;)u.childLanes|=a,l=u.alternate,l!==null&&(l.childLanes|=a),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-ut(a),e=u.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),u):null}function pu(e){if(50<zn)throw zn=0,hr=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var xl={};function s0(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ct(e,t,a,l){return new s0(e,t,a,l)}function sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lt(e,t){var a=e.alternate;return a===null?(a=ct(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function df(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function bu(e,t,a,l,n,u){var f=0;if(l=e,typeof e=="function")sc(e)&&(f=1);else if(typeof e=="string")f=oy(e,a,k.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case we:return e=ct(31,a,t,n),e.elementType=we,e.lanes=u,e;case q:return Ba(a.children,n,u,t);case G:f=8,n|=24;break;case U:return e=ct(12,a,t,n|2),e.elementType=U,e.lanes=u,e;case F:return e=ct(13,a,t,n),e.elementType=F,e.lanes=u,e;case Ue:return e=ct(19,a,t,n),e.elementType=Ue,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Y:case ee:f=10;break e;case I:f=9;break e;case je:f=11;break e;case _e:f=14;break e;case He:f=16,l=null;break e}f=29,a=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=ct(f,a,t,n),t.elementType=e,t.type=l,t.lanes=u,t}function Ba(e,t,a,l){return e=ct(7,e,l,t),e.lanes=a,e}function fc(e,t,a){return e=ct(6,e,null,t),e.lanes=a,e}function oc(e,t,a){return t=ct(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var pl=[],bl=0,Su=null,Nu=0,xt=[],pt=0,La=null,Xt=1,Gt="";function Xa(e,t){pl[bl++]=Nu,pl[bl++]=Su,Su=e,Nu=t}function mf(e,t,a){xt[pt++]=Xt,xt[pt++]=Gt,xt[pt++]=La,La=e;var l=Xt;e=Gt;var n=32-ut(l)-1;l&=~(1<<n),a+=1;var u=32-ut(t)+n;if(30<u){var f=n-n%5;u=(l&(1<<f)-1).toString(32),l>>=f,n-=f,Xt=1<<32-ut(t)+n|a<<n|l,Gt=u+e}else Xt=1<<u|a<<n|l,Gt=e}function dc(e){e.return!==null&&(Xa(e,1),mf(e,1,0))}function mc(e){for(;e===Su;)Su=pl[--bl],pl[bl]=null,Nu=pl[--bl],pl[bl]=null;for(;e===La;)La=xt[--pt],xt[pt]=null,Gt=xt[--pt],xt[pt]=null,Xt=xt[--pt],xt[pt]=null}var We=null,Me=null,se=!1,Ga=null,Dt=!1,hc=Error(c(519));function Qa(e){var t=Error(c(418,""));throw on(gt(t,e)),hc}function hf(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[ke]=e,t[Fe]=l,a){case"dialog":ue("cancel",t),ue("close",t);break;case"iframe":case"object":case"embed":ue("load",t);break;case"video":case"audio":for(a=0;a<Un.length;a++)ue(Un[a],t);break;case"source":ue("error",t);break;case"img":case"image":case"link":ue("error",t),ue("load",t);break;case"details":ue("toggle",t);break;case"input":ue("invalid",t),As(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ru(t);break;case"select":ue("invalid",t);break;case"textarea":ue("invalid",t),Os(t,l.value,l.defaultValue,l.children),ru(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||_d(t.textContent,a)?(l.popover!=null&&(ue("beforetoggle",t),ue("toggle",t)),l.onScroll!=null&&ue("scroll",t),l.onScrollEnd!=null&&ue("scrollend",t),l.onClick!=null&&(t.onclick=ai),t=!0):t=!1,t||Qa(e)}function yf(e){for(We=e.return;We;)switch(We.tag){case 5:case 13:Dt=!1;return;case 27:case 3:Dt=!0;return;default:We=We.return}}function sn(e){if(e!==We)return!1;if(!se)return yf(e),se=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Or(e.type,e.memoizedProps)),a=!a),a&&Me&&Qa(e),yf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Me=wt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Me=null}}else t===27?(t=Me,ja(e.type)?(e=Cr,Cr=null,Me=e):Me=t):Me=We?wt(e.stateNode.nextSibling):null;return!0}function fn(){Me=We=null,se=!1}function gf(){var e=Ga;return e!==null&&(tt===null?tt=e:tt.push.apply(tt,e),Ga=null),e}function on(e){Ga===null?Ga=[e]:Ga.push(e)}var yc=C(null),Va=null,Qt=null;function ra(e,t,a){B(yc,t._currentValue),t._currentValue=a}function Vt(e){e._currentValue=yc.current,X(yc)}function gc(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function vc(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var f=n.child;u=u.firstContext;e:for(;u!==null;){var m=u;u=n;for(var g=0;g<t.length;g++)if(m.context===t[g]){u.lanes|=a,m=u.alternate,m!==null&&(m.lanes|=a),gc(u.return,a,e),l||(f=null);break e}u=m.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(c(341));f.lanes|=a,u=f.alternate,u!==null&&(u.lanes|=a),gc(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function dn(e,t,a,l){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(c(387));if(f=f.memoizedProps,f!==null){var m=n.type;it(n.pendingProps.value,f.value)||(e!==null?e.push(m):e=[m])}}else if(n===lt.current){if(f=n.alternate,f===null)throw Error(c(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Xn):e=[Xn])}n=n.return}e!==null&&vc(t,e,a,l),t.flags|=262144}function ju(e){for(e=e.firstContext;e!==null;){if(!it(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Za(e){Va=e,Qt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ke(e){return vf(Va,e)}function Eu(e,t){return Va===null&&Za(e),vf(e,t)}function vf(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Qt===null){if(e===null)throw Error(c(308));Qt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Qt=Qt.next=t;return a}var f0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},o0=i.unstable_scheduleCallback,d0=i.unstable_NormalPriority,ze={$$typeof:ee,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function xc(){return{controller:new f0,data:new Map,refCount:0}}function mn(e){e.refCount--,e.refCount===0&&o0(d0,function(){e.controller.abort()})}var hn=null,pc=0,Sl=0,Nl=null;function m0(e,t){if(hn===null){var a=hn=[];pc=0,Sl=Sr(),Nl={status:"pending",value:void 0,then:function(l){a.push(l)}}}return pc++,t.then(xf,xf),t}function xf(){if(--pc===0&&hn!==null){Nl!==null&&(Nl.status="fulfilled");var e=hn;hn=null,Sl=0,Nl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function h0(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var pf=O.S;O.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&m0(e,t),pf!==null&&pf(e,t)};var ka=C(null);function bc(){var e=ka.current;return e!==null?e:xe.pooledCache}function wu(e,t){t===null?B(ka,ka.current):B(ka,t.pool)}function bf(){var e=bc();return e===null?null:{parent:ze._currentValue,pool:e}}var yn=Error(c(460)),Sf=Error(c(474)),Mu=Error(c(542)),Sc={then:function(){}};function Nf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Tu(){}function jf(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Tu,Tu),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,wf(e),e;default:if(typeof t.status=="string")t.then(Tu,Tu);else{if(e=xe,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,wf(e),e}throw gn=t,yn}}var gn=null;function Ef(){if(gn===null)throw Error(c(459));var e=gn;return gn=null,e}function wf(e){if(e===yn||e===Mu)throw Error(c(483))}var sa=!1;function Nc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function jc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function fa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function oa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(fe&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=pu(e),of(e,null,a),t}return xu(e,l,t,a),pu(e)}function vn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,vs(e,a)}}function Ec(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,u=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};u===null?n=u=f:u=u.next=f,a=a.next}while(a!==null);u===null?n=u=t:u=u.next=t}else n=u=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var wc=!1;function xn(){if(wc){var e=Nl;if(e!==null)throw e}}function pn(e,t,a,l){wc=!1;var n=e.updateQueue;sa=!1;var u=n.firstBaseUpdate,f=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var g=m,w=g.next;g.next=null,f===null?u=w:f.next=w,f=g;var D=e.alternate;D!==null&&(D=D.updateQueue,m=D.lastBaseUpdate,m!==f&&(m===null?D.firstBaseUpdate=w:m.next=w,D.lastBaseUpdate=g))}if(u!==null){var z=n.baseState;f=0,D=w=g=null,m=u;do{var M=m.lane&-536870913,T=M!==m.lane;if(T?(ie&M)===M:(l&M)===M){M!==0&&M===Sl&&(wc=!0),D!==null&&(D=D.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var $=e,K=m;M=t;var ye=a;switch(K.tag){case 1:if($=K.payload,typeof $=="function"){z=$.call(ye,z,M);break e}z=$;break e;case 3:$.flags=$.flags&-65537|128;case 0:if($=K.payload,M=typeof $=="function"?$.call(ye,z,M):$,M==null)break e;z=E({},z,M);break e;case 2:sa=!0}}M=m.callback,M!==null&&(e.flags|=64,T&&(e.flags|=8192),T=n.callbacks,T===null?n.callbacks=[M]:T.push(M))}else T={lane:M,tag:m.tag,payload:m.payload,callback:m.callback,next:null},D===null?(w=D=T,g=z):D=D.next=T,f|=M;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;T=m,m=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);D===null&&(g=z),n.baseState=g,n.firstBaseUpdate=w,n.lastBaseUpdate=D,u===null&&(n.shared.lanes=0),pa|=f,e.lanes=f,e.memoizedState=z}}function Mf(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function Tf(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Mf(a[e],t)}var jl=C(null),Au=C(0);function Af(e,t){e=Ft,B(Au,e),B(jl,t),Ft=e|t.baseLanes}function Mc(){B(Au,Ft),B(jl,jl.current)}function Tc(){Ft=Au.current,X(jl),X(Au)}var da=0,ae=null,me=null,Oe=null,Du=!1,El=!1,Ka=!1,Ou=0,bn=0,wl=null,y0=0;function Ae(){throw Error(c(321))}function Ac(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!it(e[a],t[a]))return!1;return!0}function Dc(e,t,a,l,n,u){return da=u,ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,O.H=e===null||e.memoizedState===null?oo:mo,Ka=!1,u=a(l,n),Ka=!1,El&&(u=Of(t,a,l,n)),Df(e),u}function Df(e){O.H=Hu;var t=me!==null&&me.next!==null;if(da=0,Oe=me=ae=null,Du=!1,bn=0,wl=null,t)throw Error(c(300));e===null||qe||(e=e.dependencies,e!==null&&ju(e)&&(qe=!0))}function Of(e,t,a,l){ae=e;var n=0;do{if(El&&(wl=null),bn=0,El=!1,25<=n)throw Error(c(301));if(n+=1,Oe=me=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}O.H=N0,u=t(a,l)}while(El);return u}function g0(){var e=O.H,t=e.useState()[0];return t=typeof t.then=="function"?Sn(t):t,e=e.useState()[0],(me!==null?me.memoizedState:null)!==e&&(ae.flags|=1024),t}function Oc(){var e=Ou!==0;return Ou=0,e}function Rc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function _c(e){if(Du){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Du=!1}da=0,Oe=me=ae=null,El=!1,bn=Ou=0,wl=null}function Ie(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Oe===null?ae.memoizedState=Oe=e:Oe=Oe.next=e,Oe}function Re(){if(me===null){var e=ae.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=Oe===null?ae.memoizedState:Oe.next;if(t!==null)Oe=t,me=e;else{if(e===null)throw ae.alternate===null?Error(c(467)):Error(c(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},Oe===null?ae.memoizedState=Oe=e:Oe=Oe.next=e}return Oe}function zc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Sn(e){var t=bn;return bn+=1,wl===null&&(wl=[]),e=jf(wl,e,t),t=ae,(Oe===null?t.memoizedState:Oe.next)===null&&(t=t.alternate,O.H=t===null||t.memoizedState===null?oo:mo),e}function Ru(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Sn(e);if(e.$$typeof===ee)return Ke(e)}throw Error(c(438,String(e)))}function Cc(e){var t=null,a=ae.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ae.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=zc(),ae.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=at;return t.index++,a}function Zt(e,t){return typeof t=="function"?t(e):t}function _u(e){var t=Re();return Uc(t,me,e)}function Uc(e,t,a){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=a;var n=e.baseQueue,u=l.pending;if(u!==null){if(n!==null){var f=n.next;n.next=u.next,u.next=f}t.baseQueue=n=u,l.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var m=f=null,g=null,w=t,D=!1;do{var z=w.lane&-536870913;if(z!==w.lane?(ie&z)===z:(da&z)===z){var M=w.revertLane;if(M===0)g!==null&&(g=g.next={lane:0,revertLane:0,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null}),z===Sl&&(D=!0);else if((da&M)===M){w=w.next,M===Sl&&(D=!0);continue}else z={lane:0,revertLane:w.revertLane,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null},g===null?(m=g=z,f=u):g=g.next=z,ae.lanes|=M,pa|=M;z=w.action,Ka&&a(u,z),u=w.hasEagerState?w.eagerState:a(u,z)}else M={lane:z,revertLane:w.revertLane,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null},g===null?(m=g=M,f=u):g=g.next=M,ae.lanes|=z,pa|=z;w=w.next}while(w!==null&&w!==t);if(g===null?f=u:g.next=m,!it(u,e.memoizedState)&&(qe=!0,D&&(a=Nl,a!==null)))throw a;e.memoizedState=u,e.baseState=f,e.baseQueue=g,l.lastRenderedState=u}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Hc(e){var t=Re(),a=t.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,u=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do u=e(u,f.action),f=f.next;while(f!==n);it(u,t.memoizedState)||(qe=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),a.lastRenderedState=u}return[u,l]}function Rf(e,t,a){var l=ae,n=Re(),u=se;if(u){if(a===void 0)throw Error(c(407));a=a()}else a=t();var f=!it((me||n).memoizedState,a);f&&(n.memoizedState=a,qe=!0),n=n.queue;var m=Cf.bind(null,l,n,e);if(Nn(2048,8,m,[e]),n.getSnapshot!==t||f||Oe!==null&&Oe.memoizedState.tag&1){if(l.flags|=2048,Ml(9,zu(),zf.bind(null,l,n,a,t),null),xe===null)throw Error(c(349));u||(da&124)!==0||_f(l,t,a)}return a}function _f(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ae.updateQueue,t===null?(t=zc(),ae.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function zf(e,t,a,l){t.value=a,t.getSnapshot=l,Uf(t)&&Hf(e)}function Cf(e,t,a){return a(function(){Uf(t)&&Hf(e)})}function Uf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!it(e,a)}catch{return!0}}function Hf(e){var t=vl(e,2);t!==null&&dt(t,e,2)}function Yc(e){var t=Ie();if(typeof e=="function"){var a=e;if(e=a(),Ka){ua(!0);try{a()}finally{ua(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Zt,lastRenderedState:e},t}function Yf(e,t,a,l){return e.baseState=a,Uc(e,me,typeof l=="function"?l:Zt)}function v0(e,t,a,l,n){if(Uu(e))throw Error(c(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};O.T!==null?a(!0):u.isTransition=!1,l(u),a=t.pending,a===null?(u.next=t.pending=u,qf(t,u)):(u.next=a.next,t.pending=a.next=u)}}function qf(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var u=O.T,f={};O.T=f;try{var m=a(n,l),g=O.S;g!==null&&g(f,m),Bf(e,t,m)}catch(w){qc(e,t,w)}finally{O.T=u}}else try{u=a(n,l),Bf(e,t,u)}catch(w){qc(e,t,w)}}function Bf(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){Lf(e,t,l)},function(l){return qc(e,t,l)}):Lf(e,t,a)}function Lf(e,t,a){t.status="fulfilled",t.value=a,Xf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,qf(e,a)))}function qc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,Xf(t),t=t.next;while(t!==l)}e.action=null}function Xf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Gf(e,t){return t}function Qf(e,t){if(se){var a=xe.formState;if(a!==null){e:{var l=ae;if(se){if(Me){t:{for(var n=Me,u=Dt;n.nodeType!==8;){if(!u){n=null;break t}if(n=wt(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Me=wt(n.nextSibling),l=n.data==="F!";break e}}Qa(l)}l=!1}l&&(t=a[0])}}return a=Ie(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Gf,lastRenderedState:t},a.queue=l,a=ro.bind(null,ae,l),l.dispatch=a,l=Yc(!1),u=Qc.bind(null,ae,!1,l.queue),l=Ie(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=v0.bind(null,ae,n,u,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function Vf(e){var t=Re();return Zf(t,me,e)}function Zf(e,t,a){if(t=Uc(e,t,Gf)[0],e=_u(Zt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Sn(t)}catch(f){throw f===yn?Mu:f}else l=t;t=Re();var n=t.queue,u=n.dispatch;return a!==t.memoizedState&&(ae.flags|=2048,Ml(9,zu(),x0.bind(null,n,a),null)),[l,u,e]}function x0(e,t){e.action=t}function kf(e){var t=Re(),a=me;if(a!==null)return Zf(t,a,e);Re(),t=t.memoizedState,a=Re();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Ml(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ae.updateQueue,t===null&&(t=zc(),ae.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function zu(){return{destroy:void 0,resource:void 0}}function Kf(){return Re().memoizedState}function Cu(e,t,a,l){var n=Ie();l=l===void 0?null:l,ae.flags|=e,n.memoizedState=Ml(1|t,zu(),a,l)}function Nn(e,t,a,l){var n=Re();l=l===void 0?null:l;var u=n.memoizedState.inst;me!==null&&l!==null&&Ac(l,me.memoizedState.deps)?n.memoizedState=Ml(t,u,a,l):(ae.flags|=e,n.memoizedState=Ml(1|t,u,a,l))}function Jf(e,t){Cu(8390656,8,e,t)}function Wf(e,t){Nn(2048,8,e,t)}function $f(e,t){return Nn(4,2,e,t)}function Ff(e,t){return Nn(4,4,e,t)}function Pf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function If(e,t,a){a=a!=null?a.concat([e]):null,Nn(4,4,Pf.bind(null,t,e),a)}function Bc(){}function eo(e,t){var a=Re();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Ac(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function to(e,t){var a=Re();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Ac(t,l[1]))return l[0];if(l=e(),Ka){ua(!0);try{e()}finally{ua(!1)}}return a.memoizedState=[l,t],l}function Lc(e,t,a){return a===void 0||(da&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=ud(),ae.lanes|=e,pa|=e,a)}function ao(e,t,a,l){return it(a,t)?a:jl.current!==null?(e=Lc(e,a,l),it(e,t)||(qe=!0),e):(da&42)===0?(qe=!0,e.memoizedState=a):(e=ud(),ae.lanes|=e,pa|=e,t)}function lo(e,t,a,l,n){var u=L.p;L.p=u!==0&&8>u?u:8;var f=O.T,m={};O.T=m,Qc(e,!1,t,a);try{var g=n(),w=O.S;if(w!==null&&w(m,g),g!==null&&typeof g=="object"&&typeof g.then=="function"){var D=h0(g,l);jn(e,t,D,ot(e))}else jn(e,t,l,ot(e))}catch(z){jn(e,t,{then:function(){},status:"rejected",reason:z},ot())}finally{L.p=u,O.T=f}}function p0(){}function Xc(e,t,a,l){if(e.tag!==5)throw Error(c(476));var n=no(e).queue;lo(e,n,t,W,a===null?p0:function(){return uo(e),a(l)})}function no(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:W,baseState:W,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Zt,lastRenderedState:W},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Zt,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function uo(e){var t=no(e).next.queue;jn(e,t,{},ot())}function Gc(){return Ke(Xn)}function io(){return Re().memoizedState}function co(){return Re().memoizedState}function b0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=ot();e=fa(a);var l=oa(t,e,a);l!==null&&(dt(l,t,a),vn(l,t,a)),t={cache:xc()},e.payload=t;return}t=t.return}}function S0(e,t,a){var l=ot();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Uu(e)?so(t,a):(a=rc(e,t,a,l),a!==null&&(dt(a,e,l),fo(a,t,l)))}function ro(e,t,a){var l=ot();jn(e,t,a,l)}function jn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Uu(e))so(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var f=t.lastRenderedState,m=u(f,a);if(n.hasEagerState=!0,n.eagerState=m,it(m,f))return xu(e,t,n,0),xe===null&&vu(),!1}catch{}finally{}if(a=rc(e,t,n,l),a!==null)return dt(a,e,l),fo(a,t,l),!0}return!1}function Qc(e,t,a,l){if(l={lane:2,revertLane:Sr(),action:l,hasEagerState:!1,eagerState:null,next:null},Uu(e)){if(t)throw Error(c(479))}else t=rc(e,a,l,2),t!==null&&dt(t,e,2)}function Uu(e){var t=e.alternate;return e===ae||t!==null&&t===ae}function so(e,t){El=Du=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function fo(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,vs(e,a)}}var Hu={readContext:Ke,use:Ru,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useLayoutEffect:Ae,useInsertionEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useSyncExternalStore:Ae,useId:Ae,useHostTransitionStatus:Ae,useFormState:Ae,useActionState:Ae,useOptimistic:Ae,useMemoCache:Ae,useCacheRefresh:Ae},oo={readContext:Ke,use:Ru,useCallback:function(e,t){return Ie().memoizedState=[e,t===void 0?null:t],e},useContext:Ke,useEffect:Jf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Cu(4194308,4,Pf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Cu(4194308,4,e,t)},useInsertionEffect:function(e,t){Cu(4,2,e,t)},useMemo:function(e,t){var a=Ie();t=t===void 0?null:t;var l=e();if(Ka){ua(!0);try{e()}finally{ua(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=Ie();if(a!==void 0){var n=a(t);if(Ka){ua(!0);try{a(t)}finally{ua(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=S0.bind(null,ae,e),[l.memoizedState,e]},useRef:function(e){var t=Ie();return e={current:e},t.memoizedState=e},useState:function(e){e=Yc(e);var t=e.queue,a=ro.bind(null,ae,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Bc,useDeferredValue:function(e,t){var a=Ie();return Lc(a,e,t)},useTransition:function(){var e=Yc(!1);return e=lo.bind(null,ae,e.queue,!0,!1),Ie().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ae,n=Ie();if(se){if(a===void 0)throw Error(c(407));a=a()}else{if(a=t(),xe===null)throw Error(c(349));(ie&124)!==0||_f(l,t,a)}n.memoizedState=a;var u={value:a,getSnapshot:t};return n.queue=u,Jf(Cf.bind(null,l,u,e),[e]),l.flags|=2048,Ml(9,zu(),zf.bind(null,l,u,a,t),null),a},useId:function(){var e=Ie(),t=xe.identifierPrefix;if(se){var a=Gt,l=Xt;a=(l&~(1<<32-ut(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Ou++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=y0++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Gc,useFormState:Qf,useActionState:Qf,useOptimistic:function(e){var t=Ie();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Qc.bind(null,ae,!0,a),a.dispatch=t,[e,t]},useMemoCache:Cc,useCacheRefresh:function(){return Ie().memoizedState=b0.bind(null,ae)}},mo={readContext:Ke,use:Ru,useCallback:eo,useContext:Ke,useEffect:Wf,useImperativeHandle:If,useInsertionEffect:$f,useLayoutEffect:Ff,useMemo:to,useReducer:_u,useRef:Kf,useState:function(){return _u(Zt)},useDebugValue:Bc,useDeferredValue:function(e,t){var a=Re();return ao(a,me.memoizedState,e,t)},useTransition:function(){var e=_u(Zt)[0],t=Re().memoizedState;return[typeof e=="boolean"?e:Sn(e),t]},useSyncExternalStore:Rf,useId:io,useHostTransitionStatus:Gc,useFormState:Vf,useActionState:Vf,useOptimistic:function(e,t){var a=Re();return Yf(a,me,e,t)},useMemoCache:Cc,useCacheRefresh:co},N0={readContext:Ke,use:Ru,useCallback:eo,useContext:Ke,useEffect:Wf,useImperativeHandle:If,useInsertionEffect:$f,useLayoutEffect:Ff,useMemo:to,useReducer:Hc,useRef:Kf,useState:function(){return Hc(Zt)},useDebugValue:Bc,useDeferredValue:function(e,t){var a=Re();return me===null?Lc(a,e,t):ao(a,me.memoizedState,e,t)},useTransition:function(){var e=Hc(Zt)[0],t=Re().memoizedState;return[typeof e=="boolean"?e:Sn(e),t]},useSyncExternalStore:Rf,useId:io,useHostTransitionStatus:Gc,useFormState:kf,useActionState:kf,useOptimistic:function(e,t){var a=Re();return me!==null?Yf(a,me,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Cc,useCacheRefresh:co},Tl=null,En=0;function Yu(e){var t=En;return En+=1,Tl===null&&(Tl=[]),jf(Tl,e,t)}function wn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function qu(e,t){throw t.$$typeof===_?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function ho(e){var t=e._init;return t(e._payload)}function yo(e){function t(S,b){if(e){var j=S.deletions;j===null?(S.deletions=[b],S.flags|=16):j.push(b)}}function a(S,b){if(!e)return null;for(;b!==null;)t(S,b),b=b.sibling;return null}function l(S){for(var b=new Map;S!==null;)S.key!==null?b.set(S.key,S):b.set(S.index,S),S=S.sibling;return b}function n(S,b){return S=Lt(S,b),S.index=0,S.sibling=null,S}function u(S,b,j){return S.index=j,e?(j=S.alternate,j!==null?(j=j.index,j<b?(S.flags|=67108866,b):j):(S.flags|=67108866,b)):(S.flags|=1048576,b)}function f(S){return e&&S.alternate===null&&(S.flags|=67108866),S}function m(S,b,j,R){return b===null||b.tag!==6?(b=fc(j,S.mode,R),b.return=S,b):(b=n(b,j),b.return=S,b)}function g(S,b,j,R){var Q=j.type;return Q===q?D(S,b,j.props.children,R,j.key):b!==null&&(b.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===He&&ho(Q)===b.type)?(b=n(b,j.props),wn(b,j),b.return=S,b):(b=bu(j.type,j.key,j.props,null,S.mode,R),wn(b,j),b.return=S,b)}function w(S,b,j,R){return b===null||b.tag!==4||b.stateNode.containerInfo!==j.containerInfo||b.stateNode.implementation!==j.implementation?(b=oc(j,S.mode,R),b.return=S,b):(b=n(b,j.children||[]),b.return=S,b)}function D(S,b,j,R,Q){return b===null||b.tag!==7?(b=Ba(j,S.mode,R,Q),b.return=S,b):(b=n(b,j),b.return=S,b)}function z(S,b,j){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=fc(""+b,S.mode,j),b.return=S,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case H:return j=bu(b.type,b.key,b.props,null,S.mode,j),wn(j,b),j.return=S,j;case Z:return b=oc(b,S.mode,j),b.return=S,b;case He:var R=b._init;return b=R(b._payload),z(S,b,j)}if(Ze(b)||Ve(b))return b=Ba(b,S.mode,j,null),b.return=S,b;if(typeof b.then=="function")return z(S,Yu(b),j);if(b.$$typeof===ee)return z(S,Eu(S,b),j);qu(S,b)}return null}function M(S,b,j,R){var Q=b!==null?b.key:null;if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return Q!==null?null:m(S,b,""+j,R);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case H:return j.key===Q?g(S,b,j,R):null;case Z:return j.key===Q?w(S,b,j,R):null;case He:return Q=j._init,j=Q(j._payload),M(S,b,j,R)}if(Ze(j)||Ve(j))return Q!==null?null:D(S,b,j,R,null);if(typeof j.then=="function")return M(S,b,Yu(j),R);if(j.$$typeof===ee)return M(S,b,Eu(S,j),R);qu(S,j)}return null}function T(S,b,j,R,Q){if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return S=S.get(j)||null,m(b,S,""+R,Q);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case H:return S=S.get(R.key===null?j:R.key)||null,g(b,S,R,Q);case Z:return S=S.get(R.key===null?j:R.key)||null,w(b,S,R,Q);case He:var le=R._init;return R=le(R._payload),T(S,b,j,R,Q)}if(Ze(R)||Ve(R))return S=S.get(j)||null,D(b,S,R,Q,null);if(typeof R.then=="function")return T(S,b,j,Yu(R),Q);if(R.$$typeof===ee)return T(S,b,j,Eu(b,R),Q);qu(b,R)}return null}function $(S,b,j,R){for(var Q=null,le=null,V=b,J=b=0,Le=null;V!==null&&J<j.length;J++){V.index>J?(Le=V,V=null):Le=V.sibling;var re=M(S,V,j[J],R);if(re===null){V===null&&(V=Le);break}e&&V&&re.alternate===null&&t(S,V),b=u(re,b,J),le===null?Q=re:le.sibling=re,le=re,V=Le}if(J===j.length)return a(S,V),se&&Xa(S,J),Q;if(V===null){for(;J<j.length;J++)V=z(S,j[J],R),V!==null&&(b=u(V,b,J),le===null?Q=V:le.sibling=V,le=V);return se&&Xa(S,J),Q}for(V=l(V);J<j.length;J++)Le=T(V,S,J,j[J],R),Le!==null&&(e&&Le.alternate!==null&&V.delete(Le.key===null?J:Le.key),b=u(Le,b,J),le===null?Q=Le:le.sibling=Le,le=Le);return e&&V.forEach(function(Aa){return t(S,Aa)}),se&&Xa(S,J),Q}function K(S,b,j,R){if(j==null)throw Error(c(151));for(var Q=null,le=null,V=b,J=b=0,Le=null,re=j.next();V!==null&&!re.done;J++,re=j.next()){V.index>J?(Le=V,V=null):Le=V.sibling;var Aa=M(S,V,re.value,R);if(Aa===null){V===null&&(V=Le);break}e&&V&&Aa.alternate===null&&t(S,V),b=u(Aa,b,J),le===null?Q=Aa:le.sibling=Aa,le=Aa,V=Le}if(re.done)return a(S,V),se&&Xa(S,J),Q;if(V===null){for(;!re.done;J++,re=j.next())re=z(S,re.value,R),re!==null&&(b=u(re,b,J),le===null?Q=re:le.sibling=re,le=re);return se&&Xa(S,J),Q}for(V=l(V);!re.done;J++,re=j.next())re=T(V,S,J,re.value,R),re!==null&&(e&&re.alternate!==null&&V.delete(re.key===null?J:re.key),b=u(re,b,J),le===null?Q=re:le.sibling=re,le=re);return e&&V.forEach(function(jy){return t(S,jy)}),se&&Xa(S,J),Q}function ye(S,b,j,R){if(typeof j=="object"&&j!==null&&j.type===q&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case H:e:{for(var Q=j.key;b!==null;){if(b.key===Q){if(Q=j.type,Q===q){if(b.tag===7){a(S,b.sibling),R=n(b,j.props.children),R.return=S,S=R;break e}}else if(b.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===He&&ho(Q)===b.type){a(S,b.sibling),R=n(b,j.props),wn(R,j),R.return=S,S=R;break e}a(S,b);break}else t(S,b);b=b.sibling}j.type===q?(R=Ba(j.props.children,S.mode,R,j.key),R.return=S,S=R):(R=bu(j.type,j.key,j.props,null,S.mode,R),wn(R,j),R.return=S,S=R)}return f(S);case Z:e:{for(Q=j.key;b!==null;){if(b.key===Q)if(b.tag===4&&b.stateNode.containerInfo===j.containerInfo&&b.stateNode.implementation===j.implementation){a(S,b.sibling),R=n(b,j.children||[]),R.return=S,S=R;break e}else{a(S,b);break}else t(S,b);b=b.sibling}R=oc(j,S.mode,R),R.return=S,S=R}return f(S);case He:return Q=j._init,j=Q(j._payload),ye(S,b,j,R)}if(Ze(j))return $(S,b,j,R);if(Ve(j)){if(Q=Ve(j),typeof Q!="function")throw Error(c(150));return j=Q.call(j),K(S,b,j,R)}if(typeof j.then=="function")return ye(S,b,Yu(j),R);if(j.$$typeof===ee)return ye(S,b,Eu(S,j),R);qu(S,j)}return typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint"?(j=""+j,b!==null&&b.tag===6?(a(S,b.sibling),R=n(b,j),R.return=S,S=R):(a(S,b),R=fc(j,S.mode,R),R.return=S,S=R),f(S)):a(S,b)}return function(S,b,j,R){try{En=0;var Q=ye(S,b,j,R);return Tl=null,Q}catch(V){if(V===yn||V===Mu)throw V;var le=ct(29,V,null,S.mode);return le.lanes=R,le.return=S,le}finally{}}}var Al=yo(!0),go=yo(!1),bt=C(null),Ot=null;function ma(e){var t=e.alternate;B(Ce,Ce.current&1),B(bt,e),Ot===null&&(t===null||jl.current!==null||t.memoizedState!==null)&&(Ot=e)}function vo(e){if(e.tag===22){if(B(Ce,Ce.current),B(bt,e),Ot===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ot=e)}}else ha()}function ha(){B(Ce,Ce.current),B(bt,bt.current)}function kt(e){X(bt),Ot===e&&(Ot=null),X(Ce)}var Ce=C(0);function Bu(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||zr(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Vc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:E({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Zc={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=ot(),n=fa(l);n.payload=t,a!=null&&(n.callback=a),t=oa(e,n,l),t!==null&&(dt(t,e,l),vn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=ot(),n=fa(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=oa(e,n,l),t!==null&&(dt(t,e,l),vn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=ot(),l=fa(a);l.tag=2,t!=null&&(l.callback=t),t=oa(e,l,a),t!==null&&(dt(t,e,a),vn(t,e,a))}};function xo(e,t,a,l,n,u,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,u,f):t.prototype&&t.prototype.isPureReactComponent?!cn(a,l)||!cn(n,u):!0}function po(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&Zc.enqueueReplaceState(t,t.state,null)}function Ja(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=E({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Lu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function bo(e){Lu(e)}function So(e){console.error(e)}function No(e){Lu(e)}function Xu(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function jo(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function kc(e,t,a){return a=fa(a),a.tag=3,a.payload={element:null},a.callback=function(){Xu(e,t)},a}function Eo(e){return e=fa(e),e.tag=3,e}function wo(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var u=l.value;e.payload=function(){return n(u)},e.callback=function(){jo(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){jo(t,a,l),typeof n!="function"&&(ba===null?ba=new Set([this]):ba.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function j0(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&dn(t,a,n,!0),a=bt.current,a!==null){switch(a.tag){case 13:return Ot===null?gr():a.alternate===null&&Te===0&&(Te=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===Sc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),xr(e,l,n)),!1;case 22:return a.flags|=65536,l===Sc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),xr(e,l,n)),!1}throw Error(c(435,a.tag))}return xr(e,l,n),gr(),!1}if(se)return t=bt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==hc&&(e=Error(c(422),{cause:l}),on(gt(e,a)))):(l!==hc&&(t=Error(c(423),{cause:l}),on(gt(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=gt(l,a),n=kc(e.stateNode,l,n),Ec(e,n),Te!==4&&(Te=2)),!1;var u=Error(c(520),{cause:l});if(u=gt(u,a),_n===null?_n=[u]:_n.push(u),Te!==4&&(Te=2),t===null)return!0;l=gt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=kc(a.stateNode,l,e),Ec(a,e),!1;case 1:if(t=a.type,u=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(ba===null||!ba.has(u))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Eo(n),wo(n,e,a,l),Ec(a,n),!1}a=a.return}while(a!==null);return!1}var Mo=Error(c(461)),qe=!1;function Xe(e,t,a,l){t.child=e===null?go(t,null,a,l):Al(t,e.child,a,l)}function To(e,t,a,l,n){a=a.render;var u=t.ref;if("ref"in l){var f={};for(var m in l)m!=="ref"&&(f[m]=l[m])}else f=l;return Za(t),l=Dc(e,t,a,f,u,n),m=Oc(),e!==null&&!qe?(Rc(e,t,n),Kt(e,t,n)):(se&&m&&dc(t),t.flags|=1,Xe(e,t,l,n),t.child)}function Ao(e,t,a,l,n){if(e===null){var u=a.type;return typeof u=="function"&&!sc(u)&&u.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=u,Do(e,t,u,l,n)):(e=bu(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!er(e,n)){var f=u.memoizedProps;if(a=a.compare,a=a!==null?a:cn,a(f,l)&&e.ref===t.ref)return Kt(e,t,n)}return t.flags|=1,e=Lt(u,l),e.ref=t.ref,e.return=t,t.child=e}function Do(e,t,a,l,n){if(e!==null){var u=e.memoizedProps;if(cn(u,l)&&e.ref===t.ref)if(qe=!1,t.pendingProps=l=u,er(e,n))(e.flags&131072)!==0&&(qe=!0);else return t.lanes=e.lanes,Kt(e,t,n)}return Kc(e,t,a,l,n)}function Oo(e,t,a){var l=t.pendingProps,n=l.children,u=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=u!==null?u.baseLanes|a:a,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~l}else t.childLanes=0,t.child=null;return Ro(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&wu(t,u!==null?u.cachePool:null),u!==null?Af(t,u):Mc(),vo(t);else return t.lanes=t.childLanes=536870912,Ro(e,t,u!==null?u.baseLanes|a:a,a)}else u!==null?(wu(t,u.cachePool),Af(t,u),ha(),t.memoizedState=null):(e!==null&&wu(t,null),Mc(),ha());return Xe(e,t,n,a),t.child}function Ro(e,t,a,l){var n=bc();return n=n===null?null:{parent:ze._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&wu(t,null),Mc(),vo(t),e!==null&&dn(e,t,l,!0),null}function Gu(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(c(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Kc(e,t,a,l,n){return Za(t),a=Dc(e,t,a,l,void 0,n),l=Oc(),e!==null&&!qe?(Rc(e,t,n),Kt(e,t,n)):(se&&l&&dc(t),t.flags|=1,Xe(e,t,a,n),t.child)}function _o(e,t,a,l,n,u){return Za(t),t.updateQueue=null,a=Of(t,l,a,n),Df(e),l=Oc(),e!==null&&!qe?(Rc(e,t,u),Kt(e,t,u)):(se&&l&&dc(t),t.flags|=1,Xe(e,t,a,u),t.child)}function zo(e,t,a,l,n){if(Za(t),t.stateNode===null){var u=xl,f=a.contextType;typeof f=="object"&&f!==null&&(u=Ke(f)),u=new a(l,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Zc,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=l,u.state=t.memoizedState,u.refs={},Nc(t),f=a.contextType,u.context=typeof f=="object"&&f!==null?Ke(f):xl,u.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(Vc(t,a,f,l),u.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&Zc.enqueueReplaceState(u,u.state,null),pn(t,l,u,n),xn(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){u=t.stateNode;var m=t.memoizedProps,g=Ja(a,m);u.props=g;var w=u.context,D=a.contextType;f=xl,typeof D=="object"&&D!==null&&(f=Ke(D));var z=a.getDerivedStateFromProps;D=typeof z=="function"||typeof u.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,D||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(m||w!==f)&&po(t,u,l,f),sa=!1;var M=t.memoizedState;u.state=M,pn(t,l,u,n),xn(),w=t.memoizedState,m||M!==w||sa?(typeof z=="function"&&(Vc(t,a,z,l),w=t.memoizedState),(g=sa||xo(t,a,g,l,M,w,f))?(D||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=w),u.props=l,u.state=w,u.context=f,l=g):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{u=t.stateNode,jc(e,t),f=t.memoizedProps,D=Ja(a,f),u.props=D,z=t.pendingProps,M=u.context,w=a.contextType,g=xl,typeof w=="object"&&w!==null&&(g=Ke(w)),m=a.getDerivedStateFromProps,(w=typeof m=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==z||M!==g)&&po(t,u,l,g),sa=!1,M=t.memoizedState,u.state=M,pn(t,l,u,n),xn();var T=t.memoizedState;f!==z||M!==T||sa||e!==null&&e.dependencies!==null&&ju(e.dependencies)?(typeof m=="function"&&(Vc(t,a,m,l),T=t.memoizedState),(D=sa||xo(t,a,D,l,M,T,g)||e!==null&&e.dependencies!==null&&ju(e.dependencies))?(w||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,T,g),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,T,g)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&M===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&M===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=T),u.props=l,u.state=T,u.context=g,l=D):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&M===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&M===e.memoizedState||(t.flags|=1024),l=!1)}return u=l,Gu(e,t),l=(t.flags&128)!==0,u||l?(u=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&l?(t.child=Al(t,e.child,null,n),t.child=Al(t,null,a,n)):Xe(e,t,a,n),t.memoizedState=u.state,e=t.child):e=Kt(e,t,n),e}function Co(e,t,a,l){return fn(),t.flags|=256,Xe(e,t,a,l),t.child}var Jc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Wc(e){return{baseLanes:e,cachePool:bf()}}function $c(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=St),e}function Uo(e,t,a){var l=t.pendingProps,n=!1,u=(t.flags&128)!==0,f;if((f=u)||(f=e!==null&&e.memoizedState===null?!1:(Ce.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(se){if(n?ma(t):ha(),se){var m=Me,g;if(g=m){e:{for(g=m,m=Dt;g.nodeType!==8;){if(!m){m=null;break e}if(g=wt(g.nextSibling),g===null){m=null;break e}}m=g}m!==null?(t.memoizedState={dehydrated:m,treeContext:La!==null?{id:Xt,overflow:Gt}:null,retryLane:536870912,hydrationErrors:null},g=ct(18,null,null,0),g.stateNode=m,g.return=t,t.child=g,We=t,Me=null,g=!0):g=!1}g||Qa(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return zr(m)?t.lanes=32:t.lanes=536870912,null;kt(t)}return m=l.children,l=l.fallback,n?(ha(),n=t.mode,m=Qu({mode:"hidden",children:m},n),l=Ba(l,n,a,null),m.return=t,l.return=t,m.sibling=l,t.child=m,n=t.child,n.memoizedState=Wc(a),n.childLanes=$c(e,f,a),t.memoizedState=Jc,l):(ma(t),Fc(t,m))}if(g=e.memoizedState,g!==null&&(m=g.dehydrated,m!==null)){if(u)t.flags&256?(ma(t),t.flags&=-257,t=Pc(e,t,a)):t.memoizedState!==null?(ha(),t.child=e.child,t.flags|=128,t=null):(ha(),n=l.fallback,m=t.mode,l=Qu({mode:"visible",children:l.children},m),n=Ba(n,m,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,Al(t,e.child,null,a),l=t.child,l.memoizedState=Wc(a),l.childLanes=$c(e,f,a),t.memoizedState=Jc,t=n);else if(ma(t),zr(m)){if(f=m.nextSibling&&m.nextSibling.dataset,f)var w=f.dgst;f=w,l=Error(c(419)),l.stack="",l.digest=f,on({value:l,source:null,stack:null}),t=Pc(e,t,a)}else if(qe||dn(e,t,a,!1),f=(a&e.childLanes)!==0,qe||f){if(f=xe,f!==null&&(l=a&-a,l=(l&42)!==0?1:Ci(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==g.retryLane))throw g.retryLane=l,vl(e,l),dt(f,e,l),Mo;m.data==="$?"||gr(),t=Pc(e,t,a)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=g.treeContext,Me=wt(m.nextSibling),We=t,se=!0,Ga=null,Dt=!1,e!==null&&(xt[pt++]=Xt,xt[pt++]=Gt,xt[pt++]=La,Xt=e.id,Gt=e.overflow,La=t),t=Fc(t,l.children),t.flags|=4096);return t}return n?(ha(),n=l.fallback,m=t.mode,g=e.child,w=g.sibling,l=Lt(g,{mode:"hidden",children:l.children}),l.subtreeFlags=g.subtreeFlags&65011712,w!==null?n=Lt(w,n):(n=Ba(n,m,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,m=e.child.memoizedState,m===null?m=Wc(a):(g=m.cachePool,g!==null?(w=ze._currentValue,g=g.parent!==w?{parent:w,pool:w}:g):g=bf(),m={baseLanes:m.baseLanes|a,cachePool:g}),n.memoizedState=m,n.childLanes=$c(e,f,a),t.memoizedState=Jc,l):(ma(t),a=e.child,e=a.sibling,a=Lt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function Fc(e,t){return t=Qu({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Qu(e,t){return e=ct(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Pc(e,t,a){return Al(t,e.child,null,a),e=Fc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ho(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),gc(e.return,t,a)}function Ic(e,t,a,l,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=a,u.tailMode=n)}function Yo(e,t,a){var l=t.pendingProps,n=l.revealOrder,u=l.tail;if(Xe(e,t,l.children,a),l=Ce.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ho(e,a,t);else if(e.tag===19)Ho(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(B(Ce,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Bu(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),Ic(t,!1,n,a,u);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Bu(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}Ic(t,!0,a,null,u);break;case"together":Ic(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Kt(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),pa|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(dn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,a=Lt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Lt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function er(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ju(e)))}function E0(e,t,a){switch(t.tag){case 3:be(t,t.stateNode.containerInfo),ra(t,ze,e.memoizedState.cache),fn();break;case 27:case 5:Di(t);break;case 4:be(t,t.stateNode.containerInfo);break;case 10:ra(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(ma(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Uo(e,t,a):(ma(t),e=Kt(e,t,a),e!==null?e.sibling:null);ma(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(dn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return Yo(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),B(Ce,Ce.current),l)break;return null;case 22:case 23:return t.lanes=0,Oo(e,t,a);case 24:ra(t,ze,e.memoizedState.cache)}return Kt(e,t,a)}function qo(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)qe=!0;else{if(!er(e,a)&&(t.flags&128)===0)return qe=!1,E0(e,t,a);qe=(e.flags&131072)!==0}else qe=!1,se&&(t.flags&1048576)!==0&&mf(t,Nu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")sc(l)?(e=Ja(l,e),t.tag=1,t=zo(null,t,l,e,a)):(t.tag=0,t=Kc(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===je){t.tag=11,t=To(null,t,l,e,a);break e}else if(n===_e){t.tag=14,t=Ao(null,t,l,e,a);break e}}throw t=za(l)||l,Error(c(306,t,""))}}return t;case 0:return Kc(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=Ja(l,t.pendingProps),zo(e,t,l,n,a);case 3:e:{if(be(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var u=t.memoizedState;n=u.element,jc(e,t),pn(t,l,null,a);var f=t.memoizedState;if(l=f.cache,ra(t,ze,l),l!==u.cache&&vc(t,[ze],a,!0),xn(),l=f.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=Co(e,t,l,a);break e}else if(l!==n){n=gt(Error(c(424)),t),on(n),t=Co(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Me=wt(e.firstChild),We=t,se=!0,Ga=null,Dt=!0,a=go(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(fn(),l===n){t=Kt(e,t,a);break e}Xe(e,t,l,a)}t=t.child}return t;case 26:return Gu(e,t),e===null?(a=Gd(t.type,null,t.pendingProps,null))?t.memoizedState=a:se||(a=t.type,e=t.pendingProps,l=li(P.current).createElement(a),l[ke]=t,l[Fe]=e,Qe(l,a,e),Ye(l),t.stateNode=l):t.memoizedState=Gd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Di(t),e===null&&se&&(l=t.stateNode=Bd(t.type,t.pendingProps,P.current),We=t,Dt=!0,n=Me,ja(t.type)?(Cr=n,Me=wt(l.firstChild)):Me=n),Xe(e,t,t.pendingProps.children,a),Gu(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&se&&((n=l=Me)&&(l=P0(l,t.type,t.pendingProps,Dt),l!==null?(t.stateNode=l,We=t,Me=wt(l.firstChild),Dt=!1,n=!0):n=!1),n||Qa(t)),Di(t),n=t.type,u=t.pendingProps,f=e!==null?e.memoizedProps:null,l=u.children,Or(n,u)?l=null:f!==null&&Or(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=Dc(e,t,g0,null,null,a),Xn._currentValue=n),Gu(e,t),Xe(e,t,l,a),t.child;case 6:return e===null&&se&&((e=a=Me)&&(a=I0(a,t.pendingProps,Dt),a!==null?(t.stateNode=a,We=t,Me=null,e=!0):e=!1),e||Qa(t)),null;case 13:return Uo(e,t,a);case 4:return be(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Al(t,null,l,a):Xe(e,t,l,a),t.child;case 11:return To(e,t,t.type,t.pendingProps,a);case 7:return Xe(e,t,t.pendingProps,a),t.child;case 8:return Xe(e,t,t.pendingProps.children,a),t.child;case 12:return Xe(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,ra(t,t.type,l.value),Xe(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,Za(t),n=Ke(n),l=l(n),t.flags|=1,Xe(e,t,l,a),t.child;case 14:return Ao(e,t,t.type,t.pendingProps,a);case 15:return Do(e,t,t.type,t.pendingProps,a);case 19:return Yo(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Qu(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Lt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Oo(e,t,a);case 24:return Za(t),l=Ke(ze),e===null?(n=bc(),n===null&&(n=xe,u=xc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=a),n=u),t.memoizedState={parent:l,cache:n},Nc(t),ra(t,ze,n)):((e.lanes&a)!==0&&(jc(e,t),pn(t,null,null,a),xn()),n=e.memoizedState,u=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ra(t,ze,l)):(l=u.cache,ra(t,ze,l),l!==n.cache&&vc(t,[ze],a,!0))),Xe(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function Jt(e){e.flags|=4}function Bo(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Kd(t)){if(t=bt.current,t!==null&&((ie&4194048)===ie?Ot!==null:(ie&62914560)!==ie&&(ie&536870912)===0||t!==Ot))throw gn=Sc,Sf;e.flags|=8192}}function Vu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ys():536870912,e.lanes|=t,_l|=t)}function Mn(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ee(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function w0(e,t,a){var l=t.pendingProps;switch(mc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ee(t),null;case 1:return Ee(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Vt(ze),na(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(sn(t)?Jt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,gf())),Ee(t),null;case 26:return a=t.memoizedState,e===null?(Jt(t),a!==null?(Ee(t),Bo(t,a)):(Ee(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Jt(t),Ee(t),Bo(t,a)):(Ee(t),t.flags&=-16777217):(e.memoizedProps!==l&&Jt(t),Ee(t),t.flags&=-16777217),null;case 27:tu(t),a=P.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Jt(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ee(t),null}e=k.current,sn(t)?hf(t):(e=Bd(n,l,a),t.stateNode=e,Jt(t))}return Ee(t),null;case 5:if(tu(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Jt(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ee(t),null}if(e=k.current,sn(t))hf(t);else{switch(n=li(P.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[ke]=t,e[Fe]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Qe(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Jt(t)}}return Ee(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Jt(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=P.current,sn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=We,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[ke]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||_d(e.nodeValue,a)),e||Qa(t)}else e=li(e).createTextNode(l),e[ke]=t,t.stateNode=e}return Ee(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=sn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(c(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(c(317));n[ke]=t}else fn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ee(t),n=!1}else n=gf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(kt(t),t):(kt(t),null)}if(kt(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Vu(t,t.updateQueue),Ee(t),null;case 4:return na(),e===null&&wr(t.stateNode.containerInfo),Ee(t),null;case 10:return Vt(t.type),Ee(t),null;case 19:if(X(Ce),n=t.memoizedState,n===null)return Ee(t),null;if(l=(t.flags&128)!==0,u=n.rendering,u===null)if(l)Mn(n,!1);else{if(Te!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Bu(e),u!==null){for(t.flags|=128,Mn(n,!1),e=u.updateQueue,t.updateQueue=e,Vu(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)df(a,e),a=a.sibling;return B(Ce,Ce.current&1|2),t.child}e=e.sibling}n.tail!==null&&At()>Ku&&(t.flags|=128,l=!0,Mn(n,!1),t.lanes=4194304)}else{if(!l)if(e=Bu(u),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Vu(t,e),Mn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!se)return Ee(t),null}else 2*At()-n.renderingStartTime>Ku&&a!==536870912&&(t.flags|=128,l=!0,Mn(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=At(),t.sibling=null,e=Ce.current,B(Ce,l?e&1|2:e&1),t):(Ee(t),null);case 22:case 23:return kt(t),Tc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Ee(t),t.subtreeFlags&6&&(t.flags|=8192)):Ee(t),a=t.updateQueue,a!==null&&Vu(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&X(ka),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Vt(ze),Ee(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function M0(e,t){switch(mc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Vt(ze),na(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return tu(t),null;case 13:if(kt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));fn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(Ce),null;case 4:return na(),null;case 10:return Vt(t.type),null;case 22:case 23:return kt(t),Tc(),e!==null&&X(ka),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Vt(ze),null;case 25:return null;default:return null}}function Lo(e,t){switch(mc(t),t.tag){case 3:Vt(ze),na();break;case 26:case 27:case 5:tu(t);break;case 4:na();break;case 13:kt(t);break;case 19:X(Ce);break;case 10:Vt(t.type);break;case 22:case 23:kt(t),Tc(),e!==null&&X(ka);break;case 24:Vt(ze)}}function Tn(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var u=a.create,f=a.inst;l=u(),f.destroy=l}a=a.next}while(a!==n)}}catch(m){ve(t,t.return,m)}}function ya(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&e)===e){var f=l.inst,m=f.destroy;if(m!==void 0){f.destroy=void 0,n=t;var g=a,w=m;try{w()}catch(D){ve(n,g,D)}}}l=l.next}while(l!==u)}}catch(D){ve(t,t.return,D)}}function Xo(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Tf(t,a)}catch(l){ve(e,e.return,l)}}}function Go(e,t,a){a.props=Ja(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){ve(e,t,l)}}function An(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){ve(e,t,n)}}function Rt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){ve(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){ve(e,t,n)}else a.current=null}function Qo(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){ve(e,e.return,n)}}function tr(e,t,a){try{var l=e.stateNode;K0(l,e.type,a,t),l[Fe]=t}catch(n){ve(e,e.return,n)}}function Vo(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ja(e.type)||e.tag===4}function ar(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Vo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ja(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function lr(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=ai));else if(l!==4&&(l===27&&ja(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(lr(e,t,a),e=e.sibling;e!==null;)lr(e,t,a),e=e.sibling}function Zu(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&ja(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Zu(e,t,a),e=e.sibling;e!==null;)Zu(e,t,a),e=e.sibling}function Zo(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Qe(t,l,a),t[ke]=e,t[Fe]=a}catch(u){ve(e,e.return,u)}}var Wt=!1,De=!1,nr=!1,ko=typeof WeakSet=="function"?WeakSet:Set,Be=null;function T0(e,t){if(e=e.containerInfo,Ar=si,e=tf(e),ac(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{a.nodeType,u.nodeType}catch{a=null;break e}var f=0,m=-1,g=-1,w=0,D=0,z=e,M=null;t:for(;;){for(var T;z!==a||n!==0&&z.nodeType!==3||(m=f+n),z!==u||l!==0&&z.nodeType!==3||(g=f+l),z.nodeType===3&&(f+=z.nodeValue.length),(T=z.firstChild)!==null;)M=z,z=T;for(;;){if(z===e)break t;if(M===a&&++w===n&&(m=f),M===u&&++D===l&&(g=f),(T=z.nextSibling)!==null)break;z=M,M=z.parentNode}z=T}a=m===-1||g===-1?null:{start:m,end:g}}else a=null}a=a||{start:0,end:0}}else a=null;for(Dr={focusedElem:e,selectionRange:a},si=!1,Be=t;Be!==null;)if(t=Be,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Be=e;else for(;Be!==null;){switch(t=Be,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,a=t,n=u.memoizedProps,u=u.memoizedState,l=a.stateNode;try{var $=Ja(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate($,u),l.__reactInternalSnapshotBeforeUpdate=e}catch(K){ve(a,a.return,K)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)_r(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":_r(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Be=e;break}Be=t.return}}function Ko(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:ga(e,a),l&4&&Tn(5,a);break;case 1:if(ga(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){ve(a,a.return,f)}else{var n=Ja(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){ve(a,a.return,f)}}l&64&&Xo(a),l&512&&An(a,a.return);break;case 3:if(ga(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Tf(e,t)}catch(f){ve(a,a.return,f)}}break;case 27:t===null&&l&4&&Zo(a);case 26:case 5:ga(e,a),t===null&&l&4&&Qo(a),l&512&&An(a,a.return);break;case 12:ga(e,a);break;case 13:ga(e,a),l&4&&$o(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=H0.bind(null,a),ey(e,a))));break;case 22:if(l=a.memoizedState!==null||Wt,!l){t=t!==null&&t.memoizedState!==null||De,n=Wt;var u=De;Wt=l,(De=t)&&!u?va(e,a,(a.subtreeFlags&8772)!==0):ga(e,a),Wt=n,De=u}break;case 30:break;default:ga(e,a)}}function Jo(e){var t=e.alternate;t!==null&&(e.alternate=null,Jo(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Yi(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Se=null,et=!1;function $t(e,t,a){for(a=a.child;a!==null;)Wo(e,t,a),a=a.sibling}function Wo(e,t,a){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(Jl,a)}catch{}switch(a.tag){case 26:De||Rt(a,t),$t(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:De||Rt(a,t);var l=Se,n=et;ja(a.type)&&(Se=a.stateNode,et=!1),$t(e,t,a),Yn(a.stateNode),Se=l,et=n;break;case 5:De||Rt(a,t);case 6:if(l=Se,n=et,Se=null,$t(e,t,a),Se=l,et=n,Se!==null)if(et)try{(Se.nodeType===9?Se.body:Se.nodeName==="HTML"?Se.ownerDocument.body:Se).removeChild(a.stateNode)}catch(u){ve(a,t,u)}else try{Se.removeChild(a.stateNode)}catch(u){ve(a,t,u)}break;case 18:Se!==null&&(et?(e=Se,Yd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Zn(e)):Yd(Se,a.stateNode));break;case 4:l=Se,n=et,Se=a.stateNode.containerInfo,et=!0,$t(e,t,a),Se=l,et=n;break;case 0:case 11:case 14:case 15:De||ya(2,a,t),De||ya(4,a,t),$t(e,t,a);break;case 1:De||(Rt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&Go(a,t,l)),$t(e,t,a);break;case 21:$t(e,t,a);break;case 22:De=(l=De)||a.memoizedState!==null,$t(e,t,a),De=l;break;default:$t(e,t,a)}}function $o(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Zn(e)}catch(a){ve(t,t.return,a)}}function A0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new ko),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new ko),t;default:throw Error(c(435,e.tag))}}function ur(e,t){var a=A0(e);t.forEach(function(l){var n=Y0.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function rt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],u=e,f=t,m=f;e:for(;m!==null;){switch(m.tag){case 27:if(ja(m.type)){Se=m.stateNode,et=!1;break e}break;case 5:Se=m.stateNode,et=!1;break e;case 3:case 4:Se=m.stateNode.containerInfo,et=!0;break e}m=m.return}if(Se===null)throw Error(c(160));Wo(u,f,n),Se=null,et=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Fo(t,e),t=t.sibling}var Et=null;function Fo(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:rt(t,e),st(e),l&4&&(ya(3,e,e.return),Tn(3,e),ya(5,e,e.return));break;case 1:rt(t,e),st(e),l&512&&(De||a===null||Rt(a,a.return)),l&64&&Wt&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Et;if(rt(t,e),st(e),l&512&&(De||a===null||Rt(a,a.return)),l&4){var u=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Fl]||u[ke]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(l),n.head.insertBefore(u,n.querySelector("head > title"))),Qe(u,l,a),u[ke]=e,Ye(u),l=u;break e;case"link":var f=Zd("link","href",n).get(l+(a.href||""));if(f){for(var m=0;m<f.length;m++)if(u=f[m],u.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&u.getAttribute("rel")===(a.rel==null?null:a.rel)&&u.getAttribute("title")===(a.title==null?null:a.title)&&u.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(m,1);break t}}u=n.createElement(l),Qe(u,l,a),n.head.appendChild(u);break;case"meta":if(f=Zd("meta","content",n).get(l+(a.content||""))){for(m=0;m<f.length;m++)if(u=f[m],u.getAttribute("content")===(a.content==null?null:""+a.content)&&u.getAttribute("name")===(a.name==null?null:a.name)&&u.getAttribute("property")===(a.property==null?null:a.property)&&u.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&u.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(m,1);break t}}u=n.createElement(l),Qe(u,l,a),n.head.appendChild(u);break;default:throw Error(c(468,l))}u[ke]=e,Ye(u),l=u}e.stateNode=l}else kd(n,e.type,e.stateNode);else e.stateNode=Vd(n,l,e.memoizedProps);else u!==l?(u===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):u.count--,l===null?kd(n,e.type,e.stateNode):Vd(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&tr(e,e.memoizedProps,a.memoizedProps)}break;case 27:rt(t,e),st(e),l&512&&(De||a===null||Rt(a,a.return)),a!==null&&l&4&&tr(e,e.memoizedProps,a.memoizedProps);break;case 5:if(rt(t,e),st(e),l&512&&(De||a===null||Rt(a,a.return)),e.flags&32){n=e.stateNode;try{fl(n,"")}catch(T){ve(e,e.return,T)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,tr(e,n,a!==null?a.memoizedProps:n)),l&1024&&(nr=!0);break;case 6:if(rt(t,e),st(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(T){ve(e,e.return,T)}}break;case 3:if(ii=null,n=Et,Et=ni(t.containerInfo),rt(t,e),Et=n,st(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Zn(t.containerInfo)}catch(T){ve(e,e.return,T)}nr&&(nr=!1,Po(e));break;case 4:l=Et,Et=ni(e.stateNode.containerInfo),rt(t,e),st(e),Et=l;break;case 12:rt(t,e),st(e);break;case 13:rt(t,e),st(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(or=At()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,ur(e,l)));break;case 22:n=e.memoizedState!==null;var g=a!==null&&a.memoizedState!==null,w=Wt,D=De;if(Wt=w||n,De=D||g,rt(t,e),De=D,Wt=w,st(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||g||Wt||De||Wa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){g=a=t;try{if(u=g.stateNode,n)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{m=g.stateNode;var z=g.memoizedProps.style,M=z!=null&&z.hasOwnProperty("display")?z.display:null;m.style.display=M==null||typeof M=="boolean"?"":(""+M).trim()}}catch(T){ve(g,g.return,T)}}}else if(t.tag===6){if(a===null){g=t;try{g.stateNode.nodeValue=n?"":g.memoizedProps}catch(T){ve(g,g.return,T)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,ur(e,a))));break;case 19:rt(t,e),st(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,ur(e,l)));break;case 30:break;case 21:break;default:rt(t,e),st(e)}}function st(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(Vo(l)){a=l;break}l=l.return}if(a==null)throw Error(c(160));switch(a.tag){case 27:var n=a.stateNode,u=ar(e);Zu(e,u,n);break;case 5:var f=a.stateNode;a.flags&32&&(fl(f,""),a.flags&=-33);var m=ar(e);Zu(e,m,f);break;case 3:case 4:var g=a.stateNode.containerInfo,w=ar(e);lr(e,w,g);break;default:throw Error(c(161))}}catch(D){ve(e,e.return,D)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Po(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Po(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ga(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Ko(e,t.alternate,t),t=t.sibling}function Wa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ya(4,t,t.return),Wa(t);break;case 1:Rt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Go(t,t.return,a),Wa(t);break;case 27:Yn(t.stateNode);case 26:case 5:Rt(t,t.return),Wa(t);break;case 22:t.memoizedState===null&&Wa(t);break;case 30:Wa(t);break;default:Wa(t)}e=e.sibling}}function va(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,u=t,f=u.flags;switch(u.tag){case 0:case 11:case 15:va(n,u,a),Tn(4,u);break;case 1:if(va(n,u,a),l=u,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(w){ve(l,l.return,w)}if(l=u,n=l.updateQueue,n!==null){var m=l.stateNode;try{var g=n.shared.hiddenCallbacks;if(g!==null)for(n.shared.hiddenCallbacks=null,n=0;n<g.length;n++)Mf(g[n],m)}catch(w){ve(l,l.return,w)}}a&&f&64&&Xo(u),An(u,u.return);break;case 27:Zo(u);case 26:case 5:va(n,u,a),a&&l===null&&f&4&&Qo(u),An(u,u.return);break;case 12:va(n,u,a);break;case 13:va(n,u,a),a&&f&4&&$o(n,u);break;case 22:u.memoizedState===null&&va(n,u,a),An(u,u.return);break;case 30:break;default:va(n,u,a)}t=t.sibling}}function ir(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&mn(a))}function cr(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&mn(e))}function _t(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Io(e,t,a,l),t=t.sibling}function Io(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:_t(e,t,a,l),n&2048&&Tn(9,t);break;case 1:_t(e,t,a,l);break;case 3:_t(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&mn(e)));break;case 12:if(n&2048){_t(e,t,a,l),e=t.stateNode;try{var u=t.memoizedProps,f=u.id,m=u.onPostCommit;typeof m=="function"&&m(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(g){ve(t,t.return,g)}}else _t(e,t,a,l);break;case 13:_t(e,t,a,l);break;case 23:break;case 22:u=t.stateNode,f=t.alternate,t.memoizedState!==null?u._visibility&2?_t(e,t,a,l):Dn(e,t):u._visibility&2?_t(e,t,a,l):(u._visibility|=2,Dl(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&ir(f,t);break;case 24:_t(e,t,a,l),n&2048&&cr(t.alternate,t);break;default:_t(e,t,a,l)}}function Dl(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,f=t,m=a,g=l,w=f.flags;switch(f.tag){case 0:case 11:case 15:Dl(u,f,m,g,n),Tn(8,f);break;case 23:break;case 22:var D=f.stateNode;f.memoizedState!==null?D._visibility&2?Dl(u,f,m,g,n):Dn(u,f):(D._visibility|=2,Dl(u,f,m,g,n)),n&&w&2048&&ir(f.alternate,f);break;case 24:Dl(u,f,m,g,n),n&&w&2048&&cr(f.alternate,f);break;default:Dl(u,f,m,g,n)}t=t.sibling}}function Dn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:Dn(a,l),n&2048&&ir(l.alternate,l);break;case 24:Dn(a,l),n&2048&&cr(l.alternate,l);break;default:Dn(a,l)}t=t.sibling}}var On=8192;function Ol(e){if(e.subtreeFlags&On)for(e=e.child;e!==null;)ed(e),e=e.sibling}function ed(e){switch(e.tag){case 26:Ol(e),e.flags&On&&e.memoizedState!==null&&my(Et,e.memoizedState,e.memoizedProps);break;case 5:Ol(e);break;case 3:case 4:var t=Et;Et=ni(e.stateNode.containerInfo),Ol(e),Et=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=On,On=16777216,Ol(e),On=t):Ol(e));break;default:Ol(e)}}function td(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Rn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Be=l,ld(l,e)}td(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ad(e),e=e.sibling}function ad(e){switch(e.tag){case 0:case 11:case 15:Rn(e),e.flags&2048&&ya(9,e,e.return);break;case 3:Rn(e);break;case 12:Rn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ku(e)):Rn(e);break;default:Rn(e)}}function ku(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Be=l,ld(l,e)}td(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ya(8,t,t.return),ku(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,ku(t));break;default:ku(t)}e=e.sibling}}function ld(e,t){for(;Be!==null;){var a=Be;switch(a.tag){case 0:case 11:case 15:ya(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:mn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Be=l;else e:for(a=e;Be!==null;){l=Be;var n=l.sibling,u=l.return;if(Jo(l),l===a){Be=null;break e}if(n!==null){n.return=u,Be=n;break e}Be=u}}}var D0={getCacheForType:function(e){var t=Ke(ze),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},O0=typeof WeakMap=="function"?WeakMap:Map,fe=0,xe=null,ne=null,ie=0,oe=0,ft=null,xa=!1,Rl=!1,rr=!1,Ft=0,Te=0,pa=0,$a=0,sr=0,St=0,_l=0,_n=null,tt=null,fr=!1,or=0,Ku=1/0,Ju=null,ba=null,Ge=0,Sa=null,zl=null,Cl=0,dr=0,mr=null,nd=null,zn=0,hr=null;function ot(){if((fe&2)!==0&&ie!==0)return ie&-ie;if(O.T!==null){var e=Sl;return e!==0?e:Sr()}return xs()}function ud(){St===0&&(St=(ie&536870912)===0||se?hs():536870912);var e=bt.current;return e!==null&&(e.flags|=32),St}function dt(e,t,a){(e===xe&&(oe===2||oe===9)||e.cancelPendingCommit!==null)&&(Ul(e,0),Na(e,ie,St,!1)),$l(e,a),((fe&2)===0||e!==xe)&&(e===xe&&((fe&2)===0&&($a|=a),Te===4&&Na(e,ie,St,!1)),zt(e))}function id(e,t,a){if((fe&6)!==0)throw Error(c(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Wl(e,t),n=l?z0(e,t):vr(e,t,!0),u=l;do{if(n===0){Rl&&!l&&Na(e,t,0,!1);break}else{if(a=e.current.alternate,u&&!R0(a)){n=vr(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var m=e;n=_n;var g=m.current.memoizedState.isDehydrated;if(g&&(Ul(m,f).flags|=256),f=vr(m,f,!1),f!==2){if(rr&&!g){m.errorRecoveryDisabledLanes|=u,$a|=u,n=4;break e}u=tt,tt=n,u!==null&&(tt===null?tt=u:tt.push.apply(tt,u))}n=f}if(u=!1,n!==2)continue}}if(n===1){Ul(e,0),Na(e,t,0,!0);break}e:{switch(l=e,u=n,u){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Na(l,t,St,!xa);break e;case 2:tt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(n=or+300-At(),10<n)){if(Na(l,t,St,!xa),uu(l,0,!0)!==0)break e;l.timeoutHandle=Ud(cd.bind(null,l,a,tt,Ju,fr,t,St,$a,_l,xa,u,2,-0,0),n);break e}cd(l,a,tt,Ju,fr,t,St,$a,_l,xa,u,0,-0,0)}}break}while(!0);zt(e)}function cd(e,t,a,l,n,u,f,m,g,w,D,z,M,T){if(e.timeoutHandle=-1,z=t.subtreeFlags,(z&8192||(z&16785408)===16785408)&&(Ln={stylesheets:null,count:0,unsuspend:dy},ed(t),z=hy(),z!==null)){e.cancelPendingCommit=z(hd.bind(null,e,t,u,a,l,n,f,m,g,D,1,M,T)),Na(e,u,f,!w);return}hd(e,t,u,a,l,n,f,m,g)}function R0(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],u=n.getSnapshot;n=n.value;try{if(!it(u(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Na(e,t,a,l){t&=~sr,t&=~$a,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var u=31-ut(n),f=1<<u;l[u]=-1,n&=~f}a!==0&&gs(e,a,t)}function Wu(){return(fe&6)===0?(Cn(0),!1):!0}function yr(){if(ne!==null){if(oe===0)var e=ne.return;else e=ne,Qt=Va=null,_c(e),Tl=null,En=0,e=ne;for(;e!==null;)Lo(e.alternate,e),e=e.return;ne=null}}function Ul(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,W0(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),yr(),xe=e,ne=a=Lt(e.current,null),ie=t,oe=0,ft=null,xa=!1,Rl=Wl(e,t),rr=!1,_l=St=sr=$a=pa=Te=0,tt=_n=null,fr=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-ut(l),u=1<<n;t|=e[n],l&=~u}return Ft=t,vu(),a}function rd(e,t){ae=null,O.H=Hu,t===yn||t===Mu?(t=Ef(),oe=3):t===Sf?(t=Ef(),oe=4):oe=t===Mo?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,ft=t,ne===null&&(Te=1,Xu(e,gt(t,e.current)))}function sd(){var e=O.H;return O.H=Hu,e===null?Hu:e}function fd(){var e=O.A;return O.A=D0,e}function gr(){Te=4,xa||(ie&4194048)!==ie&&bt.current!==null||(Rl=!0),(pa&134217727)===0&&($a&134217727)===0||xe===null||Na(xe,ie,St,!1)}function vr(e,t,a){var l=fe;fe|=2;var n=sd(),u=fd();(xe!==e||ie!==t)&&(Ju=null,Ul(e,t)),t=!1;var f=Te;e:do try{if(oe!==0&&ne!==null){var m=ne,g=ft;switch(oe){case 8:yr(),f=6;break e;case 3:case 2:case 9:case 6:bt.current===null&&(t=!0);var w=oe;if(oe=0,ft=null,Hl(e,m,g,w),a&&Rl){f=0;break e}break;default:w=oe,oe=0,ft=null,Hl(e,m,g,w)}}_0(),f=Te;break}catch(D){rd(e,D)}while(!0);return t&&e.shellSuspendCounter++,Qt=Va=null,fe=l,O.H=n,O.A=u,ne===null&&(xe=null,ie=0,vu()),f}function _0(){for(;ne!==null;)od(ne)}function z0(e,t){var a=fe;fe|=2;var l=sd(),n=fd();xe!==e||ie!==t?(Ju=null,Ku=At()+500,Ul(e,t)):Rl=Wl(e,t);e:do try{if(oe!==0&&ne!==null){t=ne;var u=ft;t:switch(oe){case 1:oe=0,ft=null,Hl(e,t,u,1);break;case 2:case 9:if(Nf(u)){oe=0,ft=null,dd(t);break}t=function(){oe!==2&&oe!==9||xe!==e||(oe=7),zt(e)},u.then(t,t);break e;case 3:oe=7;break e;case 4:oe=5;break e;case 7:Nf(u)?(oe=0,ft=null,dd(t)):(oe=0,ft=null,Hl(e,t,u,7));break;case 5:var f=null;switch(ne.tag){case 26:f=ne.memoizedState;case 5:case 27:var m=ne;if(!f||Kd(f)){oe=0,ft=null;var g=m.sibling;if(g!==null)ne=g;else{var w=m.return;w!==null?(ne=w,$u(w)):ne=null}break t}}oe=0,ft=null,Hl(e,t,u,5);break;case 6:oe=0,ft=null,Hl(e,t,u,6);break;case 8:yr(),Te=6;break e;default:throw Error(c(462))}}C0();break}catch(D){rd(e,D)}while(!0);return Qt=Va=null,O.H=l,O.A=n,fe=a,ne!==null?0:(xe=null,ie=0,vu(),Te)}function C0(){for(;ne!==null&&!ah();)od(ne)}function od(e){var t=qo(e.alternate,e,Ft);e.memoizedProps=e.pendingProps,t===null?$u(e):ne=t}function dd(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=_o(a,t,t.pendingProps,t.type,void 0,ie);break;case 11:t=_o(a,t,t.pendingProps,t.type.render,t.ref,ie);break;case 5:_c(t);default:Lo(a,t),t=ne=df(t,Ft),t=qo(a,t,Ft)}e.memoizedProps=e.pendingProps,t===null?$u(e):ne=t}function Hl(e,t,a,l){Qt=Va=null,_c(t),Tl=null,En=0;var n=t.return;try{if(j0(e,n,t,a,ie)){Te=1,Xu(e,gt(a,e.current)),ne=null;return}}catch(u){if(n!==null)throw ne=n,u;Te=1,Xu(e,gt(a,e.current)),ne=null;return}t.flags&32768?(se||l===1?e=!0:Rl||(ie&536870912)!==0?e=!1:(xa=e=!0,(l===2||l===9||l===3||l===6)&&(l=bt.current,l!==null&&l.tag===13&&(l.flags|=16384))),md(t,e)):$u(t)}function $u(e){var t=e;do{if((t.flags&32768)!==0){md(t,xa);return}e=t.return;var a=w0(t.alternate,t,Ft);if(a!==null){ne=a;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);Te===0&&(Te=5)}function md(e,t){do{var a=M0(e.alternate,e);if(a!==null){a.flags&=32767,ne=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ne=e;return}ne=e=a}while(e!==null);Te=6,ne=null}function hd(e,t,a,l,n,u,f,m,g){e.cancelPendingCommit=null;do Fu();while(Ge!==0);if((fe&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(u=t.lanes|t.childLanes,u|=cc,dh(e,a,u,f,m,g),e===xe&&(ne=xe=null,ie=0),zl=t,Sa=e,Cl=a,dr=u,mr=n,nd=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,q0(au,function(){return pd(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=O.T,O.T=null,n=L.p,L.p=2,f=fe,fe|=4;try{T0(e,t,a)}finally{fe=f,L.p=n,O.T=l}}Ge=1,yd(),gd(),vd()}}function yd(){if(Ge===1){Ge=0;var e=Sa,t=zl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=O.T,O.T=null;var l=L.p;L.p=2;var n=fe;fe|=4;try{Fo(t,e);var u=Dr,f=tf(e.containerInfo),m=u.focusedElem,g=u.selectionRange;if(f!==m&&m&&m.ownerDocument&&ef(m.ownerDocument.documentElement,m)){if(g!==null&&ac(m)){var w=g.start,D=g.end;if(D===void 0&&(D=w),"selectionStart"in m)m.selectionStart=w,m.selectionEnd=Math.min(D,m.value.length);else{var z=m.ownerDocument||document,M=z&&z.defaultView||window;if(M.getSelection){var T=M.getSelection(),$=m.textContent.length,K=Math.min(g.start,$),ye=g.end===void 0?K:Math.min(g.end,$);!T.extend&&K>ye&&(f=ye,ye=K,K=f);var S=Is(m,K),b=Is(m,ye);if(S&&b&&(T.rangeCount!==1||T.anchorNode!==S.node||T.anchorOffset!==S.offset||T.focusNode!==b.node||T.focusOffset!==b.offset)){var j=z.createRange();j.setStart(S.node,S.offset),T.removeAllRanges(),K>ye?(T.addRange(j),T.extend(b.node,b.offset)):(j.setEnd(b.node,b.offset),T.addRange(j))}}}}for(z=[],T=m;T=T.parentNode;)T.nodeType===1&&z.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<z.length;m++){var R=z[m];R.element.scrollLeft=R.left,R.element.scrollTop=R.top}}si=!!Ar,Dr=Ar=null}finally{fe=n,L.p=l,O.T=a}}e.current=t,Ge=2}}function gd(){if(Ge===2){Ge=0;var e=Sa,t=zl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=O.T,O.T=null;var l=L.p;L.p=2;var n=fe;fe|=4;try{Ko(e,t.alternate,t)}finally{fe=n,L.p=l,O.T=a}}Ge=3}}function vd(){if(Ge===4||Ge===3){Ge=0,lh();var e=Sa,t=zl,a=Cl,l=nd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ge=5:(Ge=0,zl=Sa=null,xd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(ba=null),Ui(a),t=t.stateNode,nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(Jl,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=O.T,n=L.p,L.p=2,O.T=null;try{for(var u=e.onRecoverableError,f=0;f<l.length;f++){var m=l[f];u(m.value,{componentStack:m.stack})}}finally{O.T=t,L.p=n}}(Cl&3)!==0&&Fu(),zt(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===hr?zn++:(zn=0,hr=e):zn=0,Cn(0)}}function xd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,mn(t)))}function Fu(e){return yd(),gd(),vd(),pd()}function pd(){if(Ge!==5)return!1;var e=Sa,t=dr;dr=0;var a=Ui(Cl),l=O.T,n=L.p;try{L.p=32>a?32:a,O.T=null,a=mr,mr=null;var u=Sa,f=Cl;if(Ge=0,zl=Sa=null,Cl=0,(fe&6)!==0)throw Error(c(331));var m=fe;if(fe|=4,ad(u.current),Io(u,u.current,f,a),fe=m,Cn(0,!1),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(Jl,u)}catch{}return!0}finally{L.p=n,O.T=l,xd(e,t)}}function bd(e,t,a){t=gt(a,t),t=kc(e.stateNode,t,2),e=oa(e,t,2),e!==null&&($l(e,2),zt(e))}function ve(e,t,a){if(e.tag===3)bd(e,e,a);else for(;t!==null;){if(t.tag===3){bd(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(ba===null||!ba.has(l))){e=gt(a,e),a=Eo(2),l=oa(t,a,2),l!==null&&(wo(a,l,t,e),$l(l,2),zt(l));break}}t=t.return}}function xr(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new O0;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(rr=!0,n.add(a),e=U0.bind(null,e,t,a),t.then(e,e))}function U0(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,xe===e&&(ie&a)===a&&(Te===4||Te===3&&(ie&62914560)===ie&&300>At()-or?(fe&2)===0&&Ul(e,0):sr|=a,_l===ie&&(_l=0)),zt(e)}function Sd(e,t){t===0&&(t=ys()),e=vl(e,t),e!==null&&($l(e,t),zt(e))}function H0(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Sd(e,a)}function Y0(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),Sd(e,a)}function q0(e,t){return Ri(e,t)}var Pu=null,Yl=null,pr=!1,Iu=!1,br=!1,Fa=0;function zt(e){e!==Yl&&e.next===null&&(Yl===null?Pu=Yl=e:Yl=Yl.next=e),Iu=!0,pr||(pr=!0,L0())}function Cn(e,t){if(!br&&Iu){br=!0;do for(var a=!1,l=Pu;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var u=0;else{var f=l.suspendedLanes,m=l.pingedLanes;u=(1<<31-ut(42|e)+1)-1,u&=n&~(f&~m),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(a=!0,wd(l,u))}else u=ie,u=uu(l,l===xe?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||Wl(l,u)||(a=!0,wd(l,u));l=l.next}while(a);br=!1}}function B0(){Nd()}function Nd(){Iu=pr=!1;var e=0;Fa!==0&&(J0()&&(e=Fa),Fa=0);for(var t=At(),a=null,l=Pu;l!==null;){var n=l.next,u=jd(l,t);u===0?(l.next=null,a===null?Pu=n:a.next=n,n===null&&(Yl=a)):(a=l,(e!==0||(u&3)!==0)&&(Iu=!0)),l=n}Cn(e)}function jd(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var f=31-ut(u),m=1<<f,g=n[f];g===-1?((m&a)===0||(m&l)!==0)&&(n[f]=oh(m,t)):g<=t&&(e.expiredLanes|=m),u&=~m}if(t=xe,a=ie,a=uu(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(oe===2||oe===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&_i(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Wl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&_i(l),Ui(a)){case 2:case 8:a=ds;break;case 32:a=au;break;case 268435456:a=ms;break;default:a=au}return l=Ed.bind(null,e),a=Ri(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&_i(l),e.callbackPriority=2,e.callbackNode=null,2}function Ed(e,t){if(Ge!==0&&Ge!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(Fu()&&e.callbackNode!==a)return null;var l=ie;return l=uu(e,e===xe?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(id(e,l,t),jd(e,At()),e.callbackNode!=null&&e.callbackNode===a?Ed.bind(null,e):null)}function wd(e,t){if(Fu())return null;id(e,t,!0)}function L0(){$0(function(){(fe&6)!==0?Ri(os,B0):Nd()})}function Sr(){return Fa===0&&(Fa=hs()),Fa}function Md(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:fu(""+e)}function Td(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function X0(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var u=Md((n[Fe]||null).action),f=l.submitter;f&&(t=(t=f[Fe]||null)?Md(t.formAction):f.getAttribute("formAction"),t!==null&&(u=t,f=null));var m=new hu("action","action",null,l,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Fa!==0){var g=f?Td(n,f):new FormData(n);Xc(a,{pending:!0,data:g,method:n.method,action:u},null,g)}}else typeof u=="function"&&(m.preventDefault(),g=f?Td(n,f):new FormData(n),Xc(a,{pending:!0,data:g,method:n.method,action:u},u,g))},currentTarget:n}]})}}for(var Nr=0;Nr<ic.length;Nr++){var jr=ic[Nr],G0=jr.toLowerCase(),Q0=jr[0].toUpperCase()+jr.slice(1);jt(G0,"on"+Q0)}jt(nf,"onAnimationEnd"),jt(uf,"onAnimationIteration"),jt(cf,"onAnimationStart"),jt("dblclick","onDoubleClick"),jt("focusin","onFocus"),jt("focusout","onBlur"),jt(i0,"onTransitionRun"),jt(c0,"onTransitionStart"),jt(r0,"onTransitionCancel"),jt(rf,"onTransitionEnd"),cl("onMouseEnter",["mouseout","mouseover"]),cl("onMouseLeave",["mouseout","mouseover"]),cl("onPointerEnter",["pointerout","pointerover"]),cl("onPointerLeave",["pointerout","pointerover"]),Ua("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ua("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ua("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ua("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ua("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ua("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Un="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),V0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Un));function Ad(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var u=void 0;if(t)for(var f=l.length-1;0<=f;f--){var m=l[f],g=m.instance,w=m.currentTarget;if(m=m.listener,g!==u&&n.isPropagationStopped())break e;u=m,n.currentTarget=w;try{u(n)}catch(D){Lu(D)}n.currentTarget=null,u=g}else for(f=0;f<l.length;f++){if(m=l[f],g=m.instance,w=m.currentTarget,m=m.listener,g!==u&&n.isPropagationStopped())break e;u=m,n.currentTarget=w;try{u(n)}catch(D){Lu(D)}n.currentTarget=null,u=g}}}}function ue(e,t){var a=t[Hi];a===void 0&&(a=t[Hi]=new Set);var l=e+"__bubble";a.has(l)||(Dd(t,e,2,!1),a.add(l))}function Er(e,t,a){var l=0;t&&(l|=4),Dd(a,e,l,t)}var ei="_reactListening"+Math.random().toString(36).slice(2);function wr(e){if(!e[ei]){e[ei]=!0,bs.forEach(function(a){a!=="selectionchange"&&(V0.has(a)||Er(a,!1,e),Er(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ei]||(t[ei]=!0,Er("selectionchange",!1,t))}}function Dd(e,t,a,l){switch(Id(t)){case 2:var n=vy;break;case 8:n=xy;break;default:n=Br}a=n.bind(null,t,a,e),n=void 0,!Ki||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Mr(e,t,a,l,n){var u=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var m=l.stateNode.containerInfo;if(m===n)break;if(f===4)for(f=l.return;f!==null;){var g=f.tag;if((g===3||g===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;m!==null;){if(f=nl(m),f===null)return;if(g=f.tag,g===5||g===6||g===26||g===27){l=u=f;continue e}m=m.parentNode}}l=l.return}Cs(function(){var w=u,D=Zi(a),z=[];e:{var M=sf.get(e);if(M!==void 0){var T=hu,$=e;switch(e){case"keypress":if(du(a)===0)break e;case"keydown":case"keyup":T=qh;break;case"focusin":$="focus",T=Fi;break;case"focusout":$="blur",T=Fi;break;case"beforeblur":case"afterblur":T=Fi;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=Ys;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=Mh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=Xh;break;case nf:case uf:case cf:T=Dh;break;case rf:T=Qh;break;case"scroll":case"scrollend":T=Eh;break;case"wheel":T=Zh;break;case"copy":case"cut":case"paste":T=Rh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=Bs;break;case"toggle":case"beforetoggle":T=Kh}var K=(t&4)!==0,ye=!K&&(e==="scroll"||e==="scrollend"),S=K?M!==null?M+"Capture":null:M;K=[];for(var b=w,j;b!==null;){var R=b;if(j=R.stateNode,R=R.tag,R!==5&&R!==26&&R!==27||j===null||S===null||(R=Il(b,S),R!=null&&K.push(Hn(b,R,j))),ye)break;b=b.return}0<K.length&&(M=new T(M,$,null,a,D),z.push({event:M,listeners:K}))}}if((t&7)===0){e:{if(M=e==="mouseover"||e==="pointerover",T=e==="mouseout"||e==="pointerout",M&&a!==Vi&&($=a.relatedTarget||a.fromElement)&&(nl($)||$[ll]))break e;if((T||M)&&(M=D.window===D?D:(M=D.ownerDocument)?M.defaultView||M.parentWindow:window,T?($=a.relatedTarget||a.toElement,T=w,$=$?nl($):null,$!==null&&(ye=h($),K=$.tag,$!==ye||K!==5&&K!==27&&K!==6)&&($=null)):(T=null,$=w),T!==$)){if(K=Ys,R="onMouseLeave",S="onMouseEnter",b="mouse",(e==="pointerout"||e==="pointerover")&&(K=Bs,R="onPointerLeave",S="onPointerEnter",b="pointer"),ye=T==null?M:Pl(T),j=$==null?M:Pl($),M=new K(R,b+"leave",T,a,D),M.target=ye,M.relatedTarget=j,R=null,nl(D)===w&&(K=new K(S,b+"enter",$,a,D),K.target=j,K.relatedTarget=ye,R=K),ye=R,T&&$)t:{for(K=T,S=$,b=0,j=K;j;j=ql(j))b++;for(j=0,R=S;R;R=ql(R))j++;for(;0<b-j;)K=ql(K),b--;for(;0<j-b;)S=ql(S),j--;for(;b--;){if(K===S||S!==null&&K===S.alternate)break t;K=ql(K),S=ql(S)}K=null}else K=null;T!==null&&Od(z,M,T,K,!1),$!==null&&ye!==null&&Od(z,ye,$,K,!0)}}e:{if(M=w?Pl(w):window,T=M.nodeName&&M.nodeName.toLowerCase(),T==="select"||T==="input"&&M.type==="file")var Q=Ks;else if(Zs(M))if(Js)Q=l0;else{Q=t0;var le=e0}else T=M.nodeName,!T||T.toLowerCase()!=="input"||M.type!=="checkbox"&&M.type!=="radio"?w&&Qi(w.elementType)&&(Q=Ks):Q=a0;if(Q&&(Q=Q(e,w))){ks(z,Q,a,D);break e}le&&le(e,M,w),e==="focusout"&&w&&M.type==="number"&&w.memoizedProps.value!=null&&Gi(M,"number",M.value)}switch(le=w?Pl(w):window,e){case"focusin":(Zs(le)||le.contentEditable==="true")&&(hl=le,lc=w,rn=null);break;case"focusout":rn=lc=hl=null;break;case"mousedown":nc=!0;break;case"contextmenu":case"mouseup":case"dragend":nc=!1,af(z,a,D);break;case"selectionchange":if(u0)break;case"keydown":case"keyup":af(z,a,D)}var V;if(Ii)e:{switch(e){case"compositionstart":var J="onCompositionStart";break e;case"compositionend":J="onCompositionEnd";break e;case"compositionupdate":J="onCompositionUpdate";break e}J=void 0}else ml?Qs(e,a)&&(J="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(J="onCompositionStart");J&&(Ls&&a.locale!=="ko"&&(ml||J!=="onCompositionStart"?J==="onCompositionEnd"&&ml&&(V=Us()):(ca=D,Ji="value"in ca?ca.value:ca.textContent,ml=!0)),le=ti(w,J),0<le.length&&(J=new qs(J,e,null,a,D),z.push({event:J,listeners:le}),V?J.data=V:(V=Vs(a),V!==null&&(J.data=V)))),(V=Wh?$h(e,a):Fh(e,a))&&(J=ti(w,"onBeforeInput"),0<J.length&&(le=new qs("onBeforeInput","beforeinput",null,a,D),z.push({event:le,listeners:J}),le.data=V)),X0(z,e,w,a,D)}Ad(z,t)})}function Hn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function ti(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Il(e,a),n!=null&&l.unshift(Hn(e,n,u)),n=Il(e,t),n!=null&&l.push(Hn(e,n,u))),e.tag===3)return l;e=e.return}return[]}function ql(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Od(e,t,a,l,n){for(var u=t._reactName,f=[];a!==null&&a!==l;){var m=a,g=m.alternate,w=m.stateNode;if(m=m.tag,g!==null&&g===l)break;m!==5&&m!==26&&m!==27||w===null||(g=w,n?(w=Il(a,u),w!=null&&f.unshift(Hn(a,w,g))):n||(w=Il(a,u),w!=null&&f.push(Hn(a,w,g)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var Z0=/\r\n?/g,k0=/\u0000|\uFFFD/g;function Rd(e){return(typeof e=="string"?e:""+e).replace(Z0,`
`).replace(k0,"")}function _d(e,t){return t=Rd(t),Rd(e)===t}function ai(){}function he(e,t,a,l,n,u){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||fl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&fl(e,""+l);break;case"className":cu(e,"class",l);break;case"tabIndex":cu(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":cu(e,a,l);break;case"style":_s(e,l,u);break;case"data":if(t!=="object"){cu(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=fu(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(a==="formAction"?(t!=="input"&&he(e,t,"name",n.name,n,null),he(e,t,"formEncType",n.formEncType,n,null),he(e,t,"formMethod",n.formMethod,n,null),he(e,t,"formTarget",n.formTarget,n,null)):(he(e,t,"encType",n.encType,n,null),he(e,t,"method",n.method,n,null),he(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=fu(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=ai);break;case"onScroll":l!=null&&ue("scroll",e);break;case"onScrollEnd":l!=null&&ue("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=fu(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":ue("beforetoggle",e),ue("toggle",e),iu(e,"popover",l);break;case"xlinkActuate":qt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":qt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":qt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":qt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":qt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":qt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":qt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":qt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":qt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":iu(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Nh.get(a)||a,iu(e,a,l))}}function Tr(e,t,a,l,n,u){switch(a){case"style":_s(e,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"children":typeof l=="string"?fl(e,l):(typeof l=="number"||typeof l=="bigint")&&fl(e,""+l);break;case"onScroll":l!=null&&ue("scroll",e);break;case"onScrollEnd":l!=null&&ue("scrollend",e);break;case"onClick":l!=null&&(e.onclick=ai);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ss.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),u=e[Fe]||null,u=u!=null?u[a]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof l=="function")){typeof u!="function"&&u!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):iu(e,a,l)}}}function Qe(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ue("error",e),ue("load",e);var l=!1,n=!1,u;for(u in a)if(a.hasOwnProperty(u)){var f=a[u];if(f!=null)switch(u){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:he(e,t,u,f,a,null)}}n&&he(e,t,"srcSet",a.srcSet,a,null),l&&he(e,t,"src",a.src,a,null);return;case"input":ue("invalid",e);var m=u=f=n=null,g=null,w=null;for(l in a)if(a.hasOwnProperty(l)){var D=a[l];if(D!=null)switch(l){case"name":n=D;break;case"type":f=D;break;case"checked":g=D;break;case"defaultChecked":w=D;break;case"value":u=D;break;case"defaultValue":m=D;break;case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(c(137,t));break;default:he(e,t,l,D,a,null)}}As(e,u,m,g,w,f,n,!1),ru(e);return;case"select":ue("invalid",e),l=f=u=null;for(n in a)if(a.hasOwnProperty(n)&&(m=a[n],m!=null))switch(n){case"value":u=m;break;case"defaultValue":f=m;break;case"multiple":l=m;default:he(e,t,n,m,a,null)}t=u,a=f,e.multiple=!!l,t!=null?sl(e,!!l,t,!1):a!=null&&sl(e,!!l,a,!0);return;case"textarea":ue("invalid",e),u=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(m=a[f],m!=null))switch(f){case"value":l=m;break;case"defaultValue":n=m;break;case"children":u=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(c(91));break;default:he(e,t,f,m,a,null)}Os(e,l,n,u),ru(e);return;case"option":for(g in a)if(a.hasOwnProperty(g)&&(l=a[g],l!=null))switch(g){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:he(e,t,g,l,a,null)}return;case"dialog":ue("beforetoggle",e),ue("toggle",e),ue("cancel",e),ue("close",e);break;case"iframe":case"object":ue("load",e);break;case"video":case"audio":for(l=0;l<Un.length;l++)ue(Un[l],e);break;case"image":ue("error",e),ue("load",e);break;case"details":ue("toggle",e);break;case"embed":case"source":case"link":ue("error",e),ue("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(w in a)if(a.hasOwnProperty(w)&&(l=a[w],l!=null))switch(w){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:he(e,t,w,l,a,null)}return;default:if(Qi(t)){for(D in a)a.hasOwnProperty(D)&&(l=a[D],l!==void 0&&Tr(e,t,D,l,a,void 0));return}}for(m in a)a.hasOwnProperty(m)&&(l=a[m],l!=null&&he(e,t,m,l,a,null))}function K0(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,f=null,m=null,g=null,w=null,D=null;for(T in a){var z=a[T];if(a.hasOwnProperty(T)&&z!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":g=z;default:l.hasOwnProperty(T)||he(e,t,T,null,l,z)}}for(var M in l){var T=l[M];if(z=a[M],l.hasOwnProperty(M)&&(T!=null||z!=null))switch(M){case"type":u=T;break;case"name":n=T;break;case"checked":w=T;break;case"defaultChecked":D=T;break;case"value":f=T;break;case"defaultValue":m=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(c(137,t));break;default:T!==z&&he(e,t,M,T,l,z)}}Xi(e,f,m,g,w,D,u,n);return;case"select":T=f=m=M=null;for(u in a)if(g=a[u],a.hasOwnProperty(u)&&g!=null)switch(u){case"value":break;case"multiple":T=g;default:l.hasOwnProperty(u)||he(e,t,u,null,l,g)}for(n in l)if(u=l[n],g=a[n],l.hasOwnProperty(n)&&(u!=null||g!=null))switch(n){case"value":M=u;break;case"defaultValue":m=u;break;case"multiple":f=u;default:u!==g&&he(e,t,n,u,l,g)}t=m,a=f,l=T,M!=null?sl(e,!!a,M,!1):!!l!=!!a&&(t!=null?sl(e,!!a,t,!0):sl(e,!!a,a?[]:"",!1));return;case"textarea":T=M=null;for(m in a)if(n=a[m],a.hasOwnProperty(m)&&n!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:he(e,t,m,null,l,n)}for(f in l)if(n=l[f],u=a[f],l.hasOwnProperty(f)&&(n!=null||u!=null))switch(f){case"value":M=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(c(91));break;default:n!==u&&he(e,t,f,n,l,u)}Ds(e,M,T);return;case"option":for(var $ in a)if(M=a[$],a.hasOwnProperty($)&&M!=null&&!l.hasOwnProperty($))switch($){case"selected":e.selected=!1;break;default:he(e,t,$,null,l,M)}for(g in l)if(M=l[g],T=a[g],l.hasOwnProperty(g)&&M!==T&&(M!=null||T!=null))switch(g){case"selected":e.selected=M&&typeof M!="function"&&typeof M!="symbol";break;default:he(e,t,g,M,l,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var K in a)M=a[K],a.hasOwnProperty(K)&&M!=null&&!l.hasOwnProperty(K)&&he(e,t,K,null,l,M);for(w in l)if(M=l[w],T=a[w],l.hasOwnProperty(w)&&M!==T&&(M!=null||T!=null))switch(w){case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(c(137,t));break;default:he(e,t,w,M,l,T)}return;default:if(Qi(t)){for(var ye in a)M=a[ye],a.hasOwnProperty(ye)&&M!==void 0&&!l.hasOwnProperty(ye)&&Tr(e,t,ye,void 0,l,M);for(D in l)M=l[D],T=a[D],!l.hasOwnProperty(D)||M===T||M===void 0&&T===void 0||Tr(e,t,D,M,l,T);return}}for(var S in a)M=a[S],a.hasOwnProperty(S)&&M!=null&&!l.hasOwnProperty(S)&&he(e,t,S,null,l,M);for(z in l)M=l[z],T=a[z],!l.hasOwnProperty(z)||M===T||M==null&&T==null||he(e,t,z,M,l,T)}var Ar=null,Dr=null;function li(e){return e.nodeType===9?e:e.ownerDocument}function zd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Cd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Or(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Rr=null;function J0(){var e=window.event;return e&&e.type==="popstate"?e===Rr?!1:(Rr=e,!0):(Rr=null,!1)}var Ud=typeof setTimeout=="function"?setTimeout:void 0,W0=typeof clearTimeout=="function"?clearTimeout:void 0,Hd=typeof Promise=="function"?Promise:void 0,$0=typeof queueMicrotask=="function"?queueMicrotask:typeof Hd<"u"?function(e){return Hd.resolve(null).then(e).catch(F0)}:Ud;function F0(e){setTimeout(function(){throw e})}function ja(e){return e==="head"}function Yd(e,t){var a=t,l=0,n=0;do{var u=a.nextSibling;if(e.removeChild(a),u&&u.nodeType===8)if(a=u.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&Yn(f.documentElement),a&2&&Yn(f.body),a&4)for(a=f.head,Yn(a),f=a.firstChild;f;){var m=f.nextSibling,g=f.nodeName;f[Fl]||g==="SCRIPT"||g==="STYLE"||g==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=m}}if(n===0){e.removeChild(u),Zn(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=u}while(a);Zn(t)}function _r(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":_r(a),Yi(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function P0(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Fl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=wt(e.nextSibling),e===null)break}return null}function I0(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=wt(e.nextSibling),e===null))return null;return e}function zr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function ey(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function wt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Cr=null;function qd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Bd(e,t,a){switch(t=li(a),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Yn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Yi(e)}var Nt=new Map,Ld=new Set;function ni(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Pt=L.d;L.d={f:ty,r:ay,D:ly,C:ny,L:uy,m:iy,X:ry,S:cy,M:sy};function ty(){var e=Pt.f(),t=Wu();return e||t}function ay(e){var t=ul(e);t!==null&&t.tag===5&&t.type==="form"?uo(t):Pt.r(e)}var Bl=typeof document>"u"?null:document;function Xd(e,t,a){var l=Bl;if(l&&typeof t=="string"&&t){var n=yt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),Ld.has(n)||(Ld.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),Qe(t,"link",e),Ye(t),l.head.appendChild(t)))}}function ly(e){Pt.D(e),Xd("dns-prefetch",e,null)}function ny(e,t){Pt.C(e,t),Xd("preconnect",e,t)}function uy(e,t,a){Pt.L(e,t,a);var l=Bl;if(l&&e&&t){var n='link[rel="preload"][as="'+yt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+yt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+yt(a.imageSizes)+'"]')):n+='[href="'+yt(e)+'"]';var u=n;switch(t){case"style":u=Ll(e);break;case"script":u=Xl(e)}Nt.has(u)||(e=E({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Nt.set(u,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(qn(u))||t==="script"&&l.querySelector(Bn(u))||(t=l.createElement("link"),Qe(t,"link",e),Ye(t),l.head.appendChild(t)))}}function iy(e,t){Pt.m(e,t);var a=Bl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+yt(l)+'"][href="'+yt(e)+'"]',u=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Xl(e)}if(!Nt.has(u)&&(e=E({rel:"modulepreload",href:e},t),Nt.set(u,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Bn(u)))return}l=a.createElement("link"),Qe(l,"link",e),Ye(l),a.head.appendChild(l)}}}function cy(e,t,a){Pt.S(e,t,a);var l=Bl;if(l&&e){var n=il(l).hoistableStyles,u=Ll(e);t=t||"default";var f=n.get(u);if(!f){var m={loading:0,preload:null};if(f=l.querySelector(qn(u)))m.loading=5;else{e=E({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Nt.get(u))&&Ur(e,a);var g=f=l.createElement("link");Ye(g),Qe(g,"link",e),g._p=new Promise(function(w,D){g.onload=w,g.onerror=D}),g.addEventListener("load",function(){m.loading|=1}),g.addEventListener("error",function(){m.loading|=2}),m.loading|=4,ui(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:m},n.set(u,f)}}}function ry(e,t){Pt.X(e,t);var a=Bl;if(a&&e){var l=il(a).hoistableScripts,n=Xl(e),u=l.get(n);u||(u=a.querySelector(Bn(n)),u||(e=E({src:e,async:!0},t),(t=Nt.get(n))&&Hr(e,t),u=a.createElement("script"),Ye(u),Qe(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function sy(e,t){Pt.M(e,t);var a=Bl;if(a&&e){var l=il(a).hoistableScripts,n=Xl(e),u=l.get(n);u||(u=a.querySelector(Bn(n)),u||(e=E({src:e,async:!0,type:"module"},t),(t=Nt.get(n))&&Hr(e,t),u=a.createElement("script"),Ye(u),Qe(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Gd(e,t,a,l){var n=(n=P.current)?ni(n):null;if(!n)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Ll(a.href),a=il(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Ll(a.href);var u=il(n).hoistableStyles,f=u.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,f),(u=n.querySelector(qn(e)))&&!u._p&&(f.instance=u,f.state.loading=5),Nt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Nt.set(e,a),u||fy(n,e,a,f.state))),t&&l===null)throw Error(c(528,""));return f}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Xl(a),a=il(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Ll(e){return'href="'+yt(e)+'"'}function qn(e){return'link[rel="stylesheet"]['+e+"]"}function Qd(e){return E({},e,{"data-precedence":e.precedence,precedence:null})}function fy(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Qe(t,"link",a),Ye(t),e.head.appendChild(t))}function Xl(e){return'[src="'+yt(e)+'"]'}function Bn(e){return"script[async]"+e}function Vd(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+yt(a.href)+'"]');if(l)return t.instance=l,Ye(l),l;var n=E({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ye(l),Qe(l,"style",n),ui(l,a.precedence,e),t.instance=l;case"stylesheet":n=Ll(a.href);var u=e.querySelector(qn(n));if(u)return t.state.loading|=4,t.instance=u,Ye(u),u;l=Qd(a),(n=Nt.get(n))&&Ur(l,n),u=(e.ownerDocument||e).createElement("link"),Ye(u);var f=u;return f._p=new Promise(function(m,g){f.onload=m,f.onerror=g}),Qe(u,"link",l),t.state.loading|=4,ui(u,a.precedence,e),t.instance=u;case"script":return u=Xl(a.src),(n=e.querySelector(Bn(u)))?(t.instance=n,Ye(n),n):(l=a,(n=Nt.get(u))&&(l=E({},a),Hr(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ye(n),Qe(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,ui(l,a.precedence,e));return t.instance}function ui(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,u=n,f=0;f<l.length;f++){var m=l[f];if(m.dataset.precedence===t)u=m;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Ur(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Hr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ii=null;function Zd(e,t,a){if(ii===null){var l=new Map,n=ii=new Map;n.set(a,l)}else n=ii,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var u=a[n];if(!(u[Fl]||u[ke]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(t)||"";f=e+f;var m=l.get(f);m?m.push(u):l.set(f,[u])}}return l}function kd(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function oy(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Kd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ln=null;function dy(){}function my(e,t,a){if(Ln===null)throw Error(c(475));var l=Ln;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Ll(a.href),u=e.querySelector(qn(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=ci.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=u,Ye(u);return}u=e.ownerDocument||e,a=Qd(a),(n=Nt.get(n))&&Ur(a,n),u=u.createElement("link"),Ye(u);var f=u;f._p=new Promise(function(m,g){f.onload=m,f.onerror=g}),Qe(u,"link",a),t.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=ci.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function hy(){if(Ln===null)throw Error(c(475));var e=Ln;return e.stylesheets&&e.count===0&&Yr(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Yr(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function ci(){if(this.count--,this.count===0){if(this.stylesheets)Yr(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var ri=null;function Yr(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,ri=new Map,t.forEach(yy,e),ri=null,ci.call(e))}function yy(e,t){if(!(t.state.loading&4)){var a=ri.get(e);if(a)var l=a.get(null);else{a=new Map,ri.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var f=n[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),u=a.get(f)||l,u===l&&a.set(null,n),a.set(f,n),this.count++,l=ci.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Xn={$$typeof:ee,Provider:null,Consumer:null,_currentValue:W,_currentValue2:W,_threadCount:0};function gy(e,t,a,l,n,u,f,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=zi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=zi(0),this.hiddenUpdates=zi(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Jd(e,t,a,l,n,u,f,m,g,w,D,z){return e=new gy(e,t,a,f,m,g,w,z),t=1,u===!0&&(t|=24),u=ct(3,null,null,t),e.current=u,u.stateNode=e,t=xc(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:l,isDehydrated:a,cache:t},Nc(u),e}function Wd(e){return e?(e=xl,e):xl}function $d(e,t,a,l,n,u){n=Wd(n),l.context===null?l.context=n:l.pendingContext=n,l=fa(t),l.payload={element:a},u=u===void 0?null:u,u!==null&&(l.callback=u),a=oa(e,l,t),a!==null&&(dt(a,e,t),vn(a,e,t))}function Fd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function qr(e,t){Fd(e,t),(e=e.alternate)&&Fd(e,t)}function Pd(e){if(e.tag===13){var t=vl(e,67108864);t!==null&&dt(t,e,67108864),qr(e,67108864)}}var si=!0;function vy(e,t,a,l){var n=O.T;O.T=null;var u=L.p;try{L.p=2,Br(e,t,a,l)}finally{L.p=u,O.T=n}}function xy(e,t,a,l){var n=O.T;O.T=null;var u=L.p;try{L.p=8,Br(e,t,a,l)}finally{L.p=u,O.T=n}}function Br(e,t,a,l){if(si){var n=Lr(l);if(n===null)Mr(e,t,l,fi,a),em(e,l);else if(by(n,e,t,a,l))l.stopPropagation();else if(em(e,l),t&4&&-1<py.indexOf(e)){for(;n!==null;){var u=ul(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=Ca(u.pendingLanes);if(f!==0){var m=u;for(m.pendingLanes|=2,m.entangledLanes|=2;f;){var g=1<<31-ut(f);m.entanglements[1]|=g,f&=~g}zt(u),(fe&6)===0&&(Ku=At()+500,Cn(0))}}break;case 13:m=vl(u,2),m!==null&&dt(m,u,2),Wu(),qr(u,2)}if(u=Lr(l),u===null&&Mr(e,t,l,fi,a),u===n)break;n=u}n!==null&&l.stopPropagation()}else Mr(e,t,l,null,a)}}function Lr(e){return e=Zi(e),Xr(e)}var fi=null;function Xr(e){if(fi=null,e=nl(e),e!==null){var t=h(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=x(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return fi=e,null}function Id(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(nh()){case os:return 2;case ds:return 8;case au:case uh:return 32;case ms:return 268435456;default:return 32}default:return 32}}var Gr=!1,Ea=null,wa=null,Ma=null,Gn=new Map,Qn=new Map,Ta=[],py="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function em(e,t){switch(e){case"focusin":case"focusout":Ea=null;break;case"dragenter":case"dragleave":wa=null;break;case"mouseover":case"mouseout":Ma=null;break;case"pointerover":case"pointerout":Gn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qn.delete(t.pointerId)}}function Vn(e,t,a,l,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:u,targetContainers:[n]},t!==null&&(t=ul(t),t!==null&&Pd(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function by(e,t,a,l,n){switch(t){case"focusin":return Ea=Vn(Ea,e,t,a,l,n),!0;case"dragenter":return wa=Vn(wa,e,t,a,l,n),!0;case"mouseover":return Ma=Vn(Ma,e,t,a,l,n),!0;case"pointerover":var u=n.pointerId;return Gn.set(u,Vn(Gn.get(u)||null,e,t,a,l,n)),!0;case"gotpointercapture":return u=n.pointerId,Qn.set(u,Vn(Qn.get(u)||null,e,t,a,l,n)),!0}return!1}function tm(e){var t=nl(e.target);if(t!==null){var a=h(t);if(a!==null){if(t=a.tag,t===13){if(t=x(a),t!==null){e.blockedOn=t,mh(e.priority,function(){if(a.tag===13){var l=ot();l=Ci(l);var n=vl(a,l);n!==null&&dt(n,a,l),qr(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function oi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Lr(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);Vi=l,a.target.dispatchEvent(l),Vi=null}else return t=ul(a),t!==null&&Pd(t),e.blockedOn=a,!1;t.shift()}return!0}function am(e,t,a){oi(e)&&a.delete(t)}function Sy(){Gr=!1,Ea!==null&&oi(Ea)&&(Ea=null),wa!==null&&oi(wa)&&(wa=null),Ma!==null&&oi(Ma)&&(Ma=null),Gn.forEach(am),Qn.forEach(am)}function di(e,t){e.blockedOn===t&&(e.blockedOn=null,Gr||(Gr=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Sy)))}var mi=null;function lm(e){mi!==e&&(mi=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){mi===e&&(mi=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(Xr(l||a)===null)continue;break}var u=ul(a);u!==null&&(e.splice(t,3),t-=3,Xc(u,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Zn(e){function t(g){return di(g,e)}Ea!==null&&di(Ea,e),wa!==null&&di(wa,e),Ma!==null&&di(Ma,e),Gn.forEach(t),Qn.forEach(t);for(var a=0;a<Ta.length;a++){var l=Ta[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Ta.length&&(a=Ta[0],a.blockedOn===null);)tm(a),a.blockedOn===null&&Ta.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],u=a[l+1],f=n[Fe]||null;if(typeof u=="function")f||lm(a);else if(f){var m=null;if(u&&u.hasAttribute("formAction")){if(n=u,f=u[Fe]||null)m=f.formAction;else if(Xr(n)!==null)continue}else m=f.action;typeof m=="function"?a[l+1]=m:(a.splice(l,3),l-=3),lm(a)}}}function Qr(e){this._internalRoot=e}hi.prototype.render=Qr.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var a=t.current,l=ot();$d(a,l,e,t,null,null)},hi.prototype.unmount=Qr.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$d(e.current,2,null,e,null,null),Wu(),t[ll]=null}};function hi(e){this._internalRoot=e}hi.prototype.unstable_scheduleHydration=function(e){if(e){var t=xs();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Ta.length&&t!==0&&t<Ta[a].priority;a++);Ta.splice(a,0,e),a===0&&tm(e)}};var nm=r.version;if(nm!=="19.1.0")throw Error(c(527,nm,"19.1.0"));L.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=v(t),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var Ny={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:O,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var yi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!yi.isDisabled&&yi.supportsFiber)try{Jl=yi.inject(Ny),nt=yi}catch{}}return Kn.createRoot=function(e,t){if(!d(e))throw Error(c(299));var a=!1,l="",n=bo,u=So,f=No,m=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=Jd(e,1,!1,null,null,a,l,n,u,f,m,null),e[ll]=t.current,wr(e),new Qr(t)},Kn.hydrateRoot=function(e,t,a){if(!d(e))throw Error(c(299));var l=!1,n="",u=bo,f=So,m=No,g=null,w=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(m=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(g=a.unstable_transitionCallbacks),a.formState!==void 0&&(w=a.formState)),t=Jd(e,1,!0,t,a??null,l,n,u,f,m,g,w),t.context=Wd(null),a=t.current,l=ot(),l=Ci(l),n=fa(l),n.callback=null,oa(a,n,l),a=l,t.current.lanes=a,$l(t,a),zt(t),e[ll]=t.current,wr(e),new hi(t)},Kn.version="19.1.0",Kn}var hm;function _y(){if(hm)return kr.exports;hm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),kr.exports=Ry(),kr.exports}var zy=_y();/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var ym="popstate";function Cy(i={}){function r(c,d){let{pathname:h,search:x,hash:N}=c.location;return es("",{pathname:h,search:x,hash:N},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function s(c,d){return typeof d=="string"?d:$n(d)}return Hy(r,s,null,i)}function Ne(i,r){if(i===!1||i===null||typeof i>"u")throw new Error(r)}function Mt(i,r){if(!i){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Uy(){return Math.random().toString(36).substring(2,10)}function gm(i,r){return{usr:i.state,key:i.key,idx:r}}function es(i,r,s=null,c){return{pathname:typeof i=="string"?i:i.pathname,search:"",hash:"",...typeof r=="string"?Zl(r):r,state:s,key:r&&r.key||c||Uy()}}function $n({pathname:i="/",search:r="",hash:s=""}){return r&&r!=="?"&&(i+=r.charAt(0)==="?"?r:"?"+r),s&&s!=="#"&&(i+=s.charAt(0)==="#"?s:"#"+s),i}function Zl(i){let r={};if(i){let s=i.indexOf("#");s>=0&&(r.hash=i.substring(s),i=i.substring(0,s));let c=i.indexOf("?");c>=0&&(r.search=i.substring(c),i=i.substring(0,c)),i&&(r.pathname=i)}return r}function Hy(i,r,s,c={}){let{window:d=document.defaultView,v5Compat:h=!1}=c,x=d.history,N="POP",v=null,y=E();y==null&&(y=0,x.replaceState({...x.state,idx:y},""));function E(){return(x.state||{idx:null}).idx}function _(){N="POP";let U=E(),Y=U==null?null:U-y;y=U,v&&v({action:N,location:G.location,delta:Y})}function H(U,Y){N="PUSH";let I=es(G.location,U,Y);y=E()+1;let ee=gm(I,y),je=G.createHref(I);try{x.pushState(ee,"",je)}catch(F){if(F instanceof DOMException&&F.name==="DataCloneError")throw F;d.location.assign(je)}h&&v&&v({action:N,location:G.location,delta:1})}function Z(U,Y){N="REPLACE";let I=es(G.location,U,Y);y=E();let ee=gm(I,y),je=G.createHref(I);x.replaceState(ee,"",je),h&&v&&v({action:N,location:G.location,delta:0})}function q(U){return Yy(U)}let G={get action(){return N},get location(){return i(d,x)},listen(U){if(v)throw new Error("A history only accepts one active listener");return d.addEventListener(ym,_),v=U,()=>{d.removeEventListener(ym,_),v=null}},createHref(U){return r(d,U)},createURL:q,encodeLocation(U){let Y=q(U);return{pathname:Y.pathname,search:Y.search,hash:Y.hash}},push:H,replace:Z,go(U){return x.go(U)}};return G}function Yy(i,r=!1){let s="http://localhost";typeof window<"u"&&(s=window.location.origin!=="null"?window.location.origin:window.location.href),Ne(s,"No window.location.(origin|href) available to create URL");let c=typeof i=="string"?i:$n(i);return c=c.replace(/ $/,"%20"),!r&&c.startsWith("//")&&(c=s+c),new URL(c,s)}function Om(i,r,s="/"){return qy(i,r,s,!1)}function qy(i,r,s,c){let d=typeof r=="string"?Zl(r):r,h=ta(d.pathname||"/",s);if(h==null)return null;let x=Rm(i);By(x);let N=null;for(let v=0;N==null&&v<x.length;++v){let y=$y(h);N=Jy(x[v],y,c)}return N}function Rm(i,r=[],s=[],c=""){let d=(h,x,N)=>{let v={relativePath:N===void 0?h.path||"":N,caseSensitive:h.caseSensitive===!0,childrenIndex:x,route:h};v.relativePath.startsWith("/")&&(Ne(v.relativePath.startsWith(c),`Absolute route path "${v.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(c.length));let y=ea([c,v.relativePath]),E=s.concat(v);h.children&&h.children.length>0&&(Ne(h.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${y}".`),Rm(h.children,r,E,y)),!(h.path==null&&!h.index)&&r.push({path:y,score:ky(y,h.index),routesMeta:E})};return i.forEach((h,x)=>{if(h.path===""||!h.path?.includes("?"))d(h,x);else for(let N of _m(h.path))d(h,x,N)}),r}function _m(i){let r=i.split("/");if(r.length===0)return[];let[s,...c]=r,d=s.endsWith("?"),h=s.replace(/\?$/,"");if(c.length===0)return d?[h,""]:[h];let x=_m(c.join("/")),N=[];return N.push(...x.map(v=>v===""?h:[h,v].join("/"))),d&&N.push(...x),N.map(v=>i.startsWith("/")&&v===""?"/":v)}function By(i){i.sort((r,s)=>r.score!==s.score?s.score-r.score:Ky(r.routesMeta.map(c=>c.childrenIndex),s.routesMeta.map(c=>c.childrenIndex)))}var Ly=/^:[\w-]+$/,Xy=3,Gy=2,Qy=1,Vy=10,Zy=-2,vm=i=>i==="*";function ky(i,r){let s=i.split("/"),c=s.length;return s.some(vm)&&(c+=Zy),r&&(c+=Gy),s.filter(d=>!vm(d)).reduce((d,h)=>d+(Ly.test(h)?Xy:h===""?Qy:Vy),c)}function Ky(i,r){return i.length===r.length&&i.slice(0,-1).every((c,d)=>c===r[d])?i[i.length-1]-r[r.length-1]:0}function Jy(i,r,s=!1){let{routesMeta:c}=i,d={},h="/",x=[];for(let N=0;N<c.length;++N){let v=c[N],y=N===c.length-1,E=h==="/"?r:r.slice(h.length)||"/",_=pi({path:v.relativePath,caseSensitive:v.caseSensitive,end:y},E),H=v.route;if(!_&&y&&s&&!c[c.length-1].route.index&&(_=pi({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},E)),!_)return null;Object.assign(d,_.params),x.push({params:d,pathname:ea([h,_.pathname]),pathnameBase:eg(ea([h,_.pathnameBase])),route:H}),_.pathnameBase!=="/"&&(h=ea([h,_.pathnameBase]))}return x}function pi(i,r){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[s,c]=Wy(i.path,i.caseSensitive,i.end),d=r.match(s);if(!d)return null;let h=d[0],x=h.replace(/(.)\/+$/,"$1"),N=d.slice(1);return{params:c.reduce((y,{paramName:E,isOptional:_},H)=>{if(E==="*"){let q=N[H]||"";x=h.slice(0,h.length-q.length).replace(/(.)\/+$/,"$1")}const Z=N[H];return _&&!Z?y[E]=void 0:y[E]=(Z||"").replace(/%2F/g,"/"),y},{}),pathname:h,pathnameBase:x,pattern:i}}function Wy(i,r=!1,s=!0){Mt(i==="*"||!i.endsWith("*")||i.endsWith("/*"),`Route path "${i}" will be treated as if it were "${i.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${i.replace(/\*$/,"/*")}".`);let c=[],d="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(x,N,v)=>(c.push({paramName:N,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(c.push({paramName:"*"}),d+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?d+="\\/*$":i!==""&&i!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,r?void 0:"i"),c]}function $y(i){try{return i.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Mt(!1,`The URL path "${i}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),i}}function ta(i,r){if(r==="/")return i;if(!i.toLowerCase().startsWith(r.toLowerCase()))return null;let s=r.endsWith("/")?r.length-1:r.length,c=i.charAt(s);return c&&c!=="/"?null:i.slice(s)||"/"}function Fy(i,r="/"){let{pathname:s,search:c="",hash:d=""}=typeof i=="string"?Zl(i):i;return{pathname:s?s.startsWith("/")?s:Py(s,r):r,search:tg(c),hash:ag(d)}}function Py(i,r){let s=r.replace(/\/+$/,"").split("/");return i.split("/").forEach(d=>{d===".."?s.length>1&&s.pop():d!=="."&&s.push(d)}),s.length>1?s.join("/"):"/"}function $r(i,r,s,c){return`Cannot include a '${i}' character in a manually specified \`to.${r}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Iy(i){return i.filter((r,s)=>s===0||r.route.path&&r.route.path.length>0)}function ns(i){let r=Iy(i);return r.map((s,c)=>c===r.length-1?s.pathname:s.pathnameBase)}function us(i,r,s,c=!1){let d;typeof i=="string"?d=Zl(i):(d={...i},Ne(!d.pathname||!d.pathname.includes("?"),$r("?","pathname","search",d)),Ne(!d.pathname||!d.pathname.includes("#"),$r("#","pathname","hash",d)),Ne(!d.search||!d.search.includes("#"),$r("#","search","hash",d)));let h=i===""||d.pathname==="",x=h?"/":d.pathname,N;if(x==null)N=s;else{let _=r.length-1;if(!c&&x.startsWith("..")){let H=x.split("/");for(;H[0]==="..";)H.shift(),_-=1;d.pathname=H.join("/")}N=_>=0?r[_]:"/"}let v=Fy(d,N),y=x&&x!=="/"&&x.endsWith("/"),E=(h||x===".")&&s.endsWith("/");return!v.pathname.endsWith("/")&&(y||E)&&(v.pathname+="/"),v}var ea=i=>i.join("/").replace(/\/\/+/g,"/"),eg=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),tg=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,ag=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function lg(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}var zm=["POST","PUT","PATCH","DELETE"];new Set(zm);var ng=["GET",...zm];new Set(ng);var kl=A.createContext(null);kl.displayName="DataRouter";var wi=A.createContext(null);wi.displayName="DataRouterState";A.createContext(!1);var Cm=A.createContext({isTransitioning:!1});Cm.displayName="ViewTransition";var ug=A.createContext(new Map);ug.displayName="Fetchers";var ig=A.createContext(null);ig.displayName="Await";var Tt=A.createContext(null);Tt.displayName="Navigation";var In=A.createContext(null);In.displayName="Location";var Yt=A.createContext({outlet:null,matches:[],isDataRoute:!1});Yt.displayName="Route";var is=A.createContext(null);is.displayName="RouteError";function cg(i,{relative:r}={}){Ne(Kl(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:c}=A.useContext(Tt),{hash:d,pathname:h,search:x}=eu(i,{relative:r}),N=h;return s!=="/"&&(N=h==="/"?s:ea([s,h])),c.createHref({pathname:N,search:x,hash:d})}function Kl(){return A.useContext(In)!=null}function la(){return Ne(Kl(),"useLocation() may be used only in the context of a <Router> component."),A.useContext(In).location}var Um="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Hm(i){A.useContext(Tt).static||A.useLayoutEffect(i)}function Ym(){let{isDataRoute:i}=A.useContext(Yt);return i?bg():rg()}function rg(){Ne(Kl(),"useNavigate() may be used only in the context of a <Router> component.");let i=A.useContext(kl),{basename:r,navigator:s}=A.useContext(Tt),{matches:c}=A.useContext(Yt),{pathname:d}=la(),h=JSON.stringify(ns(c)),x=A.useRef(!1);return Hm(()=>{x.current=!0}),A.useCallback((v,y={})=>{if(Mt(x.current,Um),!x.current)return;if(typeof v=="number"){s.go(v);return}let E=us(v,JSON.parse(h),d,y.relative==="path");i==null&&r!=="/"&&(E.pathname=E.pathname==="/"?r:ea([r,E.pathname])),(y.replace?s.replace:s.push)(E,y.state,y)},[r,s,h,d,i])}A.createContext(null);function eu(i,{relative:r}={}){let{matches:s}=A.useContext(Yt),{pathname:c}=la(),d=JSON.stringify(ns(s));return A.useMemo(()=>us(i,JSON.parse(d),c,r==="path"),[i,d,c,r])}function sg(i,r){return qm(i,r)}function qm(i,r,s,c){Ne(Kl(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d}=A.useContext(Tt),{matches:h}=A.useContext(Yt),x=h[h.length-1],N=x?x.params:{},v=x?x.pathname:"/",y=x?x.pathnameBase:"/",E=x&&x.route;{let Y=E&&E.path||"";Bm(v,!E||Y.endsWith("*")||Y.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${v}" (under <Route path="${Y}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${Y}"> to <Route path="${Y==="/"?"*":`${Y}/*`}">.`)}let _=la(),H;if(r){let Y=typeof r=="string"?Zl(r):r;Ne(y==="/"||Y.pathname?.startsWith(y),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${Y.pathname}" was given in the \`location\` prop.`),H=Y}else H=_;let Z=H.pathname||"/",q=Z;if(y!=="/"){let Y=y.replace(/^\//,"").split("/");q="/"+Z.replace(/^\//,"").split("/").slice(Y.length).join("/")}let G=Om(i,{pathname:q});Mt(E||G!=null,`No routes matched location "${H.pathname}${H.search}${H.hash}" `),Mt(G==null||G[G.length-1].route.element!==void 0||G[G.length-1].route.Component!==void 0||G[G.length-1].route.lazy!==void 0,`Matched leaf route at location "${H.pathname}${H.search}${H.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let U=hg(G&&G.map(Y=>Object.assign({},Y,{params:Object.assign({},N,Y.params),pathname:ea([y,d.encodeLocation?d.encodeLocation(Y.pathname).pathname:Y.pathname]),pathnameBase:Y.pathnameBase==="/"?y:ea([y,d.encodeLocation?d.encodeLocation(Y.pathnameBase).pathname:Y.pathnameBase])})),h,s,c);return r&&U?A.createElement(In.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...H},navigationType:"POP"}},U):U}function fg(){let i=pg(),r=lg(i)?`${i.status} ${i.statusText}`:i instanceof Error?i.message:JSON.stringify(i),s=i instanceof Error?i.stack:null,c="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:c},h={padding:"2px 4px",backgroundColor:c},x=null;return console.error("Error handled by React Router default ErrorBoundary:",i),x=A.createElement(A.Fragment,null,A.createElement("p",null,"💿 Hey developer 👋"),A.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",A.createElement("code",{style:h},"ErrorBoundary")," or"," ",A.createElement("code",{style:h},"errorElement")," prop on your route.")),A.createElement(A.Fragment,null,A.createElement("h2",null,"Unexpected Application Error!"),A.createElement("h3",{style:{fontStyle:"italic"}},r),s?A.createElement("pre",{style:d},s):null,x)}var og=A.createElement(fg,null),dg=class extends A.Component{constructor(i){super(i),this.state={location:i.location,revalidation:i.revalidation,error:i.error}}static getDerivedStateFromError(i){return{error:i}}static getDerivedStateFromProps(i,r){return r.location!==i.location||r.revalidation!=="idle"&&i.revalidation==="idle"?{error:i.error,location:i.location,revalidation:i.revalidation}:{error:i.error!==void 0?i.error:r.error,location:r.location,revalidation:i.revalidation||r.revalidation}}componentDidCatch(i,r){console.error("React Router caught the following error during render",i,r)}render(){return this.state.error!==void 0?A.createElement(Yt.Provider,{value:this.props.routeContext},A.createElement(is.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function mg({routeContext:i,match:r,children:s}){let c=A.useContext(kl);return c&&c.static&&c.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=r.route.id),A.createElement(Yt.Provider,{value:i},s)}function hg(i,r=[],s=null,c=null){if(i==null){if(!s)return null;if(s.errors)i=s.matches;else if(r.length===0&&!s.initialized&&s.matches.length>0)i=s.matches;else return null}let d=i,h=s?.errors;if(h!=null){let v=d.findIndex(y=>y.route.id&&h?.[y.route.id]!==void 0);Ne(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(h).join(",")}`),d=d.slice(0,Math.min(d.length,v+1))}let x=!1,N=-1;if(s)for(let v=0;v<d.length;v++){let y=d[v];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(N=v),y.route.id){let{loaderData:E,errors:_}=s,H=y.route.loader&&!E.hasOwnProperty(y.route.id)&&(!_||_[y.route.id]===void 0);if(y.route.lazy||H){x=!0,N>=0?d=d.slice(0,N+1):d=[d[0]];break}}}return d.reduceRight((v,y,E)=>{let _,H=!1,Z=null,q=null;s&&(_=h&&y.route.id?h[y.route.id]:void 0,Z=y.route.errorElement||og,x&&(N<0&&E===0?(Bm("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),H=!0,q=null):N===E&&(H=!0,q=y.route.hydrateFallbackElement||null)));let G=r.concat(d.slice(0,E+1)),U=()=>{let Y;return _?Y=Z:H?Y=q:y.route.Component?Y=A.createElement(y.route.Component,null):y.route.element?Y=y.route.element:Y=v,A.createElement(mg,{match:y,routeContext:{outlet:v,matches:G,isDataRoute:s!=null},children:Y})};return s&&(y.route.ErrorBoundary||y.route.errorElement||E===0)?A.createElement(dg,{location:s.location,revalidation:s.revalidation,component:Z,error:_,children:U(),routeContext:{outlet:null,matches:G,isDataRoute:!0}}):U()},null)}function cs(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function yg(i){let r=A.useContext(kl);return Ne(r,cs(i)),r}function gg(i){let r=A.useContext(wi);return Ne(r,cs(i)),r}function vg(i){let r=A.useContext(Yt);return Ne(r,cs(i)),r}function rs(i){let r=vg(i),s=r.matches[r.matches.length-1];return Ne(s.route.id,`${i} can only be used on routes that contain a unique "id"`),s.route.id}function xg(){return rs("useRouteId")}function pg(){let i=A.useContext(is),r=gg("useRouteError"),s=rs("useRouteError");return i!==void 0?i:r.errors?.[s]}function bg(){let{router:i}=yg("useNavigate"),r=rs("useNavigate"),s=A.useRef(!1);return Hm(()=>{s.current=!0}),A.useCallback(async(d,h={})=>{Mt(s.current,Um),s.current&&(typeof d=="number"?i.navigate(d):await i.navigate(d,{fromRouteId:r,...h}))},[i,r])}var xm={};function Bm(i,r,s){!r&&!xm[i]&&(xm[i]=!0,Mt(!1,s))}A.memo(Sg);function Sg({routes:i,future:r,state:s}){return qm(i,void 0,s,r)}function Lm({to:i,replace:r,state:s,relative:c}){Ne(Kl(),"<Navigate> may be used only in the context of a <Router> component.");let{static:d}=A.useContext(Tt);Mt(!d,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:h}=A.useContext(Yt),{pathname:x}=la(),N=Ym(),v=us(i,ns(h),x,c==="path"),y=JSON.stringify(v);return A.useEffect(()=>{N(JSON.parse(y),{replace:r,state:s,relative:c})},[N,y,c,r,s]),null}function Ia(i){Ne(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Ng({basename:i="/",children:r=null,location:s,navigationType:c="POP",navigator:d,static:h=!1}){Ne(!Kl(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let x=i.replace(/^\/*/,"/"),N=A.useMemo(()=>({basename:x,navigator:d,static:h,future:{}}),[x,d,h]);typeof s=="string"&&(s=Zl(s));let{pathname:v="/",search:y="",hash:E="",state:_=null,key:H="default"}=s,Z=A.useMemo(()=>{let q=ta(v,x);return q==null?null:{location:{pathname:q,search:y,hash:E,state:_,key:H},navigationType:c}},[x,v,y,E,_,H,c]);return Mt(Z!=null,`<Router basename="${x}"> is not able to match the URL "${v}${y}${E}" because it does not start with the basename, so the <Router> won't render anything.`),Z==null?null:A.createElement(Tt.Provider,{value:N},A.createElement(In.Provider,{children:r,value:Z}))}function Xm({children:i,location:r}){return sg(ts(i),r)}function ts(i,r=[]){let s=[];return A.Children.forEach(i,(c,d)=>{if(!A.isValidElement(c))return;let h=[...r,d];if(c.type===A.Fragment){s.push.apply(s,ts(c.props.children,h));return}Ne(c.type===Ia,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ne(!c.props.index||!c.props.children,"An index route cannot have child routes.");let x={id:c.props.id||h.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(x.children=ts(c.props.children,h)),s.push(x)}),s}var vi="get",xi="application/x-www-form-urlencoded";function Mi(i){return i!=null&&typeof i.tagName=="string"}function jg(i){return Mi(i)&&i.tagName.toLowerCase()==="button"}function Eg(i){return Mi(i)&&i.tagName.toLowerCase()==="form"}function wg(i){return Mi(i)&&i.tagName.toLowerCase()==="input"}function Mg(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function Tg(i,r){return i.button===0&&(!r||r==="_self")&&!Mg(i)}var gi=null;function Ag(){if(gi===null)try{new FormData(document.createElement("form"),0),gi=!1}catch{gi=!0}return gi}var Dg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Fr(i){return i!=null&&!Dg.has(i)?(Mt(!1,`"${i}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${xi}"`),null):i}function Og(i,r){let s,c,d,h,x;if(Eg(i)){let N=i.getAttribute("action");c=N?ta(N,r):null,s=i.getAttribute("method")||vi,d=Fr(i.getAttribute("enctype"))||xi,h=new FormData(i)}else if(jg(i)||wg(i)&&(i.type==="submit"||i.type==="image")){let N=i.form;if(N==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=i.getAttribute("formaction")||N.getAttribute("action");if(c=v?ta(v,r):null,s=i.getAttribute("formmethod")||N.getAttribute("method")||vi,d=Fr(i.getAttribute("formenctype"))||Fr(N.getAttribute("enctype"))||xi,h=new FormData(N,i),!Ag()){let{name:y,type:E,value:_}=i;if(E==="image"){let H=y?`${y}.`:"";h.append(`${H}x`,"0"),h.append(`${H}y`,"0")}else y&&h.append(y,_)}}else{if(Mi(i))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=vi,c=null,d=xi,x=i}return h&&d==="text/plain"&&(x=h,h=void 0),{action:c,method:s.toLowerCase(),encType:d,formData:h,body:x}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function ss(i,r){if(i===!1||i===null||typeof i>"u")throw new Error(r)}function Rg(i,r,s){let c=typeof i=="string"?new URL(i,typeof window>"u"?"server://singlefetch/":window.location.origin):i;return c.pathname==="/"?c.pathname=`_root.${s}`:r&&ta(c.pathname,r)==="/"?c.pathname=`${r.replace(/\/$/,"")}/_root.${s}`:c.pathname=`${c.pathname.replace(/\/$/,"")}.${s}`,c}async function _g(i,r){if(i.id in r)return r[i.id];try{let s=await import(i.module);return r[i.id]=s,s}catch(s){return console.error(`Error loading route module \`${i.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function zg(i){return i==null?!1:i.href==null?i.rel==="preload"&&typeof i.imageSrcSet=="string"&&typeof i.imageSizes=="string":typeof i.rel=="string"&&typeof i.href=="string"}async function Cg(i,r,s){let c=await Promise.all(i.map(async d=>{let h=r.routes[d.route.id];if(h){let x=await _g(h,s);return x.links?x.links():[]}return[]}));return qg(c.flat(1).filter(zg).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function pm(i,r,s,c,d,h){let x=(v,y)=>s[y]?v.route.id!==s[y].route.id:!0,N=(v,y)=>s[y].pathname!==v.pathname||s[y].route.path?.endsWith("*")&&s[y].params["*"]!==v.params["*"];return h==="assets"?r.filter((v,y)=>x(v,y)||N(v,y)):h==="data"?r.filter((v,y)=>{let E=c.routes[v.route.id];if(!E||!E.hasLoader)return!1;if(x(v,y)||N(v,y))return!0;if(v.route.shouldRevalidate){let _=v.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:s[0]?.params||{},nextUrl:new URL(i,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof _=="boolean")return _}return!0}):[]}function Ug(i,r,{includeHydrateFallback:s}={}){return Hg(i.map(c=>{let d=r.routes[c.route.id];if(!d)return[];let h=[d.module];return d.clientActionModule&&(h=h.concat(d.clientActionModule)),d.clientLoaderModule&&(h=h.concat(d.clientLoaderModule)),s&&d.hydrateFallbackModule&&(h=h.concat(d.hydrateFallbackModule)),d.imports&&(h=h.concat(d.imports)),h}).flat(1))}function Hg(i){return[...new Set(i)]}function Yg(i){let r={},s=Object.keys(i).sort();for(let c of s)r[c]=i[c];return r}function qg(i,r){let s=new Set;return new Set(r),i.reduce((c,d)=>{let h=JSON.stringify(Yg(d));return s.has(h)||(s.add(h),c.push({key:h,link:d})),c},[])}function Gm(){let i=A.useContext(kl);return ss(i,"You must render this element inside a <DataRouterContext.Provider> element"),i}function Bg(){let i=A.useContext(wi);return ss(i,"You must render this element inside a <DataRouterStateContext.Provider> element"),i}var fs=A.createContext(void 0);fs.displayName="FrameworkContext";function Qm(){let i=A.useContext(fs);return ss(i,"You must render this element inside a <HydratedRouter> element"),i}function Lg(i,r){let s=A.useContext(fs),[c,d]=A.useState(!1),[h,x]=A.useState(!1),{onFocus:N,onBlur:v,onMouseEnter:y,onMouseLeave:E,onTouchStart:_}=r,H=A.useRef(null);A.useEffect(()=>{if(i==="render"&&x(!0),i==="viewport"){let G=Y=>{Y.forEach(I=>{x(I.isIntersecting)})},U=new IntersectionObserver(G,{threshold:.5});return H.current&&U.observe(H.current),()=>{U.disconnect()}}},[i]),A.useEffect(()=>{if(c){let G=setTimeout(()=>{x(!0)},100);return()=>{clearTimeout(G)}}},[c]);let Z=()=>{d(!0)},q=()=>{d(!1),x(!1)};return s?i!=="intent"?[h,H,{}]:[h,H,{onFocus:Jn(N,Z),onBlur:Jn(v,q),onMouseEnter:Jn(y,Z),onMouseLeave:Jn(E,q),onTouchStart:Jn(_,Z)}]:[!1,H,{}]}function Jn(i,r){return s=>{i&&i(s),s.defaultPrevented||r(s)}}function Xg({page:i,...r}){let{router:s}=Gm(),c=A.useMemo(()=>Om(s.routes,i,s.basename),[s.routes,i,s.basename]);return c?A.createElement(Qg,{page:i,matches:c,...r}):null}function Gg(i){let{manifest:r,routeModules:s}=Qm(),[c,d]=A.useState([]);return A.useEffect(()=>{let h=!1;return Cg(i,r,s).then(x=>{h||d(x)}),()=>{h=!0}},[i,r,s]),c}function Qg({page:i,matches:r,...s}){let c=la(),{manifest:d,routeModules:h}=Qm(),{basename:x}=Gm(),{loaderData:N,matches:v}=Bg(),y=A.useMemo(()=>pm(i,r,v,d,c,"data"),[i,r,v,d,c]),E=A.useMemo(()=>pm(i,r,v,d,c,"assets"),[i,r,v,d,c]),_=A.useMemo(()=>{if(i===c.pathname+c.search+c.hash)return[];let q=new Set,G=!1;if(r.forEach(Y=>{let I=d.routes[Y.route.id];!I||!I.hasLoader||(!y.some(ee=>ee.route.id===Y.route.id)&&Y.route.id in N&&h[Y.route.id]?.shouldRevalidate||I.hasClientLoader?G=!0:q.add(Y.route.id))}),q.size===0)return[];let U=Rg(i,x,"data");return G&&q.size>0&&U.searchParams.set("_routes",r.filter(Y=>q.has(Y.route.id)).map(Y=>Y.route.id).join(",")),[U.pathname+U.search]},[x,N,c,d,y,r,i,h]),H=A.useMemo(()=>Ug(E,d),[E,d]),Z=Gg(E);return A.createElement(A.Fragment,null,_.map(q=>A.createElement("link",{key:q,rel:"prefetch",as:"fetch",href:q,...s})),H.map(q=>A.createElement("link",{key:q,rel:"modulepreload",href:q,...s})),Z.map(({key:q,link:G})=>A.createElement("link",{key:q,...G})))}function Vg(...i){return r=>{i.forEach(s=>{typeof s=="function"?s(r):s!=null&&(s.current=r)})}}var Vm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Vm&&(window.__reactRouterVersion="7.7.1")}catch{}function Zg({basename:i,children:r,window:s}){let c=A.useRef();c.current==null&&(c.current=Cy({window:s,v5Compat:!0}));let d=c.current,[h,x]=A.useState({action:d.action,location:d.location}),N=A.useCallback(v=>{A.startTransition(()=>x(v))},[x]);return A.useLayoutEffect(()=>d.listen(N),[d,N]),A.createElement(Ng,{basename:i,children:r,location:h.location,navigationType:h.action,navigator:d})}var Zm=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,bi=A.forwardRef(function({onClick:r,discover:s="render",prefetch:c="none",relative:d,reloadDocument:h,replace:x,state:N,target:v,to:y,preventScrollReset:E,viewTransition:_,...H},Z){let{basename:q}=A.useContext(Tt),G=typeof y=="string"&&Zm.test(y),U,Y=!1;if(typeof y=="string"&&G&&(U=y,Vm))try{let we=new URL(window.location.href),at=y.startsWith("//")?new URL(we.protocol+y):new URL(y),mt=ta(at.pathname,q);at.origin===we.origin&&mt!=null?y=mt+at.search+at.hash:Y=!0}catch{Mt(!1,`<Link to="${y}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let I=cg(y,{relative:d}),[ee,je,F]=Lg(c,H),Ue=Wg(y,{replace:x,state:N,target:v,preventScrollReset:E,relative:d,viewTransition:_});function _e(we){r&&r(we),we.defaultPrevented||Ue(we)}let He=A.createElement("a",{...H,...F,href:U||I,onClick:Y||h?r:_e,ref:Vg(Z,je),target:v,"data-discover":!G&&s==="render"?"true":void 0});return ee&&!G?A.createElement(A.Fragment,null,He,A.createElement(Xg,{page:I})):He});bi.displayName="Link";var kg=A.forwardRef(function({"aria-current":r="page",caseSensitive:s=!1,className:c="",end:d=!1,style:h,to:x,viewTransition:N,children:v,...y},E){let _=eu(x,{relative:y.relative}),H=la(),Z=A.useContext(wi),{navigator:q,basename:G}=A.useContext(Tt),U=Z!=null&&ev(_)&&N===!0,Y=q.encodeLocation?q.encodeLocation(_).pathname:_.pathname,I=H.pathname,ee=Z&&Z.navigation&&Z.navigation.location?Z.navigation.location.pathname:null;s||(I=I.toLowerCase(),ee=ee?ee.toLowerCase():null,Y=Y.toLowerCase()),ee&&G&&(ee=ta(ee,G)||ee);const je=Y!=="/"&&Y.endsWith("/")?Y.length-1:Y.length;let F=I===Y||!d&&I.startsWith(Y)&&I.charAt(je)==="/",Ue=ee!=null&&(ee===Y||!d&&ee.startsWith(Y)&&ee.charAt(Y.length)==="/"),_e={isActive:F,isPending:Ue,isTransitioning:U},He=F?r:void 0,we;typeof c=="function"?we=c(_e):we=[c,F?"active":null,Ue?"pending":null,U?"transitioning":null].filter(Boolean).join(" ");let at=typeof h=="function"?h(_e):h;return A.createElement(bi,{...y,"aria-current":He,className:we,ref:E,style:at,to:x,viewTransition:N},typeof v=="function"?v(_e):v)});kg.displayName="NavLink";var Kg=A.forwardRef(({discover:i="render",fetcherKey:r,navigate:s,reloadDocument:c,replace:d,state:h,method:x=vi,action:N,onSubmit:v,relative:y,preventScrollReset:E,viewTransition:_,...H},Z)=>{let q=Pg(),G=Ig(N,{relative:y}),U=x.toLowerCase()==="get"?"get":"post",Y=typeof N=="string"&&Zm.test(N),I=ee=>{if(v&&v(ee),ee.defaultPrevented)return;ee.preventDefault();let je=ee.nativeEvent.submitter,F=je?.getAttribute("formmethod")||x;q(je||ee.currentTarget,{fetcherKey:r,method:F,navigate:s,replace:d,state:h,relative:y,preventScrollReset:E,viewTransition:_})};return A.createElement("form",{ref:Z,method:U,action:G,onSubmit:c?v:I,...H,"data-discover":!Y&&i==="render"?"true":void 0})});Kg.displayName="Form";function Jg(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function km(i){let r=A.useContext(kl);return Ne(r,Jg(i)),r}function Wg(i,{target:r,replace:s,state:c,preventScrollReset:d,relative:h,viewTransition:x}={}){let N=Ym(),v=la(),y=eu(i,{relative:h});return A.useCallback(E=>{if(Tg(E,r)){E.preventDefault();let _=s!==void 0?s:$n(v)===$n(y);N(i,{replace:_,state:c,preventScrollReset:d,relative:h,viewTransition:x})}},[v,N,y,s,c,r,i,d,h,x])}var $g=0,Fg=()=>`__${String(++$g)}__`;function Pg(){let{router:i}=km("useSubmit"),{basename:r}=A.useContext(Tt),s=xg();return A.useCallback(async(c,d={})=>{let{action:h,method:x,encType:N,formData:v,body:y}=Og(c,r);if(d.navigate===!1){let E=d.fetcherKey||Fg();await i.fetch(E,s,d.action||h,{preventScrollReset:d.preventScrollReset,formData:v,body:y,formMethod:d.method||x,formEncType:d.encType||N,flushSync:d.flushSync})}else await i.navigate(d.action||h,{preventScrollReset:d.preventScrollReset,formData:v,body:y,formMethod:d.method||x,formEncType:d.encType||N,replace:d.replace,state:d.state,fromRouteId:s,flushSync:d.flushSync,viewTransition:d.viewTransition})},[i,r,s])}function Ig(i,{relative:r}={}){let{basename:s}=A.useContext(Tt),c=A.useContext(Yt);Ne(c,"useFormAction must be used inside a RouteContext");let[d]=c.matches.slice(-1),h={...eu(i||".",{relative:r})},x=la();if(i==null){h.search=x.search;let N=new URLSearchParams(h.search),v=N.getAll("index");if(v.some(E=>E==="")){N.delete("index"),v.filter(_=>_).forEach(_=>N.append("index",_));let E=N.toString();h.search=E?`?${E}`:""}}return(!i||i===".")&&d.route.index&&(h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(h.pathname=h.pathname==="/"?s:ea([s,h.pathname])),$n(h)}function ev(i,{relative:r}={}){let s=A.useContext(Cm);Ne(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=km("useViewTransitionState"),d=eu(i,{relative:r});if(!s.isTransitioning)return!1;let h=ta(s.currentLocation.pathname,c)||s.currentLocation.pathname,x=ta(s.nextLocation.pathname,c)||s.nextLocation.pathname;return pi(d.pathname,x)!=null||pi(d.pathname,h)!=null}const bm=[{id:"1",email:"<EMAIL>",password:"admin123",name:"Dr. Administrateur",role:"ADMIN",createdAt:new Date,updatedAt:new Date}],Fn=[{id:"1",firstName:"Jean",lastName:"Dupont",phone:"**********",email:"<EMAIL>",dateOfBirth:new Date("1980-05-15"),address:"123 Rue de la Paix, 75001 Paris",medicalHistory:"Aucun antécédent particulier",allergies:"Aucune allergie connue",notes:"Patient régulier, très ponctuel",createdAt:new Date,updatedAt:new Date},{id:"2",firstName:"Marie",lastName:"Martin",phone:"**********",email:"<EMAIL>",dateOfBirth:new Date("1975-12-03"),address:"456 Avenue des Champs, 75008 Paris",medicalHistory:"Hypertension artérielle",allergies:"Allergie à la pénicilline",notes:"Préfère les rendez-vous le matin",createdAt:new Date,updatedAt:new Date}],Si=new Date;Si.setDate(Si.getDate()+1);const Pr=[{id:"1",patientId:"1",date:Si,time:"09:00",duration:30,type:"CONSULTATION",status:"SCHEDULED",notes:"Contrôle de routine",createdAt:new Date,updatedAt:new Date,patient:Fn[0]},{id:"2",patientId:"2",date:Si,time:"14:30",duration:45,type:"TRAITEMENT",status:"SCHEDULED",notes:"Détartrage",createdAt:new Date,updatedAt:new Date,patient:Fn[1]}],Sm=[{id:"1",patientId:"1",date:new Date,diagnosis:"Carie dentaire sur molaire supérieure droite",treatment:"Obturation composite",prescription:"Bain de bouche antiseptique 2x/jour pendant 1 semaine",notes:"Traitement réalisé sans complications",cost:85,paid:!0,createdAt:new Date,updatedAt:new Date,patient:Fn[0]}],Oa={user:{findUnique:async({where:i})=>bm.find(r=>r.email===i.email)||null,count:async()=>bm.length},patient:{count:async()=>Fn.length,findMany:async()=>Fn},appointment:{count:async({where:i})=>i?.date?Pr.filter(r=>r.date>=i.date.gte&&r.date<=i.date.lte).length:Pr.length,findMany:async({where:i,take:r})=>{let s=Pr;return i?.date?.gte&&(s=s.filter(c=>c.date>=i.date.gte)),i?.status&&(s=s.filter(c=>c.status===i.status)),s.slice(0,r||s.length)}},consultation:{count:async({where:i})=>i?.date?Sm.filter(r=>r.date>=i.date.gte&&r.date<=i.date.lte).length:Sm.length}},Km=A.createContext(void 0),Ti=()=>{const i=A.useContext(Km);if(i===void 0)throw new Error("useAuth must be used within an AuthProvider");return i},tv=({children:i})=>{const[r,s]=A.useState(null),[c,d]=A.useState(!0);A.useEffect(()=>{const v=localStorage.getItem("user");v&&s(JSON.parse(v)),d(!1)},[]);const N={user:r,login:async(v,y)=>{try{d(!0);const E=await Oa.user.findUnique({where:{email:v}});if(E&&E.password===y){const _={id:E.id,email:E.email,name:E.name,role:E.role,createdAt:E.createdAt,updatedAt:E.updatedAt};return s(_),localStorage.setItem("user",JSON.stringify(_)),!0}return!1}catch(E){return console.error("Erreur de connexion:",E),!1}finally{d(!1)}},logout:()=>{s(null),localStorage.removeItem("user")},isLoading:c};return o.jsx(Km.Provider,{value:N,children:i})};/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const av=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),lv=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,s,c)=>c?c.toUpperCase():s.toLowerCase()),Nm=i=>{const r=lv(i);return r.charAt(0).toUpperCase()+r.slice(1)},Jm=(...i)=>i.filter((r,s,c)=>!!r&&r.trim()!==""&&c.indexOf(r)===s).join(" ").trim(),nv=i=>{for(const r in i)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var uv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iv=A.forwardRef(({color:i="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:d="",children:h,iconNode:x,...N},v)=>A.createElement("svg",{ref:v,...uv,width:r,height:r,stroke:i,strokeWidth:c?Number(s)*24/Number(r):s,className:Jm("lucide",d),...!h&&!nv(N)&&{"aria-hidden":"true"},...N},[...x.map(([y,E])=>A.createElement(y,E)),...Array.isArray(h)?h:[h]]));/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=(i,r)=>{const s=A.forwardRef(({className:c,...d},h)=>A.createElement(iv,{ref:h,iconNode:r,className:Jm(`lucide-${av(Nm(i))}`,`lucide-${i}`,c),...d}));return s.displayName=Nm(i),s};/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cv=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Ra=pe("calendar",cv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rv=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],sv=pe("chevron-left",rv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fv=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Wn=pe("chevron-right",fv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ov=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],dv=pe("circle-alert",ov);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mv=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],hv=pe("clock",mv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yv=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],gv=pe("dollar-sign",yv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vv=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],xv=pe("eye",vv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pv=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],bv=pe("file-text",pv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sv=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Nv=pe("funnel",Sv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jv=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ev=pe("house",jv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wv=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],Mv=pe("lock",wv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tv=[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]],Av=pe("log-in",Tv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dv=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],Ov=pe("log-out",Dv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rv=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],as=pe("mail",Rv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _v=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],zv=pe("map-pin",_v);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cv=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Uv=pe("menu",Cv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hv=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],el=pe("phone",Hv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yv=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Vl=pe("plus",Yv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qv=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Pn=pe("search",qv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bv=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Wm=pe("square-pen",Bv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lv=[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]],Xv=pe("trash-2",Lv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gv=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Qv=pe("trending-up",Gv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vv=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Ni=pe("user",Vv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zv=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],tl=pe("users",Zv);/**
 * @license lucide-react v0.528.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kv=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Kv=pe("x",kv),Jv=()=>{const[i,r]=A.useState(""),[s,c]=A.useState(""),[d,h]=A.useState(""),[x,N]=A.useState(!1),{login:v}=Ti(),y=async E=>{E.preventDefault(),h(""),N(!0);try{await v(i,s)||h("Email ou mot de passe incorrect")}catch{h("Une erreur est survenue lors de la connexion")}finally{N(!1)}};return o.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50 flex items-center justify-center p-4",children:o.jsx("div",{className:"max-w-md w-full",children:o.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100",children:[o.jsxs("div",{className:"text-center mb-8",children:[o.jsx("div",{className:"mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg",children:o.jsx(Ni,{className:"w-8 h-8 text-white"})}),o.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Cabinet Dentaire"}),o.jsx("p",{className:"text-gray-600 mt-2",children:"Connectez-vous à votre espace professionnel"})]}),o.jsxs("form",{onSubmit:y,className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-900 mb-2",children:"Adresse email"}),o.jsxs("div",{className:"relative",children:[o.jsx(Ni,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),o.jsx("input",{id:"email",type:"email",value:i,onChange:E=>r(E.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-gray-50 focus:bg-white",placeholder:"<EMAIL>",required:!0})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-900 mb-2",children:"Mot de passe"}),o.jsxs("div",{className:"relative",children:[o.jsx(Mv,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),o.jsx("input",{id:"password",type:"password",value:s,onChange:E=>c(E.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-gray-50 focus:bg-white",placeholder:"••••••••",required:!0})]})]}),d&&o.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl flex items-center",children:[o.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full mr-3"}),d]}),o.jsx("button",{type:"submit",disabled:x,className:"w-full bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-3 px-4 rounded-xl hover:from-blue-700 hover:to-cyan-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center font-semibold shadow-lg transition-all",children:x?o.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):o.jsxs(o.Fragment,{children:[o.jsx(Av,{className:"w-5 h-5 mr-2"}),"Se connecter"]})})]}),o.jsx("div",{className:"mt-8 text-center",children:o.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4",children:[o.jsx("p",{className:"text-sm font-medium text-blue-900 mb-1",children:"Compte de démonstration"}),o.jsx("p",{className:"text-xs text-blue-700",children:"<EMAIL> / admin123"})]})})]})})})},$m=6048e5,Wv=864e5,jm=Symbol.for("constructDateFrom");function aa(i,r){return typeof i=="function"?i(r):i&&typeof i=="object"&&jm in i?i[jm](r):i instanceof Date?new i.constructor(r):new Date(r)}function $e(i,r){return aa(r||i,i)}function Ir(i,r,s){const c=$e(i,s?.in);return isNaN(r)?aa(i,NaN):(r&&c.setDate(c.getDate()+r),c)}let $v={};function Ai(){return $v}function al(i,r){const s=Ai(),c=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??s.weekStartsOn??s.locale?.options?.weekStartsOn??0,d=$e(i,r?.in),h=d.getDay(),x=(h<c?7:0)+h-c;return d.setDate(d.getDate()-x),d.setHours(0,0,0,0),d}function ji(i,r){return al(i,{...r,weekStartsOn:1})}function Fm(i,r){const s=$e(i,r?.in),c=s.getFullYear(),d=aa(s,0);d.setFullYear(c+1,0,4),d.setHours(0,0,0,0);const h=ji(d),x=aa(s,0);x.setFullYear(c,0,4),x.setHours(0,0,0,0);const N=ji(x);return s.getTime()>=h.getTime()?c+1:s.getTime()>=N.getTime()?c:c-1}function Em(i){const r=$e(i),s=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return s.setUTCFullYear(r.getFullYear()),+i-+s}function Pm(i,...r){const s=aa.bind(null,r.find(c=>typeof c=="object"));return r.map(s)}function Ei(i,r){const s=$e(i,r?.in);return s.setHours(0,0,0,0),s}function Fv(i,r,s){const[c,d]=Pm(s?.in,i,r),h=Ei(c),x=Ei(d),N=+h-Em(h),v=+x-Em(x);return Math.round((N-v)/Wv)}function Pv(i,r){const s=Fm(i,r),c=aa(i,0);return c.setFullYear(s,0,4),c.setHours(0,0,0,0),ji(c)}function wm(i,r,s){const[c,d]=Pm(s?.in,i,r);return+Ei(c)==+Ei(d)}function Iv(i){return i instanceof Date||typeof i=="object"&&Object.prototype.toString.call(i)==="[object Date]"}function e1(i){return!(!Iv(i)&&typeof i!="number"||isNaN(+$e(i)))}function t1(i,r){const s=$e(i,r?.in),c=s.getMonth();return s.setFullYear(s.getFullYear(),c+1,0),s.setHours(23,59,59,999),s}function a1(i,r){const s=$e(i,r?.in);return s.setDate(1),s.setHours(0,0,0,0),s}function l1(i,r){const s=$e(i,r?.in);return s.setFullYear(s.getFullYear(),0,1),s.setHours(0,0,0,0),s}function n1(i,r){const s=r?.weekStartsOn,c=$e(i,r?.in),d=c.getDay(),h=(d<s?-7:0)+6-(d-s);return c.setDate(c.getDate()+h),c.setHours(23,59,59,999),c}const u1={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},i1=(i,r,s)=>{let c;const d=u1[i];return typeof d=="string"?c=d:r===1?c=d.one:c=d.other.replace("{{count}}",r.toString()),s?.addSuffix?s.comparison&&s.comparison>0?"in "+c:c+" ago":c};function Ql(i){return(r={})=>{const s=r.width?String(r.width):i.defaultWidth;return i.formats[s]||i.formats[i.defaultWidth]}}const c1={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},r1={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},s1={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},f1={date:Ql({formats:c1,defaultWidth:"full"}),time:Ql({formats:r1,defaultWidth:"full"}),dateTime:Ql({formats:s1,defaultWidth:"full"})},o1={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},d1=(i,r,s,c)=>o1[i];function Ct(i){return(r,s)=>{const c=s?.context?String(s.context):"standalone";let d;if(c==="formatting"&&i.formattingValues){const x=i.defaultFormattingWidth||i.defaultWidth,N=s?.width?String(s.width):x;d=i.formattingValues[N]||i.formattingValues[x]}else{const x=i.defaultWidth,N=s?.width?String(s.width):i.defaultWidth;d=i.values[N]||i.values[x]}const h=i.argumentCallback?i.argumentCallback(r):r;return d[h]}}const m1={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},h1={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},y1={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},g1={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},v1={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},x1={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},p1=(i,r)=>{const s=Number(i),c=s%100;if(c>20||c<10)switch(c%10){case 1:return s+"st";case 2:return s+"nd";case 3:return s+"rd"}return s+"th"},b1={ordinalNumber:p1,era:Ct({values:m1,defaultWidth:"wide"}),quarter:Ct({values:h1,defaultWidth:"wide",argumentCallback:i=>i-1}),month:Ct({values:y1,defaultWidth:"wide"}),day:Ct({values:g1,defaultWidth:"wide"}),dayPeriod:Ct({values:v1,defaultWidth:"wide",formattingValues:x1,defaultFormattingWidth:"wide"})};function Ut(i){return(r,s={})=>{const c=s.width,d=c&&i.matchPatterns[c]||i.matchPatterns[i.defaultMatchWidth],h=r.match(d);if(!h)return null;const x=h[0],N=c&&i.parsePatterns[c]||i.parsePatterns[i.defaultParseWidth],v=Array.isArray(N)?N1(N,_=>_.test(x)):S1(N,_=>_.test(x));let y;y=i.valueCallback?i.valueCallback(v):v,y=s.valueCallback?s.valueCallback(y):y;const E=r.slice(x.length);return{value:y,rest:E}}}function S1(i,r){for(const s in i)if(Object.prototype.hasOwnProperty.call(i,s)&&r(i[s]))return s}function N1(i,r){for(let s=0;s<i.length;s++)if(r(i[s]))return s}function Im(i){return(r,s={})=>{const c=r.match(i.matchPattern);if(!c)return null;const d=c[0],h=r.match(i.parsePattern);if(!h)return null;let x=i.valueCallback?i.valueCallback(h[0]):h[0];x=s.valueCallback?s.valueCallback(x):x;const N=r.slice(d.length);return{value:x,rest:N}}}const j1=/^(\d+)(th|st|nd|rd)?/i,E1=/\d+/i,w1={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},M1={any:[/^b/i,/^(a|c)/i]},T1={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},A1={any:[/1/i,/2/i,/3/i,/4/i]},D1={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},O1={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},R1={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},_1={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},z1={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},C1={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},U1={ordinalNumber:Im({matchPattern:j1,parsePattern:E1,valueCallback:i=>parseInt(i,10)}),era:Ut({matchPatterns:w1,defaultMatchWidth:"wide",parsePatterns:M1,defaultParseWidth:"any"}),quarter:Ut({matchPatterns:T1,defaultMatchWidth:"wide",parsePatterns:A1,defaultParseWidth:"any",valueCallback:i=>i+1}),month:Ut({matchPatterns:D1,defaultMatchWidth:"wide",parsePatterns:O1,defaultParseWidth:"any"}),day:Ut({matchPatterns:R1,defaultMatchWidth:"wide",parsePatterns:_1,defaultParseWidth:"any"}),dayPeriod:Ut({matchPatterns:z1,defaultMatchWidth:"any",parsePatterns:C1,defaultParseWidth:"any"})},H1={code:"en-US",formatDistance:i1,formatLong:f1,formatRelative:d1,localize:b1,match:U1,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Y1(i,r){const s=$e(i,r?.in);return Fv(s,l1(s))+1}function q1(i,r){const s=$e(i,r?.in),c=+ji(s)-+Pv(s);return Math.round(c/$m)+1}function eh(i,r){const s=$e(i,r?.in),c=s.getFullYear(),d=Ai(),h=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??d.firstWeekContainsDate??d.locale?.options?.firstWeekContainsDate??1,x=aa(r?.in||i,0);x.setFullYear(c+1,0,h),x.setHours(0,0,0,0);const N=al(x,r),v=aa(r?.in||i,0);v.setFullYear(c,0,h),v.setHours(0,0,0,0);const y=al(v,r);return+s>=+N?c+1:+s>=+y?c:c-1}function B1(i,r){const s=Ai(),c=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1,d=eh(i,r),h=aa(r?.in||i,0);return h.setFullYear(d,0,c),h.setHours(0,0,0,0),al(h,r)}function L1(i,r){const s=$e(i,r?.in),c=+al(s,r)-+B1(s,r);return Math.round(c/$m)+1}function de(i,r){const s=i<0?"-":"",c=Math.abs(i).toString().padStart(r,"0");return s+c}const Da={y(i,r){const s=i.getFullYear(),c=s>0?s:1-s;return de(r==="yy"?c%100:c,r.length)},M(i,r){const s=i.getMonth();return r==="M"?String(s+1):de(s+1,2)},d(i,r){return de(i.getDate(),r.length)},a(i,r){const s=i.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return s.toUpperCase();case"aaa":return s;case"aaaaa":return s[0];case"aaaa":default:return s==="am"?"a.m.":"p.m."}},h(i,r){return de(i.getHours()%12||12,r.length)},H(i,r){return de(i.getHours(),r.length)},m(i,r){return de(i.getMinutes(),r.length)},s(i,r){return de(i.getSeconds(),r.length)},S(i,r){const s=r.length,c=i.getMilliseconds(),d=Math.trunc(c*Math.pow(10,s-3));return de(d,r.length)}},Gl={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Mm={G:function(i,r,s){const c=i.getFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return s.era(c,{width:"abbreviated"});case"GGGGG":return s.era(c,{width:"narrow"});case"GGGG":default:return s.era(c,{width:"wide"})}},y:function(i,r,s){if(r==="yo"){const c=i.getFullYear(),d=c>0?c:1-c;return s.ordinalNumber(d,{unit:"year"})}return Da.y(i,r)},Y:function(i,r,s,c){const d=eh(i,c),h=d>0?d:1-d;if(r==="YY"){const x=h%100;return de(x,2)}return r==="Yo"?s.ordinalNumber(h,{unit:"year"}):de(h,r.length)},R:function(i,r){const s=Fm(i);return de(s,r.length)},u:function(i,r){const s=i.getFullYear();return de(s,r.length)},Q:function(i,r,s){const c=Math.ceil((i.getMonth()+1)/3);switch(r){case"Q":return String(c);case"QQ":return de(c,2);case"Qo":return s.ordinalNumber(c,{unit:"quarter"});case"QQQ":return s.quarter(c,{width:"abbreviated",context:"formatting"});case"QQQQQ":return s.quarter(c,{width:"narrow",context:"formatting"});case"QQQQ":default:return s.quarter(c,{width:"wide",context:"formatting"})}},q:function(i,r,s){const c=Math.ceil((i.getMonth()+1)/3);switch(r){case"q":return String(c);case"qq":return de(c,2);case"qo":return s.ordinalNumber(c,{unit:"quarter"});case"qqq":return s.quarter(c,{width:"abbreviated",context:"standalone"});case"qqqqq":return s.quarter(c,{width:"narrow",context:"standalone"});case"qqqq":default:return s.quarter(c,{width:"wide",context:"standalone"})}},M:function(i,r,s){const c=i.getMonth();switch(r){case"M":case"MM":return Da.M(i,r);case"Mo":return s.ordinalNumber(c+1,{unit:"month"});case"MMM":return s.month(c,{width:"abbreviated",context:"formatting"});case"MMMMM":return s.month(c,{width:"narrow",context:"formatting"});case"MMMM":default:return s.month(c,{width:"wide",context:"formatting"})}},L:function(i,r,s){const c=i.getMonth();switch(r){case"L":return String(c+1);case"LL":return de(c+1,2);case"Lo":return s.ordinalNumber(c+1,{unit:"month"});case"LLL":return s.month(c,{width:"abbreviated",context:"standalone"});case"LLLLL":return s.month(c,{width:"narrow",context:"standalone"});case"LLLL":default:return s.month(c,{width:"wide",context:"standalone"})}},w:function(i,r,s,c){const d=L1(i,c);return r==="wo"?s.ordinalNumber(d,{unit:"week"}):de(d,r.length)},I:function(i,r,s){const c=q1(i);return r==="Io"?s.ordinalNumber(c,{unit:"week"}):de(c,r.length)},d:function(i,r,s){return r==="do"?s.ordinalNumber(i.getDate(),{unit:"date"}):Da.d(i,r)},D:function(i,r,s){const c=Y1(i);return r==="Do"?s.ordinalNumber(c,{unit:"dayOfYear"}):de(c,r.length)},E:function(i,r,s){const c=i.getDay();switch(r){case"E":case"EE":case"EEE":return s.day(c,{width:"abbreviated",context:"formatting"});case"EEEEE":return s.day(c,{width:"narrow",context:"formatting"});case"EEEEEE":return s.day(c,{width:"short",context:"formatting"});case"EEEE":default:return s.day(c,{width:"wide",context:"formatting"})}},e:function(i,r,s,c){const d=i.getDay(),h=(d-c.weekStartsOn+8)%7||7;switch(r){case"e":return String(h);case"ee":return de(h,2);case"eo":return s.ordinalNumber(h,{unit:"day"});case"eee":return s.day(d,{width:"abbreviated",context:"formatting"});case"eeeee":return s.day(d,{width:"narrow",context:"formatting"});case"eeeeee":return s.day(d,{width:"short",context:"formatting"});case"eeee":default:return s.day(d,{width:"wide",context:"formatting"})}},c:function(i,r,s,c){const d=i.getDay(),h=(d-c.weekStartsOn+8)%7||7;switch(r){case"c":return String(h);case"cc":return de(h,r.length);case"co":return s.ordinalNumber(h,{unit:"day"});case"ccc":return s.day(d,{width:"abbreviated",context:"standalone"});case"ccccc":return s.day(d,{width:"narrow",context:"standalone"});case"cccccc":return s.day(d,{width:"short",context:"standalone"});case"cccc":default:return s.day(d,{width:"wide",context:"standalone"})}},i:function(i,r,s){const c=i.getDay(),d=c===0?7:c;switch(r){case"i":return String(d);case"ii":return de(d,r.length);case"io":return s.ordinalNumber(d,{unit:"day"});case"iii":return s.day(c,{width:"abbreviated",context:"formatting"});case"iiiii":return s.day(c,{width:"narrow",context:"formatting"});case"iiiiii":return s.day(c,{width:"short",context:"formatting"});case"iiii":default:return s.day(c,{width:"wide",context:"formatting"})}},a:function(i,r,s){const d=i.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return s.dayPeriod(d,{width:"abbreviated",context:"formatting"});case"aaa":return s.dayPeriod(d,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return s.dayPeriod(d,{width:"narrow",context:"formatting"});case"aaaa":default:return s.dayPeriod(d,{width:"wide",context:"formatting"})}},b:function(i,r,s){const c=i.getHours();let d;switch(c===12?d=Gl.noon:c===0?d=Gl.midnight:d=c/12>=1?"pm":"am",r){case"b":case"bb":return s.dayPeriod(d,{width:"abbreviated",context:"formatting"});case"bbb":return s.dayPeriod(d,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return s.dayPeriod(d,{width:"narrow",context:"formatting"});case"bbbb":default:return s.dayPeriod(d,{width:"wide",context:"formatting"})}},B:function(i,r,s){const c=i.getHours();let d;switch(c>=17?d=Gl.evening:c>=12?d=Gl.afternoon:c>=4?d=Gl.morning:d=Gl.night,r){case"B":case"BB":case"BBB":return s.dayPeriod(d,{width:"abbreviated",context:"formatting"});case"BBBBB":return s.dayPeriod(d,{width:"narrow",context:"formatting"});case"BBBB":default:return s.dayPeriod(d,{width:"wide",context:"formatting"})}},h:function(i,r,s){if(r==="ho"){let c=i.getHours()%12;return c===0&&(c=12),s.ordinalNumber(c,{unit:"hour"})}return Da.h(i,r)},H:function(i,r,s){return r==="Ho"?s.ordinalNumber(i.getHours(),{unit:"hour"}):Da.H(i,r)},K:function(i,r,s){const c=i.getHours()%12;return r==="Ko"?s.ordinalNumber(c,{unit:"hour"}):de(c,r.length)},k:function(i,r,s){let c=i.getHours();return c===0&&(c=24),r==="ko"?s.ordinalNumber(c,{unit:"hour"}):de(c,r.length)},m:function(i,r,s){return r==="mo"?s.ordinalNumber(i.getMinutes(),{unit:"minute"}):Da.m(i,r)},s:function(i,r,s){return r==="so"?s.ordinalNumber(i.getSeconds(),{unit:"second"}):Da.s(i,r)},S:function(i,r){return Da.S(i,r)},X:function(i,r,s){const c=i.getTimezoneOffset();if(c===0)return"Z";switch(r){case"X":return Am(c);case"XXXX":case"XX":return Pa(c);case"XXXXX":case"XXX":default:return Pa(c,":")}},x:function(i,r,s){const c=i.getTimezoneOffset();switch(r){case"x":return Am(c);case"xxxx":case"xx":return Pa(c);case"xxxxx":case"xxx":default:return Pa(c,":")}},O:function(i,r,s){const c=i.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+Tm(c,":");case"OOOO":default:return"GMT"+Pa(c,":")}},z:function(i,r,s){const c=i.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+Tm(c,":");case"zzzz":default:return"GMT"+Pa(c,":")}},t:function(i,r,s){const c=Math.trunc(+i/1e3);return de(c,r.length)},T:function(i,r,s){return de(+i,r.length)}};function Tm(i,r=""){const s=i>0?"-":"+",c=Math.abs(i),d=Math.trunc(c/60),h=c%60;return h===0?s+String(d):s+String(d)+r+de(h,2)}function Am(i,r){return i%60===0?(i>0?"-":"+")+de(Math.abs(i)/60,2):Pa(i,r)}function Pa(i,r=""){const s=i>0?"-":"+",c=Math.abs(i),d=de(Math.trunc(c/60),2),h=de(c%60,2);return s+d+r+h}const Dm=(i,r)=>{switch(i){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},th=(i,r)=>{switch(i){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},X1=(i,r)=>{const s=i.match(/(P+)(p+)?/)||[],c=s[1],d=s[2];if(!d)return Dm(i,r);let h;switch(c){case"P":h=r.dateTime({width:"short"});break;case"PP":h=r.dateTime({width:"medium"});break;case"PPP":h=r.dateTime({width:"long"});break;case"PPPP":default:h=r.dateTime({width:"full"});break}return h.replace("{{date}}",Dm(c,r)).replace("{{time}}",th(d,r))},G1={p:th,P:X1},Q1=/^D+$/,V1=/^Y+$/,Z1=["D","DD","YY","YYYY"];function k1(i){return Q1.test(i)}function K1(i){return V1.test(i)}function J1(i,r,s){const c=W1(i,r,s);if(console.warn(c),Z1.includes(i))throw new RangeError(c)}function W1(i,r,s){const c=i[0]==="Y"?"years":"days of the month";return`Use \`${i.toLowerCase()}\` instead of \`${i}\` (in \`${r}\`) for formatting ${c} to the input \`${s}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const $1=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,F1=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,P1=/^'([^]*?)'?$/,I1=/''/g,ex=/[a-zA-Z]/;function Ht(i,r,s){const c=Ai(),d=s?.locale??c.locale??H1,h=s?.firstWeekContainsDate??s?.locale?.options?.firstWeekContainsDate??c.firstWeekContainsDate??c.locale?.options?.firstWeekContainsDate??1,x=s?.weekStartsOn??s?.locale?.options?.weekStartsOn??c.weekStartsOn??c.locale?.options?.weekStartsOn??0,N=$e(i,s?.in);if(!e1(N))throw new RangeError("Invalid time value");let v=r.match(F1).map(E=>{const _=E[0];if(_==="p"||_==="P"){const H=G1[_];return H(E,d.formatLong)}return E}).join("").match($1).map(E=>{if(E==="''")return{isToken:!1,value:"'"};const _=E[0];if(_==="'")return{isToken:!1,value:tx(E)};if(Mm[_])return{isToken:!0,value:E};if(_.match(ex))throw new RangeError("Format string contains an unescaped latin alphabet character `"+_+"`");return{isToken:!1,value:E}});d.localize.preprocessor&&(v=d.localize.preprocessor(N,v));const y={firstWeekContainsDate:h,weekStartsOn:x,locale:d};return v.map(E=>{if(!E.isToken)return E.value;const _=E.value;(!s?.useAdditionalWeekYearTokens&&K1(_)||!s?.useAdditionalDayOfYearTokens&&k1(_))&&J1(_,r,String(i));const H=Mm[_[0]];return H(N,_,d.localize,y)}).join("")}function tx(i){const r=i.match(P1);return r?r[1].replace(I1,"'"):i}const ax={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},lx=(i,r,s)=>{let c;const d=ax[i];return typeof d=="string"?c=d:r===1?c=d.one:c=d.other.replace("{{count}}",String(r)),s?.addSuffix?s.comparison&&s.comparison>0?"dans "+c:"il y a "+c:c},nx={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},ux={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},ix={full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},cx={date:Ql({formats:nx,defaultWidth:"full"}),time:Ql({formats:ux,defaultWidth:"full"}),dateTime:Ql({formats:ix,defaultWidth:"full"})},rx={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},sx=(i,r,s,c)=>rx[i],fx={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},ox={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},dx={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},mx={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},hx={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},yx=(i,r)=>{const s=Number(i),c=r?.unit;if(s===0)return"0";const d=["year","week","hour","minute","second"];let h;return s===1?h=c&&d.includes(c)?"ère":"er":h="ème",s+h},gx=["MMM","MMMM"],vx={preprocessor:(i,r)=>i.getDate()===1||!r.some(c=>c.isToken&&gx.includes(c.value))?r:r.map(c=>c.isToken&&c.value==="do"?{isToken:!0,value:"d"}:c),ordinalNumber:yx,era:Ct({values:fx,defaultWidth:"wide"}),quarter:Ct({values:ox,defaultWidth:"wide",argumentCallback:i=>i-1}),month:Ct({values:dx,defaultWidth:"wide"}),day:Ct({values:mx,defaultWidth:"wide"}),dayPeriod:Ct({values:hx,defaultWidth:"wide"})},xx=/^(\d+)(ième|ère|ème|er|e)?/i,px=/\d+/i,bx={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},Sx={any:[/^av/i,/^ap/i]},Nx={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},jx={any:[/1/i,/2/i,/3/i,/4/i]},Ex={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},wx={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},Mx={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},Tx={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},Ax={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},Dx={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},Ox={ordinalNumber:Im({matchPattern:xx,parsePattern:px,valueCallback:i=>parseInt(i)}),era:Ut({matchPatterns:bx,defaultMatchWidth:"wide",parsePatterns:Sx,defaultParseWidth:"any"}),quarter:Ut({matchPatterns:Nx,defaultMatchWidth:"wide",parsePatterns:jx,defaultParseWidth:"any",valueCallback:i=>i+1}),month:Ut({matchPatterns:Ex,defaultMatchWidth:"wide",parsePatterns:wx,defaultParseWidth:"any"}),day:Ut({matchPatterns:Mx,defaultMatchWidth:"wide",parsePatterns:Tx,defaultParseWidth:"any"}),dayPeriod:Ut({matchPatterns:Ax,defaultMatchWidth:"any",parsePatterns:Dx,defaultParseWidth:"any"})},It={code:"fr",formatDistance:lx,formatLong:cx,formatRelative:sx,localize:vx,match:Ox,options:{weekStartsOn:1,firstWeekContainsDate:4}},Rx=()=>{const[i,r]=A.useState({totalPatients:0,todayAppointments:0,monthlyConsultations:0,upcomingAppointments:[]}),[s,c]=A.useState(!0);A.useEffect(()=>{d()},[]);const d=async()=>{try{c(!0);const x=new Date,N=new Date(x.getFullYear(),x.getMonth(),x.getDate()),v=new Date(x.getFullYear(),x.getMonth(),x.getDate(),23,59,59),y=a1(x),E=t1(x),_=await Oa.patient.count(),H=await Oa.appointment.count({where:{date:{gte:N,lte:v},status:"SCHEDULED"}}),Z=await Oa.consultation.count({where:{date:{gte:y,lte:E}}}),q=await Oa.appointment.findMany({where:{date:{gte:x},status:"SCHEDULED"},include:{patient:!0},orderBy:{date:"asc"},take:5});r({totalPatients:_,todayAppointments:H,monthlyConsultations:Z,upcomingAppointments:q})}catch(x){console.error("Erreur lors du chargement des données:",x)}finally{c(!1)}},h=({title:x,value:N,icon:v,color:y,trend:E,trendValue:_})=>o.jsx("div",{className:"relative overflow-hidden rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-900/5",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:`rounded-lg p-3 ${y}`,children:v}),o.jsxs("div",{className:"ml-4 flex-1",children:[o.jsx("p",{className:"text-sm font-medium text-gray-600",children:x}),o.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:N}),E&&_&&o.jsxs("div",{className:"mt-1 flex items-center text-sm",children:[o.jsx("span",{className:`font-medium ${E==="up"?"text-green-600":"text-red-600"}`,children:_}),o.jsx("span",{className:"ml-1 text-gray-500",children:"vs mois dernier"})]})]})]})});return s?o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):o.jsxs("div",{className:"space-y-6 lg:space-y-8",children:[o.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 lg:p-8 text-white",children:o.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-2xl lg:text-3xl font-bold",children:"Bonjour Dr. Administrateur 👋"}),o.jsxs("p",{className:"mt-2 text-blue-100",children:[Ht(new Date,"EEEE dd MMMM yyyy",{locale:It})," - Voici un aperçu de votre cabinet"]})]}),o.jsxs("div",{className:"mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3",children:[o.jsxs("button",{className:"inline-flex items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm px-4 py-2 text-sm font-semibold text-white hover:bg-white/30 transition-colors",children:[o.jsx(Ra,{className:"w-4 h-4 mr-2"}),"Planning du jour"]}),o.jsxs("button",{className:"inline-flex items-center justify-center rounded-lg bg-white px-4 py-2 text-sm font-semibold text-blue-600 hover:bg-blue-50 transition-colors",children:[o.jsx(Vl,{className:"w-4 h-4 mr-2"}),"Nouveau RDV"]})]})]})}),o.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[o.jsx(h,{title:"Total Patients",value:i.totalPatients,icon:o.jsx(tl,{className:"w-6 h-6 text-white"}),color:"bg-gradient-to-br from-blue-500 to-blue-600",trend:"up",trendValue:"+12%"}),o.jsx(h,{title:"RDV Aujourd'hui",value:i.todayAppointments,icon:o.jsx(Ra,{className:"w-6 h-6 text-white"}),color:"bg-gradient-to-br from-emerald-500 to-emerald-600",trend:"up",trendValue:"+5%"}),o.jsx(h,{title:"Consultations ce mois",value:i.monthlyConsultations,icon:o.jsx(Qv,{className:"w-6 h-6 text-white"}),color:"bg-gradient-to-br from-purple-500 to-purple-600",trend:"up",trendValue:"+8%"}),o.jsx(h,{title:"Revenus du mois",value:2450,icon:o.jsx(gv,{className:"w-6 h-6 text-white"}),color:"bg-gradient-to-br from-amber-500 to-orange-500",trend:"up",trendValue:"+15%"})]}),o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[o.jsx("div",{className:"lg:col-span-2",children:o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl",children:[o.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Prochains rendez-vous"}),o.jsxs("button",{className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:["Voir tout",o.jsx(Wn,{className:"w-4 h-4 inline ml-1"})]})]})}),o.jsx("div",{className:"p-6",children:i.upcomingAppointments.length===0?o.jsxs("div",{className:"text-center py-12",children:[o.jsx(Ra,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),o.jsx("p",{className:"text-gray-500",children:"Aucun rendez-vous à venir"})]}):o.jsx("div",{className:"space-y-4",children:i.upcomingAppointments.map(x=>o.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:o.jsx(tl,{className:"w-5 h-5 text-blue-600"})})}),o.jsxs("div",{children:[o.jsxs("p",{className:"font-medium text-gray-900",children:[x.patient.firstName," ",x.patient.lastName]}),o.jsxs("p",{className:"text-sm text-gray-500",children:[Ht(new Date(x.date),"dd MMMM yyyy",{locale:It})," à ",x.time]}),o.jsx("p",{className:"text-xs text-gray-400",children:x.notes})]})]}),o.jsxs("div",{className:"text-right",children:[o.jsx("span",{className:"inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700",children:x.type}),o.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[x.duration," min"]})]})]},x.id))})})]})}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actions rapides"}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("button",{className:"w-full flex items-center justify-between p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx(Vl,{className:"w-5 h-5 text-blue-600 mr-3"}),o.jsx("span",{className:"font-medium text-blue-900",children:"Nouveau patient"})]}),o.jsx(Wn,{className:"w-4 h-4 text-blue-600"})]}),o.jsxs("button",{className:"w-full flex items-center justify-between p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx(Ra,{className:"w-5 h-5 text-green-600 mr-3"}),o.jsx("span",{className:"font-medium text-green-900",children:"Planifier RDV"})]}),o.jsx(Wn,{className:"w-4 h-4 text-green-600"})]}),o.jsxs("button",{className:"w-full flex items-center justify-between p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx(el,{className:"w-5 h-5 text-purple-600 mr-3"}),o.jsx("span",{className:"font-medium text-purple-900",children:"Recherche rapide"})]}),o.jsx(Wn,{className:"w-4 h-4 text-purple-600"})]})]})]}),o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Activité récente"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-start space-x-3",children:[o.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full mt-2"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("p",{className:"text-sm text-gray-900",children:"Consultation terminée"}),o.jsx("p",{className:"text-xs text-gray-500",children:"Jean Dupont - il y a 2h"})]})]}),o.jsxs("div",{className:"flex items-start space-x-3",children:[o.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("p",{className:"text-sm text-gray-900",children:"Nouveau rendez-vous"}),o.jsx("p",{className:"text-xs text-gray-500",children:"Marie Martin - il y a 4h"})]})]}),o.jsxs("div",{className:"flex items-start space-x-3",children:[o.jsx("div",{className:"w-2 h-2 bg-orange-400 rounded-full mt-2"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("p",{className:"text-sm text-gray-900",children:"Paiement reçu"}),o.jsx("p",{className:"text-xs text-gray-500",children:"85€ - il y a 6h"})]})]})]})]})]})]})]})},_x=()=>{const[i,r]=A.useState([]),[s,c]=A.useState(""),[d,h]=A.useState(!0);A.useEffect(()=>{x()},[]);const x=async()=>{try{h(!0);const v=await Oa.patient.findMany();r(v)}catch(v){console.error("Erreur lors du chargement des patients:",v)}finally{h(!1)}},N=i.filter(v=>`${v.firstName} ${v.lastName}`.toLowerCase().includes(s.toLowerCase())||v.phone.includes(s)||v.email?.toLowerCase().includes(s.toLowerCase()));return d?o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"sm:flex sm:items-center sm:justify-between",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Patients"}),o.jsx("p",{className:"mt-2 text-sm text-gray-700",children:"Gérez vos patients et leurs informations médicales"})]}),o.jsx("div",{className:"mt-4 sm:mt-0",children:o.jsxs("button",{onClick:()=>console.log("Ajouter patient"),className:"inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600",children:[o.jsx(Vl,{className:"w-4 h-4 mr-2"}),"Nouveau patient"]})})]}),o.jsx("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:o.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[o.jsx("div",{className:"flex-1",children:o.jsxs("div",{className:"relative",children:[o.jsx(Pn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),o.jsx("input",{type:"text",placeholder:"Rechercher par nom, téléphone ou email...",value:s,onChange:v=>c(v.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})}),o.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[o.jsx(Nv,{className:"w-4 h-4 mr-2"}),"Filtres"]})]})}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6",children:[o.jsx("div",{className:"bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 p-6",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"p-3 bg-blue-100 rounded-lg",children:o.jsx(tl,{className:"w-6 h-6 text-blue-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total patients"}),o.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:i.length})]})]})}),o.jsx("div",{className:"bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 p-6",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"p-3 bg-green-100 rounded-lg",children:o.jsx(Ra,{className:"w-6 h-6 text-green-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Nouveaux ce mois"}),o.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:"12"})]})]})}),o.jsx("div",{className:"bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 p-6",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"p-3 bg-purple-100 rounded-lg",children:o.jsx(el,{className:"w-6 h-6 text-purple-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Contacts actifs"}),o.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:i.filter(v=>v.phone).length})]})]})})]}),o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden",children:[o.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:o.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Liste des patients (",N.length,")"]})}),N.length===0?o.jsxs("div",{className:"text-center py-12",children:[o.jsx(tl,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),o.jsx("p",{className:"text-gray-500",children:s?"Aucun patient trouvé pour cette recherche":"Aucun patient enregistré"})]}):o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[o.jsx("thead",{className:"bg-gray-50",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Patient"}),o.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),o.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Âge"}),o.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Dernière visite"}),o.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(v=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:o.jsxs("span",{className:"text-sm font-medium text-blue-600",children:[v.firstName[0],v.lastName[0]]})}),o.jsxs("div",{className:"ml-4",children:[o.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[v.firstName," ",v.lastName]}),v.notes&&o.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:v.notes})]})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"space-y-1",children:[o.jsxs("div",{className:"flex items-center text-sm text-gray-900",children:[o.jsx(el,{className:"w-4 h-4 mr-2 text-gray-400"}),v.phone]}),v.email&&o.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[o.jsx(as,{className:"w-4 h-4 mr-2 text-gray-400"}),v.email]})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:v.dateOfBirth?new Date().getFullYear()-new Date(v.dateOfBirth).getFullYear()+" ans":"-"}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:Ht(new Date(v.updatedAt),"dd/MM/yyyy",{locale:It})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:o.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[o.jsx("button",{className:"text-blue-600 hover:text-blue-900 p-1",children:o.jsx(xv,{className:"w-4 h-4"})}),o.jsx("button",{className:"text-gray-600 hover:text-gray-900 p-1",children:o.jsx(Wm,{className:"w-4 h-4"})}),o.jsx("button",{className:"text-red-600 hover:text-red-900 p-1",children:o.jsx(Xv,{className:"w-4 h-4"})})]})})]},v.id))})]})})]})]})},zx=()=>{const[i,r]=A.useState([]),[s,c]=A.useState(new Date),[d,h]=A.useState("week"),[x,N]=A.useState(!0),[v,y]=A.useState("");A.useEffect(()=>{E()},[]);const E=async()=>{try{N(!0);const U=await Oa.appointment.findMany({});r(U)}catch(U){console.error("Erreur lors du chargement des rendez-vous:",U)}finally{N(!1)}},_=()=>{const U=al(s,{weekStartsOn:1});return Array.from({length:7},(Y,I)=>Ir(U,I))},H=U=>i.filter(Y=>wm(new Date(Y.date),U)&&(v===""||`${Y.patient?.firstName} ${Y.patient?.lastName}`.toLowerCase().includes(v.toLowerCase())||Y.patient?.phone.includes(v))),Z=U=>{switch(U){case"SCHEDULED":return"bg-blue-100 text-blue-800";case"COMPLETED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";case"NO_SHOW":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}},q=U=>{switch(U){case"CONSULTATION":return"bg-purple-100 text-purple-800";case"CONTROLE":return"bg-green-100 text-green-800";case"URGENCE":return"bg-red-100 text-red-800";case"TRAITEMENT":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},G=["08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00","18:30"];return x?o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"sm:flex sm:items-center sm:justify-between",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Rendez-vous"}),o.jsx("p",{className:"mt-2 text-sm text-gray-700",children:"Planifiez et gérez vos rendez-vous patients"})]}),o.jsx("div",{className:"mt-4 sm:mt-0 flex space-x-3",children:o.jsxs("button",{className:"inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500",children:[o.jsx(Vl,{className:"w-4 h-4 mr-2"}),"Nouveau RDV"]})})]}),o.jsx("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:o.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("button",{onClick:()=>c(Ir(s,d==="day"?-1:-7)),className:"p-2 hover:bg-gray-100 rounded-lg",children:o.jsx(sv,{className:"w-5 h-5"})}),o.jsx("h2",{className:"text-lg font-semibold text-gray-900 min-w-[200px] text-center",children:d==="day"?Ht(s,"EEEE dd MMMM yyyy",{locale:It}):`Semaine du ${Ht(al(s,{weekStartsOn:1}),"dd MMM",{locale:It})} au ${Ht(n1(s,{weekStartsOn:1}),"dd MMM yyyy",{locale:It})}`}),o.jsx("button",{onClick:()=>c(Ir(s,d==="day"?1:7)),className:"p-2 hover:bg-gray-100 rounded-lg",children:o.jsx(Wn,{className:"w-5 h-5"})})]}),o.jsx("button",{onClick:()=>c(new Date),className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg",children:"Aujourd'hui"})]}),o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("div",{className:"relative",children:[o.jsx(Pn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),o.jsx("input",{type:"text",placeholder:"Rechercher un patient...",value:v,onChange:U=>y(U.target.value),className:"pl-9 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),o.jsxs("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[o.jsx("button",{onClick:()=>h("day"),className:`px-3 py-1 text-sm rounded-md ${d==="day"?"bg-white shadow-sm":""}`,children:"Jour"}),o.jsx("button",{onClick:()=>h("week"),className:`px-3 py-1 text-sm rounded-md ${d==="week"?"bg-white shadow-sm":""}`,children:"Semaine"})]})]})]})}),o.jsx("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden",children:d==="week"?o.jsxs("div",{className:"grid grid-cols-8 divide-x divide-gray-200",children:[o.jsxs("div",{className:"bg-gray-50",children:[o.jsx("div",{className:"h-16 border-b border-gray-200 flex items-center justify-center",children:o.jsx(hv,{className:"w-5 h-5 text-gray-400"})}),G.map(U=>o.jsx("div",{className:"h-16 border-b border-gray-200 flex items-center justify-center text-sm text-gray-500",children:U},U))]}),_().map(U=>o.jsxs("div",{className:"min-w-0",children:[o.jsxs("div",{className:"h-16 border-b border-gray-200 p-4",children:[o.jsx("div",{className:"text-sm font-medium text-gray-900",children:Ht(U,"EEE",{locale:It})}),o.jsx("div",{className:`text-lg font-semibold ${wm(U,new Date)?"text-blue-600":"text-gray-900"}`,children:Ht(U,"d")})]}),o.jsx("div",{className:"relative",children:G.map(Y=>o.jsx("div",{className:"h-16 border-b border-gray-200 p-1",children:H(U).filter(I=>I.time===Y).map(I=>o.jsxs("div",{className:"bg-blue-100 border border-blue-200 rounded p-1 text-xs cursor-pointer hover:bg-blue-200 transition-colors",children:[o.jsxs("div",{className:"font-medium text-blue-900 truncate",children:[I.patient?.firstName," ",I.patient?.lastName]}),o.jsxs("div",{className:"text-blue-700 truncate",children:[I.type," - ",I.duration,"min"]})]},I.id))},Y))})]},U.toISOString()))]}):o.jsx("div",{className:"p-6",children:o.jsx("div",{className:"space-y-4",children:H(s).length===0?o.jsxs("div",{className:"text-center py-12",children:[o.jsx(Ra,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),o.jsx("p",{className:"text-gray-500",children:"Aucun rendez-vous pour cette journée"})]}):H(s).sort((U,Y)=>U.time.localeCompare(Y.time)).map(U=>o.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("div",{className:"text-lg font-semibold text-gray-900 min-w-[60px]",children:U.time}),o.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:o.jsx(Ni,{className:"w-5 h-5 text-blue-600"})}),o.jsxs("div",{children:[o.jsxs("div",{className:"font-medium text-gray-900",children:[U.patient?.firstName," ",U.patient?.lastName]}),o.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[o.jsxs("span",{className:"flex items-center",children:[o.jsx(el,{className:"w-4 h-4 mr-1"}),U.patient?.phone]}),o.jsxs("span",{children:[U.duration," minutes"]})]})]})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${q(U.type)}`,children:U.type}),o.jsx("span",{className:`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${Z(U.status)}`,children:U.status})]})]},U.id))})})})]})},Cx=()=>{const[i,r]=A.useState(""),[s,c]=A.useState([]),[d,h]=A.useState(null),[x,N]=A.useState(!1),[v,y]=A.useState([]);A.useEffect(()=>{const q=localStorage.getItem("recentSearches");q&&y(JSON.parse(q))},[]),A.useEffect(()=>{i.length>=2?E():(c([]),h(null))},[i]);const E=async()=>{N(!0);try{const G=(await Oa.patient.findMany()).filter(U=>U.phone.includes(i)||`${U.firstName} ${U.lastName}`.toLowerCase().includes(i.toLowerCase())||U.email?.toLowerCase().includes(i.toLowerCase()));c(G),G.length===1&&G[0].phone===i&&(h(G[0]),_(i))}catch(q){console.error("Erreur lors de la recherche:",q)}finally{N(!1)}},_=q=>{const G=[q,...v.filter(U=>U!==q)].slice(0,5);y(G),localStorage.setItem("recentSearches",JSON.stringify(G))},H=q=>{h(q),_(q.phone)},Z=()=>{r(""),c([]),h(null)};return o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Recherche rapide"}),o.jsx("p",{className:"mt-2 text-sm text-gray-700",children:"Trouvez rapidement un patient par téléphone, nom ou email"})]}),o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:[o.jsxs("div",{className:"relative",children:[o.jsx(Pn,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400"}),o.jsx("input",{type:"text",placeholder:"Tapez un numéro de téléphone, nom ou email...",value:i,onChange:q=>r(q.target.value),className:"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500",autoFocus:!0}),i&&o.jsx("button",{onClick:Z,className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:"✕"})]}),!i&&v.length>0&&o.jsxs("div",{className:"mt-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Recherches récentes :"}),o.jsx("div",{className:"flex flex-wrap gap-2",children:v.map((q,G)=>o.jsx("button",{onClick:()=>r(q),className:"px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors",children:q},G))})]})]}),x&&o.jsx("div",{className:"flex items-center justify-center py-8",children:o.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),s.length>0&&!d&&o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden",children:[o.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:o.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Résultats de recherche (",s.length,")"]})}),o.jsx("div",{className:"divide-y divide-gray-200",children:s.map(q=>o.jsx("div",{onClick:()=>H(q),className:"p-6 hover:bg-gray-50 cursor-pointer transition-colors",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:o.jsxs("span",{className:"text-lg font-medium text-blue-600",children:[q.firstName[0],q.lastName[0]]})}),o.jsxs("div",{children:[o.jsxs("h4",{className:"text-lg font-medium text-gray-900",children:[q.firstName," ",q.lastName]}),o.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[o.jsxs("span",{className:"flex items-center",children:[o.jsx(el,{className:"w-4 h-4 mr-1"}),q.phone]}),q.email&&o.jsxs("span",{className:"flex items-center",children:[o.jsx(as,{className:"w-4 h-4 mr-1"}),q.email]})]})]})]}),o.jsx("div",{className:"text-sm text-gray-500",children:"Cliquez pour voir le dossier"})]})},q.id))})]}),d&&o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[o.jsx("div",{className:"lg:col-span-2 space-y-6",children:o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:[o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:o.jsxs("span",{className:"text-xl font-medium text-blue-600",children:[d.firstName[0],d.lastName[0]]})}),o.jsxs("div",{children:[o.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[d.firstName," ",d.lastName]}),o.jsxs("p",{className:"text-gray-500",children:["Patient depuis ",Ht(new Date(d.createdAt),"MMMM yyyy",{locale:It})]})]})]}),o.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[o.jsx(Wm,{className:"w-4 h-4 mr-2"}),"Modifier"]})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informations de contact"}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx(el,{className:"w-5 h-5 text-gray-400 mr-3"}),o.jsx("span",{className:"text-gray-900",children:d.phone})]}),d.email&&o.jsxs("div",{className:"flex items-center",children:[o.jsx(as,{className:"w-5 h-5 text-gray-400 mr-3"}),o.jsx("span",{className:"text-gray-900",children:d.email})]}),d.address&&o.jsxs("div",{className:"flex items-start",children:[o.jsx(zv,{className:"w-5 h-5 text-gray-400 mr-3 mt-0.5"}),o.jsx("span",{className:"text-gray-900",children:d.address})]}),d.dateOfBirth&&o.jsxs("div",{className:"flex items-center",children:[o.jsx(Ra,{className:"w-5 h-5 text-gray-400 mr-3"}),o.jsxs("span",{className:"text-gray-900",children:[Ht(new Date(d.dateOfBirth),"dd MMMM yyyy",{locale:It}),"(",new Date().getFullYear()-new Date(d.dateOfBirth).getFullYear()," ans)"]})]})]})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informations médicales"}),o.jsxs("div",{className:"space-y-3",children:[d.medicalHistory&&o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Antécédents médicaux"}),o.jsx("p",{className:"text-gray-900",children:d.medicalHistory})]}),d.allergies&&o.jsxs("div",{children:[o.jsxs("p",{className:"text-sm font-medium text-gray-700 flex items-center",children:[o.jsx(dv,{className:"w-4 h-4 text-red-500 mr-1"}),"Allergies"]}),o.jsx("p",{className:"text-red-700 font-medium",children:d.allergies})]}),d.notes&&o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Notes"}),o.jsx("p",{className:"text-gray-900",children:d.notes})]})]})]})]})]})}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actions rapides"}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("button",{className:"w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[o.jsx(Vl,{className:"w-5 h-5 mr-2"}),"Nouveau rendez-vous"]}),o.jsxs("button",{className:"w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[o.jsx(bv,{className:"w-5 h-5 mr-2"}),"Nouvelle consultation"]}),o.jsxs("button",{className:"w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[o.jsx(el,{className:"w-5 h-5 mr-2"}),"Appeler le patient"]})]})]}),o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-6",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Dernières consultations"}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Contrôle de routine"}),o.jsx("span",{className:"text-xs text-gray-500",children:"Il y a 2 mois"})]}),o.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"Détartrage effectué"})]}),o.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Traitement carie"}),o.jsx("span",{className:"text-xs text-gray-500",children:"Il y a 6 mois"})]}),o.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"Obturation composite"})]})]})]})]})]}),i.length>=2&&s.length===0&&!x&&o.jsxs("div",{className:"bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl p-12 text-center",children:[o.jsx(Pn,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),o.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Aucun patient trouvé"}),o.jsxs("p",{className:"text-gray-500 mb-4",children:['Aucun patient ne correspond à votre recherche "',i,'"']}),o.jsxs("button",{className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[o.jsx(Vl,{className:"w-4 h-4 mr-2"}),"Créer un nouveau patient"]})]})]})},Ux=()=>{const[i,r]=A.useState(!1),{user:s,logout:c}=Ti(),d=la(),h=[{name:"Dashboard",href:"/",icon:Ev},{name:"Patients",href:"/patients",icon:tl},{name:"Rendez-vous",href:"/appointments",icon:Ra},{name:"Recherche rapide",href:"/search",icon:Pn}],x=N=>d.pathname===N;return o.jsxs("div",{className:"h-screen flex overflow-hidden bg-gray-50",children:[o.jsxs("div",{className:`fixed inset-0 flex z-40 md:hidden ${i?"":"hidden"}`,children:[o.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>r(!1)}),o.jsxs("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl",children:[o.jsx("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:o.jsx("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>r(!1),children:o.jsx(Kv,{className:"h-6 w-6 text-white"})})}),o.jsxs("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[o.jsxs("div",{className:"flex-shrink-0 flex items-center px-4 mb-8",children:[o.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3",children:o.jsx(tl,{className:"w-5 h-5 text-white"})}),o.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Cabinet Dentaire"})]}),o.jsx("nav",{className:"px-3 space-y-1",children:h.map(N=>{const v=N.icon;return o.jsxs(bi,{to:N.href,className:`${x(N.href)?"bg-blue-50 text-blue-700 border-r-2 border-blue-600":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"} group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`,onClick:()=>r(!1),children:[o.jsx(v,{className:"mr-3 h-5 w-5"}),N.name]},N.name)})})]})]})]}),o.jsx("div",{className:"hidden md:flex md:flex-shrink-0",children:o.jsx("div",{className:"flex flex-col w-64",children:o.jsxs("div",{className:"flex flex-col h-0 flex-1 bg-white shadow-sm border-r border-gray-200",children:[o.jsxs("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[o.jsxs("div",{className:"flex items-center flex-shrink-0 px-4 mb-8",children:[o.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3",children:o.jsx(tl,{className:"w-5 h-5 text-white"})}),o.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Cabinet Dentaire"})]}),o.jsx("nav",{className:"flex-1 px-3 space-y-1",children:h.map(N=>{const v=N.icon;return o.jsxs(bi,{to:N.href,className:`${x(N.href)?"bg-blue-50 text-blue-700 border-r-2 border-blue-600":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"} group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`,children:[o.jsx(v,{className:"mr-3 h-5 w-5"}),N.name]},N.name)})})]}),o.jsx("div",{className:"flex-shrink-0 border-t border-gray-200 p-4",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:o.jsx(Ni,{className:"h-5 w-5 text-gray-600"})})}),o.jsxs("div",{className:"ml-3 flex-1",children:[o.jsx("p",{className:"text-sm font-medium text-gray-900",children:s?.name}),o.jsx("p",{className:"text-xs text-gray-500",children:s?.email})]}),o.jsx("button",{onClick:c,className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Se déconnecter",children:o.jsx(Ov,{className:"h-4 w-4"})})]})})]})})}),o.jsxs("div",{className:"flex flex-col w-0 flex-1 overflow-hidden",children:[o.jsx("div",{className:"md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3",children:o.jsx("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500",onClick:()=>r(!0),children:o.jsx(Uv,{className:"h-6 w-6"})})}),o.jsx("main",{className:"flex-1 relative z-0 overflow-y-auto focus:outline-none",children:o.jsx("div",{className:"py-6",children:o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:o.jsxs(Xm,{children:[o.jsx(Ia,{path:"/",element:o.jsx(Rx,{})}),o.jsx(Ia,{path:"/patients",element:o.jsx(_x,{})}),o.jsx(Ia,{path:"/appointments",element:o.jsx(zx,{})}),o.jsx(Ia,{path:"/search",element:o.jsx(Cx,{})})]})})})})]})]})},Hx=({children:i})=>{const{user:r,isLoading:s}=Ti();return s?o.jsx("div",{className:"min-h-screen flex items-center justify-center",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):r?o.jsx(o.Fragment,{children:i}):o.jsx(Lm,{to:"/login"})},Yx=()=>{const{user:i}=Ti();return o.jsx(Zg,{children:o.jsxs(Xm,{children:[o.jsx(Ia,{path:"/login",element:i?o.jsx(Lm,{to:"/"}):o.jsx(Jv,{})}),o.jsx(Ia,{path:"/*",element:o.jsx(Hx,{children:o.jsx(Ux,{})})})]})})};function qx(){return o.jsx(tv,{children:o.jsx(Yx,{})})}zy.createRoot(document.getElementById("root")).render(o.jsx(A.StrictMode,{children:o.jsx(qx,{})}));
