// Base de données utilisant Prisma et SQLite
import { PrismaClient } from '../generated/prisma';

// Instance Prisma
const prisma = new PrismaClient();

// Types compatibles avec l'interface existante
export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConsultationData {
  id?: string;
  patientId: string;
  date: Date;
  type: string;
  reason?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  observations?: string;
  conclusion?: string;
  nextAppointment?: Date;
  cost?: number;
  paid?: boolean;
  privateNotes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PaymentData {
  id?: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: string;
  date: Date;
  description?: string;
  createdAt?: Date;
}

class PrismaDatabase {
  // === PATIENTS ===
  async createPatient(patient: PatientData): Promise<PatientData> {
    try {
      const newPatient = await prisma.patient.create({
        data: {
          firstName: patient.firstName,
          lastName: patient.lastName,
          phone: patient.phone,
          email: patient.email,
          dateOfBirth: patient.dateOfBirth,
          gender: patient.gender,
          address: patient.address,
          city: patient.city,
          postalCode: patient.postalCode,
          profession: patient.profession,
          emergencyContact: patient.emergencyContact,
          emergencyPhone: patient.emergencyPhone,
          medicalHistory: patient.medicalHistory,
          allergies: patient.allergies,
          medications: patient.medications,
          bloodType: patient.bloodType,
          insuranceNumber: patient.insuranceNumber,
          insuranceProvider: patient.insuranceProvider,
          notes: patient.notes,
          isActive: patient.isActive ?? true
        }
      });

      console.log('✅ Patient créé:', newPatient.firstName, newPatient.lastName);
      return newPatient;
    } catch (error) {
      console.error('❌ Erreur création patient:', error);
      throw error;
    }
  }

  async getAllPatients(): Promise<PatientData[]> {
    try {
      const patients = await prisma.patient.findMany({
        where: { isActive: true },
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      });

      return patients;
    } catch (error) {
      console.error('❌ Erreur récupération patients:', error);
      return [];
    }
  }

  async getPatientById(id: string): Promise<PatientData | null> {
    try {
      const patient = await prisma.patient.findUnique({
        where: { id }
      });

      return patient;
    } catch (error) {
      console.error('❌ Erreur récupération patient:', error);
      return null;
    }
  }

  async updatePatient(id: string, patient: Partial<PatientData>): Promise<PatientData | null> {
    try {
      const updatedPatient = await prisma.patient.update({
        where: { id },
        data: {
          firstName: patient.firstName,
          lastName: patient.lastName,
          phone: patient.phone,
          email: patient.email,
          dateOfBirth: patient.dateOfBirth,
          gender: patient.gender,
          address: patient.address,
          city: patient.city,
          postalCode: patient.postalCode,
          profession: patient.profession,
          emergencyContact: patient.emergencyContact,
          emergencyPhone: patient.emergencyPhone,
          medicalHistory: patient.medicalHistory,
          allergies: patient.allergies,
          medications: patient.medications,
          bloodType: patient.bloodType,
          insuranceNumber: patient.insuranceNumber,
          insuranceProvider: patient.insuranceProvider,
          notes: patient.notes
        }
      });

      console.log('✅ Patient mis à jour:', updatedPatient.firstName, updatedPatient.lastName);
      return updatedPatient;
    } catch (error) {
      console.error('❌ Erreur mise à jour patient:', error);
      return null;
    }
  }

  async deletePatient(id: string): Promise<boolean> {
    try {
      await prisma.patient.update({
        where: { id },
        data: { isActive: false }
      });

      console.log('✅ Patient désactivé:', id);
      return true;
    } catch (error) {
      console.error('❌ Erreur suppression patient:', error);
      return false;
    }
  }

  async searchPatients(query: string): Promise<PatientData[]> {
    try {
      const patients = await prisma.patient.findMany({
        where: {
          isActive: true,
          OR: [
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query } },
            { email: { contains: query, mode: 'insensitive' } }
          ]
        },
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      });

      return patients;
    } catch (error) {
      console.error('❌ Erreur recherche patients:', error);
      return [];
    }
  }

  // === RENDEZ-VOUS ===
  async createAppointment(appointment: AppointmentData): Promise<AppointmentData> {
    try {
      const newAppointment = await prisma.appointment.create({
        data: {
          patientId: appointment.patientId!,
          date: appointment.date,
          time: appointment.time,
          duration: appointment.duration,
          type: appointment.type,
          status: appointment.status,
          notes: appointment.notes
        }
      });

      console.log('✅ Rendez-vous créé:', newAppointment.date, newAppointment.time);
      return {
        ...newAppointment,
        isUrgent: false // Ajouter le champ manquant
      };
    } catch (error) {
      console.error('❌ Erreur création rendez-vous:', error);
      throw error;
    }
  }

  async getAllAppointments(): Promise<AppointmentData[]> {
    try {
      const appointments = await prisma.appointment.findMany({
        include: {
          patient: true
        },
        orderBy: [
          { date: 'desc' },
          { time: 'desc' }
        ]
      });

      return appointments.map(apt => ({
        ...apt,
        patientName: `${apt.patient.firstName} ${apt.patient.lastName}`,
        patientPhone: apt.patient.phone,
        isUrgent: false // Ajouter le champ manquant
      }));
    } catch (error) {
      console.error('❌ Erreur récupération rendez-vous:', error);
      return [];
    }
  }

  async getAppointmentsByPatient(patientId: string): Promise<AppointmentData[]> {
    try {
      const appointments = await prisma.appointment.findMany({
        where: { patientId },
        include: {
          patient: true
        },
        orderBy: [
          { date: 'desc' },
          { time: 'desc' }
        ]
      });

      return appointments.map(apt => ({
        ...apt,
        patientName: `${apt.patient.firstName} ${apt.patient.lastName}`,
        patientPhone: apt.patient.phone,
        isUrgent: false // Ajouter le champ manquant
      }));
    } catch (error) {
      console.error('❌ Erreur récupération rendez-vous patient:', error);
      return [];
    }
  }

  async updateAppointment(id: string, appointment: Partial<AppointmentData>): Promise<AppointmentData | null> {
    try {
      const updatedAppointment = await prisma.appointment.update({
        where: { id },
        data: {
          patientId: appointment.patientId,
          date: appointment.date,
          time: appointment.time,
          duration: appointment.duration,
          type: appointment.type,
          status: appointment.status,
          notes: appointment.notes
        },
        include: {
          patient: true
        }
      });

      console.log('✅ Rendez-vous mis à jour:', updatedAppointment.date, updatedAppointment.time);
      return {
        ...updatedAppointment,
        patientName: `${updatedAppointment.patient.firstName} ${updatedAppointment.patient.lastName}`,
        patientPhone: updatedAppointment.patient.phone,
        isUrgent: false
      };
    } catch (error) {
      console.error('❌ Erreur mise à jour rendez-vous:', error);
      return null;
    }
  }

  async deleteAppointment(id: string): Promise<boolean> {
    try {
      await prisma.appointment.delete({
        where: { id }
      });

      console.log('✅ Rendez-vous supprimé:', id);
      return true;
    } catch (error) {
      console.error('❌ Erreur suppression rendez-vous:', error);
      return false;
    }
  }

  // Fermer la connexion
  async disconnect(): Promise<void> {
    await prisma.$disconnect();
  }
}

// Instance singleton
export const database = new PrismaDatabase();
export default database;
