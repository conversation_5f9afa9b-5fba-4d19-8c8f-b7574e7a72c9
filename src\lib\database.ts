// Base de données locale utilisant localStorage
// En production, ceci serait remplacé par des appels API REST vers une vraie base de données

// Simuler une base de données côté client avec localStorage
const DB_KEY = 'cabinet_patients';
const CONSULTATIONS_KEY = 'cabinet_consultations';
const PAYMENTS_KEY = 'cabinet_payments';

export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConsultationData {
  id?: string;
  patientId: string;
  type: string;
  reason?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  cost?: number;
  nextAppointment?: Date;
  date: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

// Interfaces simplifiées pour éviter les problèmes d'export
export interface PaymentData {
  id?: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: string;
  date: Date;
  description?: string;
  createdAt?: Date;
}

export interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number; // en minutes
  status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Fonctions pour gérer la base de données locale
export const database = {
  // Initialiser avec des données par défaut
  init() {
    const existing = localStorage.getItem(DB_KEY);
    if (!existing) {
      // Initialiser avec une liste vide
      localStorage.setItem(DB_KEY, JSON.stringify([]));
    }

    // Initialiser les autres collections si elles n'existent pas
    const existingConsultations = localStorage.getItem(CONSULTATIONS_KEY);
    if (!existingConsultations) {
      localStorage.setItem(CONSULTATIONS_KEY, JSON.stringify([]));
    }

    const existingPayments = localStorage.getItem(PAYMENTS_KEY);
    if (!existingPayments) {
      localStorage.setItem(PAYMENTS_KEY, JSON.stringify([]));
    }

    const existingAppointments = localStorage.getItem('cabinet_appointments');
    if (!existingAppointments) {
      localStorage.setItem('cabinet_appointments', JSON.stringify([]));
    }
  },

  // Récupérer tous les patients
  getAllPatients(): PatientData[] {
    const data = localStorage.getItem(DB_KEY);
    if (!data) return [];
    
    const patients = JSON.parse(data);
    // Convertir les dates string en objets Date
    return patients.map((p: any) => ({
      ...p,
      dateOfBirth: p.dateOfBirth ? new Date(p.dateOfBirth) : undefined,
      createdAt: new Date(p.createdAt),
      updatedAt: new Date(p.updatedAt)
    }));
  },

  // Récupérer un patient par ID
  getPatientById(id: string): PatientData | null {
    const patients = this.getAllPatients();
    return patients.find(p => p.id === id) || null;
  },

  // Créer un nouveau patient
  createPatient(data: Omit<PatientData, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>): PatientData {
    const patients = this.getAllPatients();
    const newPatient: PatientData = {
      ...data,
      id: Date.now().toString(),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    patients.push(newPatient);
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient créé et sauvegardé:', newPatient);
    return newPatient;
  },

  // Mettre à jour un patient
  updatePatient(id: string, data: Partial<PatientData>): PatientData | null {
    const patients = this.getAllPatients();
    const index = patients.findIndex(p => p.id === id);
    
    if (index === -1) return null;
    
    patients[index] = {
      ...patients[index],
      ...data,
      updatedAt: new Date()
    };
    
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient mis à jour:', patients[index]);
    return patients[index];
  },

  // Supprimer un patient
  deletePatient(id: string): boolean {
    const patients = this.getAllPatients();
    const index = patients.findIndex(p => p.id === id);
    
    if (index === -1) return false;
    
    const deleted = patients.splice(index, 1)[0];
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient supprimé:', deleted);
    return true;
  },

  // Rechercher des patients
  searchPatients(query: string): PatientData[] {
    const patients = this.getAllPatients();
    const lowerQuery = query.toLowerCase();

    return patients.filter(patient =>
      `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(lowerQuery) ||
      patient.phone.includes(query) ||
      patient.email?.toLowerCase().includes(lowerQuery)
    );
  },

  // === CONSULTATIONS ===

  // Créer une consultation
  createConsultation(data: Omit<ConsultationData, 'id' | 'createdAt' | 'updatedAt'>, paymentData?: { amount: number; method: string }): ConsultationData {
    const consultations = this.getAllConsultations();
    const newConsultation: ConsultationData = {
      ...data,
      id: Date.now().toString(),
      date: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    consultations.push(newConsultation);
    localStorage.setItem(CONSULTATIONS_KEY, JSON.stringify(consultations));

    // Si un paiement est fourni, l'enregistrer aussi
    if (paymentData && paymentData.amount > 0) {
      this.createPayment({
        patientId: data.patientId,
        consultationId: newConsultation.id!,
        amount: paymentData.amount,
        method: paymentData.method as any,
        date: new Date(),
        description: `Paiement pour consultation ${data.type}`
      });
    }

    console.log('Consultation créée:', newConsultation);
    return newConsultation;
  },

  // Récupérer toutes les consultations
  getAllConsultations(): ConsultationData[] {
    const data = localStorage.getItem(CONSULTATIONS_KEY);
    if (!data) return [];

    const consultations = JSON.parse(data);
    return consultations.map((c: any) => ({
      ...c,
      date: new Date(c.date),
      nextAppointment: c.nextAppointment ? new Date(c.nextAppointment) : undefined,
      createdAt: new Date(c.createdAt),
      updatedAt: new Date(c.updatedAt)
    }));
  },

  // Récupérer les consultations d'un patient
  getConsultationsByPatient(patientId: string): ConsultationData[] {
    const consultations = this.getAllConsultations();
    return consultations.filter(c => c.patientId === patientId);
  },

  // === PAIEMENTS ===

  // Créer un paiement
  createPayment(data: Omit<PaymentData, 'id' | 'createdAt'>): PaymentData {
    const payments = this.getAllPayments();
    const newPayment: PaymentData = {
      ...data,
      id: Date.now().toString(),
      createdAt: new Date()
    };

    payments.push(newPayment);
    localStorage.setItem(PAYMENTS_KEY, JSON.stringify(payments));

    console.log('Paiement créé:', newPayment);
    return newPayment;
  },

  // Récupérer tous les paiements
  getAllPayments(): PaymentData[] {
    const data = localStorage.getItem(PAYMENTS_KEY);
    if (!data) return [];

    const payments = JSON.parse(data);
    return payments.map((p: any) => ({
      ...p,
      date: new Date(p.date),
      createdAt: new Date(p.createdAt)
    }));
  },

  // Récupérer les paiements d'un patient
  getPaymentsByPatient(patientId: string): PaymentData[] {
    const payments = this.getAllPayments();
    return payments.filter(p => p.patientId === patientId);
  },

  // Récupérer les paiements d'une consultation
  getPaymentsByConsultation(consultationId: string): PaymentData[] {
    const payments = this.getAllPayments();
    return payments.filter(p => p.consultationId === consultationId);
  },

  // === FONCTIONS SIMPLIFIÉES ===

  // Calculer le total payé pour une consultation
  getTotalPaidForConsultation(consultationId: string): number {
    const payments = this.getPaymentsByConsultation(consultationId);
    return payments.reduce((total, payment) => total + payment.amount, 0);
  },

  // Calculer le montant restant pour une consultation
  getRemainingAmountForConsultation(consultationId: string): number {
    const consultation = this.getAllConsultations().find(c => c.id === consultationId);
    if (!consultation || !consultation.cost) return 0;

    const totalPaid = this.getTotalPaidForConsultation(consultationId);
    return consultation.cost - totalPaid;
  },

  // Ajouter un paiement simple
  addPayment(consultationId: string, amount: number): boolean {
    const consultation = this.getAllConsultations().find(c => c.id === consultationId);
    if (!consultation) return false;

    const remaining = this.getRemainingAmountForConsultation(consultationId);
    if (amount > remaining) return false; // Ne pas dépasser le montant dû

    this.createPayment({
      patientId: consultation.patientId,
      consultationId,
      amount,
      method: 'CASH',
      date: new Date(),
      description: `Paiement pour consultation ${consultation.type}`
    });

    return true;
  },

  // Obtenir l'historique complet des paiements d'un patient
  getPatientPaymentHistory(patientId: string): Array<PaymentData & { consultationType?: string; consultationDate?: Date }> {
    const payments = this.getPaymentsByPatient(patientId);
    const consultations = this.getConsultationsByPatient(patientId);

    return payments.map(payment => {
      const consultation = consultations.find(c => c.id === payment.consultationId);
      return {
        ...payment,
        consultationType: consultation?.type,
        consultationDate: consultation?.date
      };
    }).sort((a, b) => b.date.getTime() - a.date.getTime());
  },

  // === GESTION DES RENDEZ-VOUS ===

  // Créer un rendez-vous
  createAppointment(data: Omit<AppointmentData, 'id' | 'createdAt' | 'updatedAt'>): AppointmentData {
    const appointments = this.getAllAppointments();
    const newAppointment: AppointmentData = {
      ...data,
      id: Date.now().toString(),
      status: data.status || 'SCHEDULED',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    appointments.push(newAppointment);
    localStorage.setItem('cabinet_appointments', JSON.stringify(appointments));

    console.log('Rendez-vous créé:', newAppointment);
    return newAppointment;
  },

  // Récupérer tous les rendez-vous
  getAllAppointments(): AppointmentData[] {
    const data = localStorage.getItem('cabinet_appointments');
    if (!data) return [];

    const appointments = JSON.parse(data);
    return appointments.map((a: any) => ({
      ...a,
      date: new Date(a.date),
      createdAt: new Date(a.createdAt),
      updatedAt: new Date(a.updatedAt)
    }));
  },

  // Récupérer les rendez-vous d'une date spécifique
  getAppointmentsByDate(date: Date): AppointmentData[] {
    const appointments = this.getAllAppointments();
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);

    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      appointmentDate.setHours(0, 0, 0, 0);
      return appointmentDate.getTime() === targetDate.getTime();
    }).sort((a, b) => a.time.localeCompare(b.time));
  },

  // Récupérer les rendez-vous d'une semaine
  getAppointmentsByWeek(startDate: Date): AppointmentData[] {
    const appointments = this.getAllAppointments();
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    end.setHours(23, 59, 59, 999);

    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      return appointmentDate >= start && appointmentDate <= end;
    }).sort((a, b) => {
      const dateCompare = new Date(a.date).getTime() - new Date(b.date).getTime();
      if (dateCompare === 0) {
        return a.time.localeCompare(b.time);
      }
      return dateCompare;
    });
  },

  // Récupérer les rendez-vous d'un patient
  getAppointmentsByPatient(patientId: string): AppointmentData[] {
    const appointments = this.getAllAppointments();
    return appointments.filter(a => a.patientId === patientId);
  },

  // Mettre à jour un rendez-vous
  updateAppointment(id: string, data: Partial<AppointmentData>): AppointmentData | null {
    const appointments = this.getAllAppointments();
    const index = appointments.findIndex(a => a.id === id);

    if (index === -1) return null;

    appointments[index] = {
      ...appointments[index],
      ...data,
      updatedAt: new Date()
    };

    localStorage.setItem('cabinet_appointments', JSON.stringify(appointments));

    console.log('Rendez-vous mis à jour:', appointments[index]);
    return appointments[index];
  },

  // Supprimer un rendez-vous
  deleteAppointment(id: string): boolean {
    const appointments = this.getAllAppointments();
    const index = appointments.findIndex(a => a.id === id);

    if (index === -1) return false;

    const deleted = appointments.splice(index, 1)[0];
    localStorage.setItem('cabinet_appointments', JSON.stringify(appointments));

    console.log('Rendez-vous supprimé:', deleted);
    return true;
  }
};

// Initialiser la base de données au chargement
database.init();