import { PrismaClient } from '../generated/prisma';

// Créer une instance Prisma pour le côté client (simulation)
// En production, ceci serait remplacé par des appels API REST
let prisma: PrismaClient;

// Simuler une base de données côté client avec localStorage
const DB_KEY = 'cabinet_patients';

export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Fonctions pour gérer la base de données locale
export const database = {
  // Initialiser avec des données par défaut
  init() {
    const existing = localStorage.getItem(DB_KEY);
    if (!existing) {
      const defaultPatients: PatientData[] = [
        {
          id: '1',
          firstName: 'Jean',
          lastName: 'Dupont',
          phone: '**********',
          email: '<EMAIL>',
          dateOfBirth: new Date('1980-05-15'),
          gender: 'HOMME',
          address: '123 Rue de la Paix',
          city: 'Paris',
          postalCode: '75001',
          profession: 'Ingénieur',
          emergencyContact: 'Sophie Dupont',
          emergencyPhone: '**********',
          medicalHistory: 'Aucun antécédent particulier',
          allergies: 'Aucune allergie connue',
          bloodType: 'A+',
          insuranceNumber: '1234567890123',
          insuranceProvider: 'Sécurité Sociale',
          notes: 'Patient régulier, très ponctuel',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          firstName: 'Marie',
          lastName: 'Martin',
          phone: '**********',
          email: '<EMAIL>',
          dateOfBirth: new Date('1975-12-03'),
          gender: 'FEMME',
          address: '456 Avenue des Champs',
          city: 'Paris',
          postalCode: '75008',
          profession: 'Professeure',
          emergencyContact: 'Paul Martin',
          emergencyPhone: '**********',
          medicalHistory: 'Hypertension artérielle',
          allergies: 'Allergie à la pénicilline',
          medications: 'Lisinopril 10mg',
          bloodType: 'B+',
          insuranceNumber: '2345678901234',
          insuranceProvider: 'Mutuelle Générale',
          notes: 'Préfère les rendez-vous le matin',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '3',
          firstName: 'Hocine',
          lastName: 'Boukerche Mansour',
          phone: '**********',
          email: '<EMAIL>',
          dateOfBirth: new Date('1945-04-01'),
          gender: 'HOMME',
          address: '123 Rue de la Paix',
          city: 'Alger',
          postalCode: '16000',
          profession: 'Retraité',
          emergencyContact: 'Fatima Boukerche',
          emergencyPhone: '**********',
          medicalHistory: 'Hypertension artérielle, Diabète type 2',
          allergies: 'Pénicilline',
          medications: 'Metformine 500mg, Lisinopril 10mg',
          bloodType: 'O+',
          insuranceNumber: '1234567890123',
          insuranceProvider: 'CNAS',
          notes: 'Patient coopératif, sensible',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      localStorage.setItem(DB_KEY, JSON.stringify(defaultPatients));
    }
  },

  // Récupérer tous les patients
  getAllPatients(): PatientData[] {
    const data = localStorage.getItem(DB_KEY);
    if (!data) return [];
    
    const patients = JSON.parse(data);
    // Convertir les dates string en objets Date
    return patients.map((p: any) => ({
      ...p,
      dateOfBirth: p.dateOfBirth ? new Date(p.dateOfBirth) : undefined,
      createdAt: new Date(p.createdAt),
      updatedAt: new Date(p.updatedAt)
    }));
  },

  // Récupérer un patient par ID
  getPatientById(id: string): PatientData | null {
    const patients = this.getAllPatients();
    return patients.find(p => p.id === id) || null;
  },

  // Créer un nouveau patient
  createPatient(data: Omit<PatientData, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>): PatientData {
    const patients = this.getAllPatients();
    const newPatient: PatientData = {
      ...data,
      id: Date.now().toString(),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    patients.push(newPatient);
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient créé et sauvegardé:', newPatient);
    return newPatient;
  },

  // Mettre à jour un patient
  updatePatient(id: string, data: Partial<PatientData>): PatientData | null {
    const patients = this.getAllPatients();
    const index = patients.findIndex(p => p.id === id);
    
    if (index === -1) return null;
    
    patients[index] = {
      ...patients[index],
      ...data,
      updatedAt: new Date()
    };
    
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient mis à jour:', patients[index]);
    return patients[index];
  },

  // Supprimer un patient
  deletePatient(id: string): boolean {
    const patients = this.getAllPatients();
    const index = patients.findIndex(p => p.id === id);
    
    if (index === -1) return false;
    
    const deleted = patients.splice(index, 1)[0];
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient supprimé:', deleted);
    return true;
  },

  // Rechercher des patients
  searchPatients(query: string): PatientData[] {
    const patients = this.getAllPatients();
    const lowerQuery = query.toLowerCase();
    
    return patients.filter(patient =>
      `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(lowerQuery) ||
      patient.phone.includes(query) ||
      patient.email?.toLowerCase().includes(lowerQuery)
    );
  }
};

// Initialiser la base de données au chargement
database.init();
