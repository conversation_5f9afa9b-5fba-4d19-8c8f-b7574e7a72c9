// Base de données locale utilisant localStorage
// En production, ceci serait remplacé par des appels API REST vers une vraie base de données

// Simuler une base de données côté client avec localStorage
const DB_KEY = 'cabinet_patients';
const CONSULTATIONS_KEY = 'cabinet_consultations';
const PAYMENTS_KEY = 'cabinet_payments';

export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConsultationData {
  id?: string;
  patientId: string;
  type: string;
  reason?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  cost?: number;
  nextAppointment?: Date;
  date: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PaymentData {
  id?: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: 'CASH' | 'CARD' | 'TRANSFER' | 'CHECK';
  date: Date;
  description?: string;
  createdAt?: Date;
}

export interface PaymentSummary {
  totalAmount: number;
  totalPaid: number;
  remainingAmount: number;
  paymentCount: number;
  isFullyPaid: boolean;
  paymentPercentage: number;
}

// Fonctions pour gérer la base de données locale
export const database = {
  // Initialiser avec des données par défaut


  // Récupérer tous les patients
  getAllPatients(): PatientData[] {
    const data = localStorage.getItem(DB_KEY);
    if (!data) return [];
    
    const patients = JSON.parse(data);
    // Convertir les dates string en objets Date
    return patients.map((p: any) => ({
      ...p,
      dateOfBirth: p.dateOfBirth ? new Date(p.dateOfBirth) : undefined,
      createdAt: new Date(p.createdAt),
      updatedAt: new Date(p.updatedAt)
    }));
  },

  // Récupérer un patient par ID
  getPatientById(id: string): PatientData | null {
    const patients = this.getAllPatients();
    return patients.find(p => p.id === id) || null;
  },

  // Créer un nouveau patient
  createPatient(data: Omit<PatientData, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>): PatientData {
    const patients = this.getAllPatients();
    const newPatient: PatientData = {
      ...data,
      id: Date.now().toString(),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    patients.push(newPatient);
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient créé et sauvegardé:', newPatient);
    return newPatient;
  },

  // Mettre à jour un patient
  updatePatient(id: string, data: Partial<PatientData>): PatientData | null {
    const patients = this.getAllPatients();
    const index = patients.findIndex(p => p.id === id);
    
    if (index === -1) return null;
    
    patients[index] = {
      ...patients[index],
      ...data,
      updatedAt: new Date()
    };
    
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient mis à jour:', patients[index]);
    return patients[index];
  },

  // Supprimer un patient
  deletePatient(id: string): boolean {
    const patients = this.getAllPatients();
    const index = patients.findIndex(p => p.id === id);
    
    if (index === -1) return false;
    
    const deleted = patients.splice(index, 1)[0];
    localStorage.setItem(DB_KEY, JSON.stringify(patients));
    
    console.log('Patient supprimé:', deleted);
    return true;
  },

  // Rechercher des patients
  searchPatients(query: string): PatientData[] {
    const patients = this.getAllPatients();
    const lowerQuery = query.toLowerCase();

    return patients.filter(patient =>
      `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(lowerQuery) ||
      patient.phone.includes(query) ||
      patient.email?.toLowerCase().includes(lowerQuery)
    );
  },

  // === CONSULTATIONS ===

  // Créer une consultation
  createConsultation(data: Omit<ConsultationData, 'id' | 'createdAt' | 'updatedAt'>, paymentData?: { amount: number; method: string }): ConsultationData {
    const consultations = this.getAllConsultations();
    const newConsultation: ConsultationData = {
      ...data,
      id: Date.now().toString(),
      date: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    consultations.push(newConsultation);
    localStorage.setItem(CONSULTATIONS_KEY, JSON.stringify(consultations));

    // Si un paiement est fourni, l'enregistrer aussi
    if (paymentData && paymentData.amount > 0) {
      this.createPayment({
        patientId: data.patientId,
        consultationId: newConsultation.id!,
        amount: paymentData.amount,
        method: paymentData.method as any,
        date: new Date(),
        description: `Paiement pour consultation ${data.type}`
      });
    }

    console.log('Consultation créée:', newConsultation);
    return newConsultation;
  },

  // Récupérer toutes les consultations
  getAllConsultations(): ConsultationData[] {
    const data = localStorage.getItem(CONSULTATIONS_KEY);
    if (!data) return [];

    const consultations = JSON.parse(data);
    return consultations.map((c: any) => ({
      ...c,
      date: new Date(c.date),
      nextAppointment: c.nextAppointment ? new Date(c.nextAppointment) : undefined,
      createdAt: new Date(c.createdAt),
      updatedAt: new Date(c.updatedAt)
    }));
  },

  // Récupérer les consultations d'un patient
  getConsultationsByPatient(patientId: string): ConsultationData[] {
    const consultations = this.getAllConsultations();
    return consultations.filter(c => c.patientId === patientId);
  },

  // === PAIEMENTS ===

  // Créer un paiement
  createPayment(data: Omit<PaymentData, 'id' | 'createdAt'>): PaymentData {
    const payments = this.getAllPayments();
    const newPayment: PaymentData = {
      ...data,
      id: Date.now().toString(),
      createdAt: new Date()
    };

    payments.push(newPayment);
    localStorage.setItem(PAYMENTS_KEY, JSON.stringify(payments));

    console.log('Paiement créé:', newPayment);
    return newPayment;
  },

  // Récupérer tous les paiements
  getAllPayments(): PaymentData[] {
    const data = localStorage.getItem(PAYMENTS_KEY);
    if (!data) return [];

    const payments = JSON.parse(data);
    return payments.map((p: any) => ({
      ...p,
      date: new Date(p.date),
      createdAt: new Date(p.createdAt)
    }));
  },

  // Récupérer les paiements d'un patient
  getPaymentsByPatient(patientId: string): PaymentData[] {
    const payments = this.getAllPayments();
    return payments.filter(p => p.patientId === patientId);
  },

  // Récupérer les paiements d'une consultation
  getPaymentsByConsultation(consultationId: string): PaymentData[] {
    const payments = this.getAllPayments();
    return payments.filter(p => p.consultationId === consultationId);
  },

  // === GESTION DES PAIEMENTS ÉCHELONNÉS ===

  // Calculer le résumé des paiements pour une consultation
  getPaymentSummary(consultationId: string): PaymentSummary {
    const consultation = this.getAllConsultations().find(c => c.id === consultationId);
    const payments = this.getPaymentsByConsultation(consultationId);

    const totalAmount = consultation?.cost || 0;
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = totalAmount - totalPaid;
    const paymentCount = payments.length;
    const isFullyPaid = remainingAmount <= 0;
    const paymentPercentage = totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0;

    return {
      totalAmount,
      totalPaid,
      remainingAmount,
      paymentCount,
      isFullyPaid,
      paymentPercentage
    };
  },

  // Ajouter un paiement avec validation
  addPaymentToConsultation(consultationId: string, amount: number, method: 'CASH' | 'CARD' | 'TRANSFER' | 'CHECK' = 'CASH'): { success: boolean; message: string; payment?: PaymentData } {
    const consultation = this.getAllConsultations().find(c => c.id === consultationId);

    if (!consultation) {
      return { success: false, message: 'Consultation non trouvée' };
    }

    const summary = this.getPaymentSummary(consultationId);

    if (amount <= 0) {
      return { success: false, message: 'Le montant doit être supérieur à 0' };
    }

    if (amount > summary.remainingAmount) {
      return {
        success: false,
        message: `Le montant dépasse le reste à payer (${summary.remainingAmount.toLocaleString()} DA)`
      };
    }

    const payment = this.createPayment({
      patientId: consultation.patientId,
      consultationId,
      amount,
      method,
      date: new Date(),
      description: `Paiement ${summary.paymentCount + 1} pour consultation ${consultation.type}`
    });

    return {
      success: true,
      message: `Paiement de ${amount.toLocaleString()} DA enregistré avec succès`,
      payment
    };
  },

  // Obtenir l'historique complet des paiements d'un patient
  getPatientPaymentHistory(patientId: string): Array<PaymentData & { consultationType?: string; consultationDate?: Date }> {
    const payments = this.getPaymentsByPatient(patientId);
    const consultations = this.getConsultationsByPatient(patientId);

    return payments.map(payment => {
      const consultation = consultations.find(c => c.id === payment.consultationId);
      return {
        ...payment,
        consultationType: consultation?.type,
        consultationDate: consultation?.date
      };
    }).sort((a, b) => b.date.getTime() - a.date.getTime());
  },

  // Obtenir le résumé financier d'un patient
  getPatientFinancialSummary(patientId: string): {
    totalConsultations: number;
    totalAmount: number;
    totalPaid: number;
    totalRemaining: number;
    fullyPaidConsultations: number;
    partiallyPaidConsultations: number;
    unpaidConsultations: number;
  } {
    const consultations = this.getConsultationsByPatient(patientId);

    let totalAmount = 0;
    let totalPaid = 0;
    let fullyPaidConsultations = 0;
    let partiallyPaidConsultations = 0;
    let unpaidConsultations = 0;

    consultations.forEach(consultation => {
      const cost = consultation.cost || 0;
      totalAmount += cost;

      if (consultation.id) {
        const summary = this.getPaymentSummary(consultation.id);
        totalPaid += summary.totalPaid;

        if (summary.isFullyPaid) {
          fullyPaidConsultations++;
        } else if (summary.totalPaid > 0) {
          partiallyPaidConsultations++;
        } else {
          unpaidConsultations++;
        }
      }
    });

    return {
      totalConsultations: consultations.length,
      totalAmount,
      totalPaid,
      totalRemaining: totalAmount - totalPaid,
      fullyPaidConsultations,
      partiallyPaidConsultations,
      unpaidConsultations
    };
  }
};


