// Base de données utilisant l'API REST avec fichier JSON sur le serveur
// Toutes les données sont stockées dans cabinet.json sur le serveur

const API_BASE_URL = 'http://localhost:3001/api';

// Types compatibles avec l'interface existante
export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConsultationData {
  id?: string;
  patientId: string;
  date: Date;
  type: string;
  reason?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  observations?: string;
  conclusion?: string;
  nextAppointment?: Date;
  cost?: number;
  paid?: boolean;
  privateNotes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PaymentData {
  id?: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: string;
  date: Date;
  description?: string;
  createdAt?: Date;
}

// Utilitaires pour les appels API
async function apiCall(endpoint: string, options: RequestInit = {}): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Erreur appel API ${endpoint}:`, error);
    throw error;
  }
}

// API Database class
class APIDatabase {
  // === PATIENTS ===
  async createPatient(patient: PatientData): Promise<PatientData> {
    const response = await apiCall('/patients', {
      method: 'POST',
      body: JSON.stringify(patient),
    });
    return response.patient;
  }

  async getAllPatients(): Promise<PatientData[]> {
    const response = await apiCall('/patients');
    return response.patients;
  }

  async getPatientById(id: string): Promise<PatientData | null> {
    try {
      const response = await apiCall(`/patients/${id}`);
      return response.patient;
    } catch (error: any) {
      if (error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  async updatePatient(id: string, patient: Partial<PatientData>): Promise<PatientData | null> {
    try {
      const response = await apiCall(`/patients/${id}`, {
        method: 'PUT',
        body: JSON.stringify(patient),
      });
      return response.patient;
    } catch (error: any) {
      if (error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  async deletePatient(id: string): Promise<boolean> {
    try {
      await apiCall(`/patients/${id}`, {
        method: 'DELETE',
      });
      return true;
    } catch (error: any) {
      if (error.message.includes('404')) {
        return false;
      }
      throw error;
    }
  }

  async searchPatients(query: string): Promise<PatientData[]> {
    const response = await apiCall(`/patients?search=${encodeURIComponent(query)}`);
    return response.patients;
  }

  // === RENDEZ-VOUS ===
  async createAppointment(appointment: AppointmentData): Promise<AppointmentData> {
    const appointmentData = {
      ...appointment,
      date: appointment.date instanceof Date ? appointment.date.toISOString().split('T')[0] : appointment.date,
    };
    
    const response = await apiCall('/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
    return response.appointment;
  }

  async getAllAppointments(): Promise<AppointmentData[]> {
    const response = await apiCall('/appointments');
    return response.appointments.map((apt: any) => ({
      ...apt,
      date: new Date(apt.date),
      isUrgent: Boolean(apt.isUrgent)
    }));
  }

  async getAppointmentsByPatient(patientId: string): Promise<AppointmentData[]> {
    const response = await apiCall(`/appointments?patientId=${patientId}`);
    return response.appointments.map((apt: any) => ({
      ...apt,
      date: new Date(apt.date),
      isUrgent: Boolean(apt.isUrgent)
    }));
  }

  async updateAppointment(id: string, appointment: Partial<AppointmentData>): Promise<AppointmentData | null> {
    try {
      const appointmentData = {
        ...appointment,
        date: appointment.date instanceof Date ? appointment.date.toISOString().split('T')[0] : appointment.date,
      };
      
      const response = await apiCall(`/appointments/${id}`, {
        method: 'PUT',
        body: JSON.stringify(appointmentData),
      });
      return {
        ...response.appointment,
        date: new Date(response.appointment.date),
        isUrgent: Boolean(response.appointment.isUrgent)
      };
    } catch (error: any) {
      if (error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  async deleteAppointment(id: string): Promise<boolean> {
    try {
      await apiCall(`/appointments/${id}`, {
        method: 'DELETE',
      });
      return true;
    } catch (error: any) {
      if (error.message.includes('404')) {
        return false;
      }
      throw error;
    }
  }

  // === CONSULTATIONS (placeholder) ===
  async createConsultation(consultation: ConsultationData): Promise<ConsultationData> {
    return consultation;
  }

  async getConsultationsByPatient(_patientId: string): Promise<ConsultationData[]> {
    return [];
  }

  // === PAIEMENTS (placeholder) ===
  async createPayment(payment: PaymentData): Promise<PaymentData> {
    return payment;
  }

  async getPaymentsByPatient(_patientId: string): Promise<PaymentData[]> {
    return [];
  }
}

// Instance singleton
const dbInstance = new APIDatabase();

// Interface compatible avec l'ancienne API
export const database = {
  // Patients
  createPatient: (patient: PatientData) => dbInstance.createPatient(patient),
  getAllPatients: () => dbInstance.getAllPatients(),
  getPatientById: (id: string) => dbInstance.getPatientById(id),
  updatePatient: (id: string, patient: Partial<PatientData>) => dbInstance.updatePatient(id, patient),
  deletePatient: (id: string) => dbInstance.deletePatient(id),
  searchPatients: (query: string) => dbInstance.searchPatients(query),

  // Rendez-vous
  createAppointment: (appointment: AppointmentData) => dbInstance.createAppointment(appointment),
  getAllAppointments: () => dbInstance.getAllAppointments(),
  getAppointmentsByPatient: (patientId: string) => dbInstance.getAppointmentsByPatient(patientId),
  updateAppointment: (id: string, appointment: Partial<AppointmentData>) => dbInstance.updateAppointment(id, appointment),
  deleteAppointment: (id: string) => dbInstance.deleteAppointment(id),

  // Consultations et paiements
  createConsultation: (consultation: ConsultationData) => dbInstance.createConsultation(consultation),
  getConsultationsByPatient: (patientId: string) => dbInstance.getConsultationsByPatient(patientId),
  createPayment: (payment: PaymentData) => dbInstance.createPayment(payment),
  getPaymentsByPatient: (patientId: string) => dbInstance.getPaymentsByPatient(patientId)
};

export default database;
