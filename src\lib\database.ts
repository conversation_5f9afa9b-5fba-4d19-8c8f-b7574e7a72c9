// Base de données locale utilisant IndexedDB pour la persistance
// Compatible navigateur et accessible sur le réseau via l'application

// Utilitaire pour générer des IDs uniques
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Types compatibles avec l'interface existante
export interface PatientData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  dateOfBirth?: Date;
  gender?: 'HOMME' | 'FEMME' | 'AUTRE';
  address?: string;
  city?: string;
  postalCode?: string;
  profession?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  medicalHistory?: string;
  allergies?: string;
  medications?: string;
  bloodType?: string;
  insuranceNumber?: string;
  insuranceProvider?: string;
  notes?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppointmentData {
  id?: string;
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  type: string;
  date: Date;
  time: string;
  duration: number;
  status: string;
  notes?: string;
  isUrgent?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConsultationData {
  id?: string;
  patientId: string;
  date: Date;
  type: string;
  reason?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  prescription?: string;
  observations?: string;
  conclusion?: string;
  nextAppointment?: Date;
  cost?: number;
  paid?: boolean;
  privateNotes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PaymentData {
  id?: string;
  patientId: string;
  consultationId?: string;
  amount: number;
  method: string;
  date: Date;
  description?: string;
  createdAt?: Date;
}

// Base de données IndexedDB
class IndexedDBDatabase {
  private dbName = 'CabinetDentaire';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Store pour les patients
        if (!db.objectStoreNames.contains('patients')) {
          const patientsStore = db.createObjectStore('patients', { keyPath: 'id' });
          patientsStore.createIndex('name', ['lastName', 'firstName']);
          patientsStore.createIndex('phone', 'phone');
          patientsStore.createIndex('email', 'email');
        }

        // Store pour les rendez-vous
        if (!db.objectStoreNames.contains('appointments')) {
          const appointmentsStore = db.createObjectStore('appointments', { keyPath: 'id' });
          appointmentsStore.createIndex('patientId', 'patientId');
          appointmentsStore.createIndex('date', 'date');
        }

        // Store pour les consultations
        if (!db.objectStoreNames.contains('consultations')) {
          const consultationsStore = db.createObjectStore('consultations', { keyPath: 'id' });
          consultationsStore.createIndex('patientId', 'patientId');
          consultationsStore.createIndex('date', 'date');
        }

        // Store pour les paiements
        if (!db.objectStoreNames.contains('payments')) {
          const paymentsStore = db.createObjectStore('payments', { keyPath: 'id' });
          paymentsStore.createIndex('patientId', 'patientId');
          paymentsStore.createIndex('date', 'date');
        }
      };
    });
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // === PATIENTS ===
  async createPatient(patient: PatientData): Promise<PatientData> {
    const db = await this.ensureDB();
    const now = new Date();
    const newPatient: PatientData = {
      ...patient,
      id: generateId(),
      isActive: true,
      createdAt: now,
      updatedAt: now
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['patients'], 'readwrite');
      const store = transaction.objectStore('patients');
      const request = store.add(newPatient);

      request.onsuccess = () => {
        console.log('✅ Patient créé:', newPatient.firstName, newPatient.lastName);
        resolve(newPatient);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async getAllPatients(): Promise<PatientData[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['patients'], 'readonly');
      const store = transaction.objectStore('patients');
      const request = store.getAll();

      request.onsuccess = () => {
        const patients = request.result
          .filter((p: PatientData) => p.isActive !== false)
          .sort((a: PatientData, b: PatientData) => {
            const aName = `${a.lastName} ${a.firstName}`.toLowerCase();
            const bName = `${b.lastName} ${b.firstName}`.toLowerCase();
            return aName.localeCompare(bName);
          });
        resolve(patients);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async getPatientById(id: string): Promise<PatientData | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['patients'], 'readonly');
      const store = transaction.objectStore('patients');
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async updatePatient(id: string, patientUpdate: Partial<PatientData>): Promise<PatientData | null> {
    const db = await this.ensureDB();
    const existingPatient = await this.getPatientById(id);
    
    if (!existingPatient) return null;

    const updatedPatient: PatientData = {
      ...existingPatient,
      ...patientUpdate,
      id,
      updatedAt: new Date()
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['patients'], 'readwrite');
      const store = transaction.objectStore('patients');
      const request = store.put(updatedPatient);

      request.onsuccess = () => {
        console.log('✅ Patient mis à jour:', updatedPatient.firstName, updatedPatient.lastName);
        resolve(updatedPatient);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async deletePatient(id: string): Promise<boolean> {
    const db = await this.ensureDB();
    const patient = await this.getPatientById(id);
    
    if (!patient) return false;

    const updatedPatient = { ...patient, isActive: false, updatedAt: new Date() };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['patients'], 'readwrite');
      const store = transaction.objectStore('patients');
      const request = store.put(updatedPatient);

      request.onsuccess = () => {
        console.log('✅ Patient désactivé:', id);
        resolve(true);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async searchPatients(query: string): Promise<PatientData[]> {
    const allPatients = await this.getAllPatients();
    const searchTerm = query.toLowerCase();

    return allPatients.filter(patient => 
      patient.firstName.toLowerCase().includes(searchTerm) ||
      patient.lastName.toLowerCase().includes(searchTerm) ||
      patient.phone?.includes(searchTerm) ||
      patient.email?.toLowerCase().includes(searchTerm)
    );
  }

  // === RENDEZ-VOUS ===
  async createAppointment(appointment: AppointmentData): Promise<AppointmentData> {
    const db = await this.ensureDB();
    const now = new Date();
    const newAppointment: AppointmentData = {
      ...appointment,
      id: generateId(),
      createdAt: now,
      updatedAt: now
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appointments'], 'readwrite');
      const store = transaction.objectStore('appointments');
      const request = store.add(newAppointment);

      request.onsuccess = () => {
        console.log('✅ Rendez-vous créé:', newAppointment.date, newAppointment.time);
        resolve(newAppointment);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async getAllAppointments(): Promise<AppointmentData[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appointments'], 'readonly');
      const store = transaction.objectStore('appointments');
      const request = store.getAll();

      request.onsuccess = () => {
        const appointments = request.result.sort((a: AppointmentData, b: AppointmentData) => {
          const dateA = new Date(a.date).getTime();
          const dateB = new Date(b.date).getTime();
          if (dateA !== dateB) return dateB - dateA;
          return b.time.localeCompare(a.time);
        });
        resolve(appointments);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async getAppointmentsByPatient(patientId: string): Promise<AppointmentData[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appointments'], 'readonly');
      const store = transaction.objectStore('appointments');
      const index = store.index('patientId');
      const request = index.getAll(patientId);

      request.onsuccess = () => {
        const appointments = request.result.sort((a: AppointmentData, b: AppointmentData) => {
          const dateA = new Date(a.date).getTime();
          const dateB = new Date(b.date).getTime();
          if (dateA !== dateB) return dateB - dateA;
          return b.time.localeCompare(a.time);
        });
        resolve(appointments);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async updateAppointment(id: string, appointmentUpdate: Partial<AppointmentData>): Promise<AppointmentData | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appointments'], 'readwrite');
      const store = transaction.objectStore('appointments');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const existingAppointment = getRequest.result;
        if (!existingAppointment) {
          resolve(null);
          return;
        }

        const updatedAppointment: AppointmentData = {
          ...existingAppointment,
          ...appointmentUpdate,
          id,
          updatedAt: new Date()
        };

        const putRequest = store.put(updatedAppointment);
        putRequest.onsuccess = () => {
          console.log('✅ Rendez-vous mis à jour:', updatedAppointment.date, updatedAppointment.time);
          resolve(updatedAppointment);
        };
        putRequest.onerror = () => reject(putRequest.error);
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  async deleteAppointment(id: string): Promise<boolean> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appointments'], 'readwrite');
      const store = transaction.objectStore('appointments');
      const request = store.delete(id);

      request.onsuccess = () => {
        console.log('✅ Rendez-vous supprimé:', id);
        resolve(true);
      };
      request.onerror = () => reject(request.error);
    });
  }
}

// Instance singleton
const dbInstance = new IndexedDBDatabase();

// Interface compatible avec l'ancienne API
export const database = {
  // Patients
  createPatient: (patient: PatientData) => dbInstance.createPatient(patient),
  getAllPatients: () => dbInstance.getAllPatients(),
  getPatientById: (id: string) => dbInstance.getPatientById(id),
  updatePatient: (id: string, patient: Partial<PatientData>) => dbInstance.updatePatient(id, patient),
  deletePatient: (id: string) => dbInstance.deletePatient(id),
  searchPatients: (query: string) => dbInstance.searchPatients(query),

  // Rendez-vous
  createAppointment: (appointment: AppointmentData) => dbInstance.createAppointment(appointment),
  getAllAppointments: () => dbInstance.getAllAppointments(),
  getAppointmentsByPatient: (patientId: string) => dbInstance.getAppointmentsByPatient(patientId),
  updateAppointment: (id: string, appointment: Partial<AppointmentData>) => dbInstance.updateAppointment(id, appointment),
  deleteAppointment: (id: string) => dbInstance.deleteAppointment(id),

  // Consultations et paiements (à implémenter si nécessaire)
  createConsultation: async (consultation: ConsultationData) => consultation,
  getConsultationsByPatient: async (patientId: string) => [],
  createPayment: async (payment: PaymentData) => payment,
  getPaymentsByPatient: async (patientId: string) => []
};

export default database;
