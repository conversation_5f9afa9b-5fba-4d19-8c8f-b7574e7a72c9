const { app, BrowserWindow, Menu, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.js
// │
process.env.APP_ROOT = path.join(__dirname, '..');

const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'];
const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron');
const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist');

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST;

let win;

function createWindow() {
  win = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString());
  });

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
    win.webContents.openDevTools();
  } else {
    win.loadFile(path.join(RENDERER_DIST, 'index.html'));
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
    win = null;
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.whenReady().then(() => {
  createWindow();

  // Create application menu
  const template = [
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Sauvegarder la base de données',
          click: async () => {
            const result = await dialog.showSaveDialog(win, {
              title: 'Sauvegarder la base de données',
              defaultPath: 'cabinet-dentaire-backup.db',
              filters: [
                { name: 'Base de données', extensions: ['db', 'sqlite'] }
              ]
            });

            if (!result.canceled && result.filePath) {
              try {
                const dbPath = path.join(process.env.APP_ROOT, 'prisma', 'cabinet.sqlite');
                fs.copyFileSync(dbPath, result.filePath);
                dialog.showMessageBox(win, {
                  type: 'info',
                  title: 'Sauvegarde réussie',
                  message: 'La base de données a été sauvegardée avec succès.'
                });
              } catch (error) {
                dialog.showErrorBox('Erreur de sauvegarde', 'Impossible de sauvegarder la base de données.');
              }
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Quitter',
          accelerator: 'CmdOrCtrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Aide',
      submenu: [
        {
          label: 'À propos',
          click: () => {
            dialog.showMessageBox(win, {
              type: 'info',
              title: 'À propos',
              message: 'Cabinet Dentaire v1.0.0',
              detail: 'Application de gestion pour cabinet dentaire'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
    win = null;
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.whenReady().then(() => {
  createWindow();
  
  // Create application menu
  const template = [
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Sauvegarder la base de données',
          click: async () => {
            const result = await dialog.showSaveDialog(win, {
              title: 'Sauvegarder la base de données',
              defaultPath: 'cabinet-dentaire-backup.db',
              filters: [
                { name: 'Base de données', extensions: ['db', 'sqlite'] }
              ]
            });
            
            if (!result.canceled && result.filePath) {
              try {
                const dbPath = path.join(process.env.APP_ROOT, 'prisma', 'cabinet.sqlite');
                fs.copyFileSync(dbPath, result.filePath);
                dialog.showMessageBox(win, {
                  type: 'info',
                  title: 'Sauvegarde réussie',
                  message: 'La base de données a été sauvegardée avec succès.'
                });
              } catch (error) {
                dialog.showErrorBox('Erreur de sauvegarde', 'Impossible de sauvegarder la base de données.');
              }
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Quitter',
          accelerator: 'CmdOrCtrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Aide',
      submenu: [
        {
          label: 'À propos',
          click: () => {
            dialog.showMessageBox(win, {
              type: 'info',
              title: 'À propos',
              message: 'Cabinet Dentaire v1.0.0',
              detail: 'Application de gestion pour cabinet dentaire'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
});
