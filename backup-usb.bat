@echo off
echo.
echo ========================================
echo   SAUVEGARDE DIRECTE SUR USB
echo ========================================
echo.

set SOURCE_FILE=cabinet.json
set DATE_TIME=%date:~6,4%-%date:~3,2%-%date:~0,2%_%time:~0,2%h%time:~3,2%m
set DATE_TIME=%DATE_TIME: =0%

echo Verification du fichier source...
if not exist "%SOURCE_FILE%" (
    echo ERREUR: Le fichier %SOURCE_FILE% n'existe pas !
    pause
    exit /b 1
)

echo Fichier trouve: %SOURCE_FILE%
echo.

echo Detection des cles USB...
echo.

:: Detecter les cles USB (lecteurs amovibles)
for /f "tokens=1" %%d in ('wmic logicaldisk where "drivetype=2" get deviceid /value ^| find "="') do (
    set %%d
    call :backup_to_drive
)

echo.
echo Recherche terminee.
echo.
pause
goto :eof

:backup_to_drive
if defined DeviceID (
    set USB_DRIVE=%DeviceID%
    echo Cle USB detectee: %USB_DRIVE%
    
    :: Creer le dossier Cabinet sur l'USB
    set USB_FOLDER=%USB_DRIVE%\Cabinet_Dentaire
    if not exist "%USB_FOLDER%" (
        mkdir "%USB_FOLDER%"
        echo Dossier cree: %USB_FOLDER%
    )
    
    :: Copier le fichier avec horodatage
    set USB_FILE=%USB_FOLDER%\cabinet_backup_%DATE_TIME%.json
    copy "%SOURCE_FILE%" "%USB_FILE%"
    
    if errorlevel 1 (
        echo ERREUR lors de la copie sur %USB_DRIVE%
    ) else (
        echo ✅ SAUVEGARDE REUSSIE sur %USB_DRIVE%
        echo Fichier: %USB_FILE%
        
        :: Copier aussi la version actuelle
        copy "%SOURCE_FILE%" "%USB_FOLDER%\cabinet_actuel.json"
        echo Copie actuelle: %USB_FOLDER%\cabinet_actuel.json
    )
    echo.
    
    set DeviceID=
)
goto :eof
