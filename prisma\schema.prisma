// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      String   @default("ADMIN") // ADMIN, ASSISTANT
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Patient {
  id              String   @id @default(cuid())
  firstName       String
  lastName        String
  phone           String   @unique
  email           String?
  dateOfBirth     DateTime?
  gender          String?  // Homme, Femme, Autre
  address         String?
  city            String?
  postalCode      String?
  profession      String?
  emergencyContact String?
  emergencyPhone  String?
  medicalHistory  String?  // Antécédents médicaux
  allergies       String?
  medications     String?  // Médicaments actuels
  bloodType       String?  // Groupe sanguin
  insuranceNumber String?  // Numéro de sécurité sociale
  insuranceProvider String? // Mutuelle
  notes           String?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  appointments    Appointment[]
  consultations   Consultation[]
  treatments      Treatment[]
  payments        Payment[]
  radiotherapyInfo RadiotherapyInfo[]
  doctorNotes     DoctorNote[]

  @@map("patients")
}

model Appointment {
  id          String   @id @default(cuid())
  patientId   String
  date        DateTime
  time        String
  duration    Int      @default(30) // en minutes
  type        String   // CONSULTATION, CONTROLE, URGENCE, etc.
  status      String   @default("SCHEDULED") // SCHEDULED, COMPLETED, CANCELLED, NO_SHOW
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  patient     Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("appointments")
}

model Consultation {
  id          String   @id @default(cuid())
  patientId   String
  date        DateTime @default(now())
  type        String   // CONSULTATION, CONTROLE, URGENCE, SPECIALISTE
  reason      String?  // Motif de consultation
  symptoms    String?  // Symptômes
  diagnosis   String?  // Diagnostic
  treatment   String?  // Traitement effectué
  prescription String? // Ordonnance
  observations String? // Observations du médecin
  conclusion  String?  // Conclusion
  nextAppointment DateTime? // Prochain RDV recommandé
  cost        Float?
  paid        Boolean  @default(false)
  privateNotes String?  // Notes privées du médecin
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  patient     Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)
  treatments  Treatment[]
  doctorNotes DoctorNote[]

  @@map("consultations")
}

model Treatment {
  id            String   @id @default(cuid())
  patientId     String
  consultationId String?
  name          String   // Nom du traitement
  type          String   // DETARTRAGE, OBTURATION, EXTRACTION, IMPLANT, COURONNE, etc.
  tooth         String?  // Dent concernée (ex: 16, 21, etc.)
  description   String?  // Description détaillée
  status        String   @default("PLANNED") // PLANNED, IN_PROGRESS, COMPLETED, CANCELLED
  startDate     DateTime?
  endDate       DateTime?
  cost          Float?
  sessions      Int      @default(1) // Nombre de séances
  currentSession Int     @default(0) // Séance actuelle
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  patient       Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consultation  Consultation? @relation(fields: [consultationId], references: [id])
  payments      Payment[]

  @@map("treatments")
}

model Payment {
  id          String   @id @default(cuid())
  patientId   String
  treatmentId String?
  amount      Float
  method      String   // CASH, CARD, TRANSFER, CHECK, INSURANCE
  status      String   @default("PENDING") // PENDING, COMPLETED, CANCELLED, REFUNDED
  description String?
  invoiceNumber String?
  date        DateTime @default(now())
  dueDate     DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  patient     Patient   @relation(fields: [patientId], references: [id], onDelete: Cascade)
  treatment   Treatment? @relation(fields: [treatmentId], references: [id])

  @@map("payments")
}

model RadiotherapyInfo {
  id              String   @id @default(cuid())
  patientId       String
  fractionation   Int      // Fractionnement (ex: 2 Gy/séance)
  prescribedDose  Int      // Dose prescrite (ex: 60 Gy)
  rayType         String   // Type de rayonnement (ex: Photons (X))
  sessions        Int      // Nombre de séances
  completedSessions Int    @default(0)
  startDate       DateTime?
  endDate         DateTime?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  patient         Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("radiotherapy_info")
}

model DoctorNote {
  id              String   @id @default(cuid())
  patientId       String
  consultationId  String?
  doctorName      String
  date            DateTime @default(now())
  observations    String?
  conclusion      String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  patient         Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consultation    Consultation? @relation(fields: [consultationId], references: [id])

  @@map("doctor_notes")
}
