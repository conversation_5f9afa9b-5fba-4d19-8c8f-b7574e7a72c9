// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      String   @default("ADMIN") // ADMIN, ASSISTANT
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Patient {
  id              String   @id @default(cuid())
  firstName       String
  lastName        String
  phone           String   @unique
  email           String?
  dateOfBirth     DateTime?
  address         String?
  medicalHistory  String?  // Antécédents médicaux
  allergies       String?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  appointments    Appointment[]
  consultations   Consultation[]

  @@map("patients")
}

model Appointment {
  id          String   @id @default(cuid())
  patientId   String
  date        DateTime
  time        String
  duration    Int      @default(30) // en minutes
  type        String   // CONSULTATION, CONTROLE, URGENCE, etc.
  status      String   @default("SCHEDULED") // SCHEDULED, COMPLETED, CANCELLED, NO_SHOW
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  patient     Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("appointments")
}

model Consultation {
  id          String   @id @default(cuid())
  patientId   String
  date        DateTime @default(now())
  diagnosis   String?
  treatment   String?
  prescription String?
  notes       String?
  cost        Float?
  paid        Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  patient     Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("consultations")
}
